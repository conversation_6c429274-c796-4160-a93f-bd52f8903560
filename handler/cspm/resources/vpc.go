package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchVPCRequest struct {
	Keyword  string `json:"keyword"`
	IP       string `json:"ip"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchVPCResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Provider   string `json:"provider"`
	Name       string `json:"name"`
	CIDR       string `json:"cidr"`
	CIDRv6     string `json:"cidr_v6"`
	SubnetNum  int    `json:"subnet_num"`
	IsDefault  bool   `json:"is_default"`
}

func SearchVPC(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchVPCRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "vpc"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.IP != "" {
		filter["$expr"] = bson.M{
			"$function": bson.M{
				"body": funcBody,
				"args": bson.A{"$cidr", req.IP},
				"lang": "js",
			},
		}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchVPCResponseItem]{}
	resp.Items = []SearchVPCResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.VPC{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchVPCResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Provider:   element.Provider,
			Name:       element.Name,
			CIDR:       element.CIDR,
			CIDRv6:     element.CIDRv6,
			SubnetNum:  len(element.SubnetIDList),
			IsDefault:  element.IsDefault,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}

var funcBody = `
function(cidr, ip) {
	const ipToNumber = function(ip) {
		return ip.split('.').map(function(octet) { return parseInt(octet, 10); }).reduce(function(acc, octet) { return (acc << 8) + octet; }, 0);
	};
	const cidrToRange = function(cidr) {
		let parts = cidr.split('/');
		let ip = parts[0];
		let prefixLength = parseInt(parts[1], 10);
		
		let ipNum = ipToNumber(ip);
		let mask = ~((1 << (32 - prefixLength)) - 1);
		let network = ipNum & mask;
		let broadcast = network + ~mask;
		return [network, broadcast];
	};
	let [network, broadcast] = cidrToRange(cidr);
	let searchIPNum = ipToNumber(ip);
	return searchIPNum >= network && searchIPNum <= broadcast;
}
`
