package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchListenerRequest struct {
	Keyword string `json:"keyword"`
	Kind    string `json:"kind"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchListenerResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Provider   string `json:"provider"`
	Name       string `json:"name"`
	Kind       string `json:"kind"`
}

type Listener struct {
	model.ObjectMeta `bson:",inline"`
}

func SearchListener(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON><PERSON><PERSON>())
		return
	}
	req := &SearchListenerRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}

	filter := bson.M{}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.Kind != "" {
		filter["kind"] = req.Kind
	} else {
		filter["kind"] = bson.M{"$in": []string{"lb-listener"}}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchListenerResponseItem]{}
	resp.Items = []SearchListenerResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []Listener{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		item := SearchListenerResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Name:       element.Name,
			Provider:   element.Provider,
			Kind:       element.Kind,
		}
		if item.Name == "" {
			item.Name = element.Description
		}
		resp.Items = append(resp.Items, item)
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
