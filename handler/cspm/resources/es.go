package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchESRequest struct {
	Keyword             string `json:"keyword"`
	PublicAllowed       *bool  `json:"public_allowed"`
	KibanaPublicAllowed *bool  `json:"kibana_public_allowed"`
	IsGlobal            *bool  `json:"is_global"`
}
type SearchESResponseItem struct {
	Uid                   string `json:"uid"`
	UpdateTime            int64  `json:"update_time"`
	Provider              string `json:"provider"`
	Name                  string `json:"name"`
	EngineVersion         string `json:"engine_version"`
	PrivateEndpoint       string `json:"private_endpoint"`
	PublicEndpoint        string `json:"public_endpoint"`
	KibanaPrivateEndpoint string `json:"kibana_private_endpoint"`
	KibanaPublicEndpoint  string `json:"kibana_public_endpoint"`
	PublicAllowed         bool   `json:"public_allowed"`
	KibanaPublicAllowed   bool   `json:"kibana_public_allowed"`
}

func SearchES(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchESRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "es"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.PublicAllowed != nil {
		filter["public_allowed"] = req.PublicAllowed
	}
	if req.KibanaPublicAllowed != nil {
		filter["kibana_public_allowed"] = req.KibanaPublicAllowed
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchESResponseItem]{}
	resp.Items = []SearchESResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.ES{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchESResponseItem{
			Uid:                   element.UID,
			UpdateTime:            element.UpdateTime,
			Provider:              element.Provider,
			Name:                  element.Name,
			EngineVersion:         element.EngineVersion,
			PrivateEndpoint:       element.PrivateEndpoint,
			PublicEndpoint:        element.PublicEndpoint,
			PublicAllowed:         element.PublicAllowed,
			KibanaPrivateEndpoint: element.KibanaPrivateEndpoint,
			KibanaPublicEndpoint:  element.KibanaPublicEndpoint,
			KibanaPublicAllowed:   element.KibanaPublicAllowed,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
