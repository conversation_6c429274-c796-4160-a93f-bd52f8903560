package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchSubnetRequest struct {
	Keyword  string `json:"keyword"`
	IP       string `json:"ip"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchSubnetResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Provider   string `json:"provider"`
	Name       string `json:"name"`
	AclNum     int    `json:"acl_num"`
	CIDR       string `json:"cidr"`
	CIDRv6     string `json:"cidr_v6"`
	IsDefault  bool   `json:"is_default"`
}

func SearchSubnet(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchSubnetRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "subnet"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.IP != "" {
		filter["$expr"] = bson.M{
			"$function": bson.M{
				"body": funcBody,
				"args": bson.A{"$cidr", req.IP},
				"lang": "js",
			},
		}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchSubnetResponseItem]{}
	resp.Items = []SearchSubnetResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.Subnet{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchSubnetResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Provider:   element.Provider,
			Name:       element.Name,
			AclNum:     len(element.ACL),
			CIDR:       element.CIDR,
			CIDRv6:     element.CIDRv6,
			IsDefault:  element.IsDefault,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
