package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchBucketRequest struct {
	Keyword        string `json:"keyword"`
	ACL            string `json:"acl"`
	Encryption     string `json:"encryption"`
	LoggingEnabled *bool  `json:"logging_enabled"`
	IsGlobal       *bool  `json:"is_global"`
}

type SearchBucketResponseItem struct {
	Uid            string   `json:"uid"`
	UpdateTime     int64    `json:"update_time"`
	Provider       string   `json:"provider"`
	Name           string   `json:"name"`
	ACL            []string `json:"acl"`
	Encryption     string   `json:"encryption"`
	LoggingEnabled bool     `json:"logging_enabled"`
}

func SearchBucket(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchBucketRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "bucket"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.ACL != "" {
		filter["acl"] = req.ACL
	}
	if req.Encryption != "" {
		filter["encryption.algorithm"] = req.Encryption
	}
	if req.LoggingEnabled != nil {
		filter["logging.enabled"] = *req.LoggingEnabled
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchBucketResponseItem]{}
	resp.Items = []SearchBucketResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.Bucket{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		item := SearchBucketResponseItem{
			Uid:            element.UID,
			UpdateTime:     element.UpdateTime,
			Provider:       element.Provider,
			Name:           element.Name,
			ACL:            element.ACL,
			Encryption:     element.Encryption.Algorithm,
			LoggingEnabled: element.Logging.Enabled,
		}
		if item.Encryption == "" {
			item.Encryption = "未启用"
		}
		resp.Items = append(resp.Items, item)
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
