package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchMQRequest struct {
	Keyword         string   `json:"keyword"`
	Authentications []string `json:"authentications"`
	IsGlobal        *bool    `json:"is_global"`
}
type SearchMQResponseItem struct {
	Uid             string   `json:"uid"`
	UpdateTime      int64    `json:"update_time"`
	Provider        string   `json:"provider"`
	Name            string   `json:"name"`
	Class           string   `json:"class"`
	Status          string   `json:"status"`
	Authentications []string `json:"authentications"`
}

func SearchMQ(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>rror())
		return
	}
	req := &SearchMQRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>rror())
		return
	}
	filter := bson.M{"kind": "mq"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if len(req.Authentications) > 0 {
		filter["authentications"] = bson.M{"$all": req.Authentications}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchMQResponseItem]{}
	resp.Items = []SearchMQResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.MQ{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchMQResponseItem{
			Uid:             element.UID,
			UpdateTime:      element.UpdateTime,
			Provider:        element.Provider,
			Name:            element.Name,
			Class:           element.Class,
			Status:          element.Status,
			Authentications: element.Authentications,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
