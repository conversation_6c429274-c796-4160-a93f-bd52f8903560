package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchLBRequest struct {
	Keyword  string `json:"keyword"`
	IP       string `json:"ip"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchLBResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Name       string `json:"name"`
	Provider   string `json:"provider"`
	Status     string `json:"status"`
	Class      string `json:"class"`
}

func SearchLB(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchLBRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>rror())
		return
	}
	filter := bson.D{{Key: "kind", Value: "lb"}}
	if req.Keyword != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}})
	}
	if req.IP != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"private_ip_list": req.IP},
			{"public_ip_list": req.IP},
		}})
	}
	if req.IsGlobal != nil {
		filter = append(filter, bson.E{Key: "is_global", Value: *req.IsGlobal})
	}
	resp := handler.PagnationResponseData[SearchLBResponseItem]{}
	resp.Items = []SearchLBResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.LB{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchLBResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Name:       element.Name,
			Provider:   element.Provider,
			Status:     element.Status,
			Class:      element.Class,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
