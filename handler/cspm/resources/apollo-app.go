package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchApolloAppRequest struct {
	Keyword  string `json:"keyword"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchApolloAppResponseItem struct {
	Uid           string `json:"uid"`
	UpdateTime    int64  `json:"update_time"`
	Provider      string `json:"provider"`
	Name          string `json:"name"`
	OwnerName     string `json:"owner_name"`
	EnabledEnvNum int    `json:"enabled_env_num"`
}

func SearchApolloApp(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON><PERSON><PERSON>())
		return
	}
	req := &SearchApolloAppRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "apollo-app"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchApolloAppResponseItem]{}
	resp.Items = []SearchApolloAppResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.ApolloApp{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchApolloAppResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Provider:   element.Provider,
			Name:       element.Name,
			OwnerName:  element.OwnerName,
			EnabledEnvNum: len(lo.Uniq(lo.FilterMap(element.Envs, func(item model.ApolloAppEnv, _ int) (string, bool) {
				return item.EnvName, item.ConfigExists
			}))),
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
