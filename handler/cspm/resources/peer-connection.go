package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchPCRequest struct {
	Keyword  string `json:"keyword"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchPCResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Provider   string `json:"provider"`
	Name       string `json:"name"`
	Status     string `json:"status"`
	VPCNum     int    `json:"vpc_num"`
}

func SearchPeerConnection(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchPCRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>rror())
		return
	}
	filter := bson.M{"kind": "peer-connection"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchPCResponseItem]{}
	resp.Items = []SearchPCResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.PeerConnection{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchPCResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Provider:   element.Provider,
			Name:       element.Name,
			Status:     element.Status,
			VPCNum:     len(element.VpcIdList),
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
