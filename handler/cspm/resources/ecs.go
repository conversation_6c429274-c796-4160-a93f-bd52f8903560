package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"fmt"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchECSRequest struct {
	Keyword      string `json:"keyword"`
	Class        string `json:"class" binding:"oneof=cvm edge ''"`
	IP           string `json:"ip"`
	HidsDeployed *bool  `json:"hids_deployed"`
	IsGlobal     *bool  `json:"is_global"`
}
type SearchECSResponseItem struct {
	Uid              string `json:"uid"`
	UpdateTime       int64  `json:"update_time"`
	Name             string `json:"name"`
	Class            string `json:"class"`
	Provider         string `json:"provider"`
	PrimaryPrivateIP string `json:"primary_private_ip"`
	PrimaryPublicIP  string `json:"primary_public_ip"`
	Status           string `json:"status"`
	Hostname         string `json:"hostname"`
}

func SearchECS(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchECSRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.D{{Key: "kind", Value: "ecs"}}
	if req.Keyword != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}})
	}
	if req.Class != "" {
		filter = append(filter, bson.E{Key: "class", Value: req.Class})
	}
	if req.IP != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"private_ip_list": primitive.Regex{Pattern: fmt.Sprintf("^%v", regexp.QuoteMeta(req.IP)), Options: "i"}},
			{"public_ip_list": primitive.Regex{Pattern: fmt.Sprintf("^%v", regexp.QuoteMeta(req.IP)), Options: "i"}},
		}})
	}
	if req.HidsDeployed != nil {
		filter = append(filter, bson.E{Key: "hids_info", Value: bson.M{"$exists": *req.HidsDeployed}})
	}
	if req.IsGlobal != nil {
		filter = append(filter, bson.E{Key: "is_global", Value: *req.IsGlobal})
	}
	resp := handler.PagnationResponseData[SearchECSResponseItem]{}
	resp.Items = []SearchECSResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.ECS{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchECSResponseItem{
			Uid:              element.UID,
			UpdateTime:       element.UpdateTime,
			Name:             element.Name,
			Class:            element.Class,
			Provider:         element.Provider,
			Status:           element.Status,
			PrimaryPrivateIP: element.PrimaryPrivateIP,
			PrimaryPublicIP:  element.PrimaryPublicIP,
			Hostname:         element.Hostname,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
