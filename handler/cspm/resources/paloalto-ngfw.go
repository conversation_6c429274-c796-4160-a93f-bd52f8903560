package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchPaloAltoRequest struct {
	Keyword              string `json:"keyword"`
	InternetRulesUpdated *bool  `json:"internet_rules_updated"`
	Region               string `json:"region"`
	IsGlobal             *bool  `json:"is_global"`
}
type SearchPaloAltoResponseItem struct {
	Uid                  string `json:"uid"`
	UpdateTime           int64  `json:"update_time"`
	Provider             string `json:"provider"`
	Name                 string `json:"name"`
	Version              string `json:"version"`
	InternetRulesUpdated bool   `json:"internet_rules_updated"`
	Region               string `json:"region"`
}

func SearchPaloAltoFirewall(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchPaloAltoRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "paloalto-ngfw"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.InternetRulesUpdated != nil {
		filter["internet_rules_updated"] = *req.InternetRulesUpdated
	}
	if req.Region != "" {
		filter["region"] = req.Region
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchPaloAltoResponseItem]{}
	resp.Items = []SearchPaloAltoResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.PaloaltoFirewall{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchPaloAltoResponseItem{
			Uid:                  element.UID,
			UpdateTime:           element.UpdateTime,
			Provider:             element.Provider,
			Name:                 element.Name,
			Version:              element.Version,
			InternetRulesUpdated: element.InternetRulesUpdated,
			Region:               element.Region,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}

func GetPaloAltoRegionOptions(ctx *gin.Context) {
	filter := bson.M{"kind": "paloalto-ngfw"}
	all, err := storage.MDB.Collection("cspm_resources").Distinct(ctx, "region", filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}

	result := lo.FilterMap(all, func(item any, _ int) (string, bool) {
		itemStr, _ := item.(string)
		return itemStr, itemStr != ""
	})
	slices.SortFunc(result, func(a, b string) int {
		return strings.Compare(a, b)
	})
	handler.Success(ctx, result)
}

func modifyPaloAltoFirewall(result map[string]any) {
	//remove original_object
	result["original_object"] = map[string]any{}

	//remove rules
	devicesAny, ok := result["devices"]
	if !ok {
		return
	}

	devicesAnySlice, ok := devicesAny.(primitive.A)
	if !ok {
		return
	}
	for _, device := range devicesAnySlice {
		deviceObj, ok := device.(map[string]any)
		if !ok {
			continue
		}

		vsysesAny, ok := deviceObj["vsyses"]
		if !ok {
			continue
		}
		vsysesAnySlice, ok := vsysesAny.(primitive.A)
		if !ok {
			continue
		}
		for _, vsys := range vsysesAnySlice {
			vsysObj, ok := vsys.(map[string]any)
			if !ok {
				continue
			}
			vsysObj["rules"] = nil
		}
	}
}

type SearchPaloAltoRulesRequest struct {
	Uid    string `json:"uid"`
	Device string `json:"device"`
	Vsys   string `json:"vsys"`
}

type SearchPaloAltoRulesResponseItem struct {
	Rules []*model.PaloaltoRule `json:"rules"`
}

func SearchPaloAltoRules(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchPaloAltoRulesRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"uid": req.Uid}

	var result model.PaloaltoFirewall
	err = storage.MDB.Collection("cspm_resources").FindOne(ctx, filter).Decode(&result)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}

	device, found := lo.Find(result.Devices, func(device *model.PaloaltoDevice) bool {
		return device.Name == req.Device
	})
	if !found {
		handler.InvalidRequest(ctx, "device not found")
		return
	}
	vsys, found := lo.Find(device.Vsyses, func(vsys *model.PaloaltoVsys) bool {
		return vsys.Name == req.Vsys
	})
	if !found {
		handler.InvalidRequest(ctx, "vsys not found")
		return
	}

	low := max(0, (pq.Page-1)*pq.Size)
	high := min(len(vsys.Rules), pq.Page*pq.Size)
	if low > high {
		low = high
	}

	resp := handler.PagnationResponseData[*model.PaloaltoRule]{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count = int64(len(vsys.Rules))
	resp.Items = vsys.Rules[low:high]


	handler.Success(ctx, resp)
}
