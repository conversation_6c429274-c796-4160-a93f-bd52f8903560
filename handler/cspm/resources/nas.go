package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchNasRequest struct {
	Keyword  string `json:"keyword"`
	Class    string `json:"class"`
	Encrypt  *bool  `json:"encrypt"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchNasResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Provider   string `json:"provider"`
	Name       string `json:"name"`
	Class      string `json:"class"`
	Encrypt    bool   `json:"encrypt"`
	AclEnabled bool   `json:"acl_enabled"`
}

func SearchNAS(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchNasRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "nas"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.Class != "" {
		filter["class"] = req.Class
	}
	if req.Encrypt != nil {
		filter["encrypt"] = *req.Encrypt
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchNasResponseItem]{}
	resp.Items = []SearchNasResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.NAS{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchNasResponseItem{
			Uid:        element.UID,
			UpdateTime: element.UpdateTime,
			Provider:   element.Provider,
			Name:       element.Name,
			Class:      element.Class,
			Encrypt:    element.Encrypt,
			AclEnabled: len(element.ACLRules) > 0,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}

func GetNasClassOptions(ctx *gin.Context) {
	filter := bson.M{"kind": "nas"}
	all, err := storage.MDB.Collection("cspm_resources").Distinct(ctx, "class", filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}

	result := lo.FilterMap(all, func(item any, _ int) (string, bool) {
		itemStr, _ := item.(string)
		return itemStr, itemStr != ""
	})
	slices.SortFunc(result, func(a, b string) int {
		return strings.Compare(a, b)
	})
	handler.Success(ctx, result)
}
