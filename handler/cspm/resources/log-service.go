package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchLogServiceRequest struct {
	Keyword              string `json:"keyword"`
	ProjectName          string `json:"project_name"`
	LogStoreConfigsExist *bool  `json:"log_store_configs_exist"`
	PolicyDocumentsExist *bool  `json:"policy_documents_exist"`
	IsGlobal             *bool  `json:"is_global"`
}
type SearchLogServiceResponseItem struct {
	Uid                  string `json:"uid"`
	UpdateTime           int64  `json:"update_time"`
	Provider             string `json:"provider"`
	Name                 string `json:"name"`
	ProjectName          string `json:"project_name"`
	LogStoreConfigsExist bool   `json:"log_store_configs_exist"`
	PolicyDocumentsExist bool   `json:"policy_documents_exist"`
}

func SearchLogService(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchLogServiceRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "log-service"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.ProjectName != "" {
		filter["project_name"] = req.ProjectName
	}
	if req.LogStoreConfigsExist != nil {
		filter["log_store_configs.0"] = bson.M{"$exists": *req.LogStoreConfigsExist}
	}
	if req.PolicyDocumentsExist != nil {
		filter["policy_documents.0"] = bson.M{"$exists": *req.PolicyDocumentsExist}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchLogServiceResponseItem]{}
	resp.Items = []SearchLogServiceResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.LogService{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchLogServiceResponseItem{
			Uid:                  element.UID,
			UpdateTime:           element.UpdateTime,
			Provider:             element.Provider,
			Name:                 element.Name,
			ProjectName:          element.Project,
			LogStoreConfigsExist: len(element.LogStoreConfigs) > 0,
			PolicyDocumentsExist: len(element.PolicyDocuments) > 0,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
