package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchCDNRequest struct {
	Keyword      string `json:"keyword"`
	HTTPSEnabled *bool  `json:"https_enabled"`
	IsGlobal     *bool  `json:"is_global"`
}

type SearchCDNResponseItem struct {
	Uid           string `json:"uid"`
	UpdateTime    int64  `json:"update_time"`
	Provider      string `json:"provider"`
	Name          string `json:"name"`
	Status        string `json:"status"`
	CNAME         string `json:"cname"`
	SourceSiteNum int    `json:"source_site_num"`
}

func SearchCDN(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON><PERSON><PERSON>())
		return
	}
	req := &SearchCDNRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>rror())
		return
	}
	filter := bson.M{"kind": "cdn"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.HTTPSEnabled != nil {
		filter["https_enabled"] = req.HTTPSEnabled
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchCDNResponseItem]{}
	resp.Items = []SearchCDNResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.CDN{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchCDNResponseItem{
			Uid:           element.UID,
			UpdateTime:    element.UpdateTime,
			Provider:      element.Provider,
			Name:          element.Name,
			Status:        element.Status,
			CNAME:         element.CNAME,
			SourceSiteNum: len(element.SourceSite),
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
