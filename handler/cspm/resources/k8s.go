package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchK8SRequest struct {
	Keyword       string `json:"keyword"`
	Cidr          string `json:"cidr"`
	PublicAllowed *bool  `json:"public_allowed"`
	IsGlobal      *bool  `json:"is_global"`
}
type SearchK8SResponseItem struct {
	Uid             string `json:"uid"`
	UpdateTime      int64  `json:"update_time"`
	Provider        string `json:"provider"`
	Name            string `json:"name"`
	EngineVersion   string `json:"engine_version"`
	PrivateEndpoint string `json:"private_endpoint"`
	PublicEndpoint  string `json:"public_endpoint"`
}

func SearchK8S(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchK8SRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.D{{Key: "kind", Value: "kubernetes"}}
	if req.Keyword != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}})
	}
	if req.Cidr != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"service_cidrs": req.Cidr},
			{"pod_cidrs": req.Cidr},
		}})
	}
	if req.PublicAllowed != nil {
		if *req.PublicAllowed {
			filter = append(filter, bson.E{Key: "public_endpoint", Value: bson.M{"$ne": ""}})
		} else {
			filter = append(filter, bson.E{Key: "public_endpoint", Value: ""})
		}
	}
	if req.IsGlobal != nil {
		filter = append(filter, bson.E{Key: "is_global", Value: *req.IsGlobal})
	}
	resp := handler.PagnationResponseData[SearchK8SResponseItem]{}
	resp.Items = []SearchK8SResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.K8S{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchK8SResponseItem{
			Uid:             element.UID,
			UpdateTime:      element.UpdateTime,
			Provider:        element.Provider,
			Name:            element.Name,
			EngineVersion:   element.EngineVersion,
			PrivateEndpoint: element.PrivateEndpoint,
			PublicEndpoint:  element.PublicEndpoint,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
