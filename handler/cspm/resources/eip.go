package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchEIPRequest struct {
	Keyword  string `json:"keyword"`
	IP       string `json:"ip"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchEIPResponseItem struct {
	Uid        string `json:"uid"`
	UpdateTime int64  `json:"update_time"`
	Name       string `json:"name"`
	Provider   string `json:"provider"`
	IP         string `json:"ip"`
	Status     string `json:"status"`
}

func SearchEIP(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchEIPRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>rror())
		return
	}
	filter := bson.M{"kind": "eip"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.IP != "" {
		filter["ip"] = req.IP
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchEIPResponseItem]{}
	resp.Items = []SearchEIPResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	eips := []model.EIP{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &eips)
	for _, eip := range eips {
		resp.Items = append(resp.Items, SearchEIPResponseItem{
			Uid:        eip.UID,
			UpdateTime: eip.UpdateTime,
			Name:       eip.Name,
			Provider:   eip.Provider,
			IP:         eip.IP,
			Status:     eip.Status,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
