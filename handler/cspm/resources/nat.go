package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchNATRequest struct {
	Keyword              string `json:"keyword"`
	IP                   string `json:"ip"`
	TrafficMirrorEnabled *bool `json:"traffic_mirror_enabled"`
	IsGlobal             *bool `json:"is_global"`
}
type SearchNATResponseItem struct {
	Uid         string `json:"uid"`
	UpdateTime  int64  `json:"update_time"`
	Provider    string `json:"provider"`
	Name        string `json:"name"`
	SNATRuleNum int    `json:"snat_rule_num"`
	DNATRuleNum int    `json:"dnat_rule_num"`
	Status      string `json:"status"`
}

func SearchNAT(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchNATRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.D{{Key: "kind", Value: "nat"}}
	if req.Keyword != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}})
	}
	if req.IP != "" {
		filter = append(filter, bson.E{Key: "$or", Value: []bson.M{
			{"snat_rules": bson.M{"$elemMatch": bson.M{"eip": req.IP}}},
			{"dnat_rules": bson.M{"$elemMatch": bson.M{"external_ip": req.IP}}},
		}})
	}
	if req.TrafficMirrorEnabled != nil {
		filter = append(filter, bson.E{Key: "traffic_mirror_enabled", Value: req.TrafficMirrorEnabled})
	}
	if req.IsGlobal != nil {
		filter = append(filter, bson.E{Key: "is_global", Value: *req.IsGlobal})
	}
	resp := handler.PagnationResponseData[SearchNATResponseItem]{}
	resp.Items = []SearchNATResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.NAT{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchNATResponseItem{
			Uid:         element.UID,
			UpdateTime:  element.UpdateTime,
			Provider:    element.Provider,
			Name:        element.Name,
			SNATRuleNum: len(element.SNATRules),
			DNATRuleNum: len(element.DNATRules),
			Status:      element.Status,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
