package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchCloudServiceProviderRequest struct {
	Keyword  string `json:"keyword"`
	IsGlobal *bool  `json:"is_global"`
}
type SearchCloudServiceProviderResponseItem struct {
	Uid             string `json:"uid"`
	UpdateTime      int64  `json:"update_time"`
	Provider        string `json:"provider"`
	Name            string `json:"name"`
	AuditLogTypeNum int    `json:"audit_log_type_num"`
}

func SearchCloudServiceProvider(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.<PERSON>())
		return
	}
	req := &SearchCloudServiceProviderRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "cloud-service-provider"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}

	resp := handler.PagnationResponseData[SearchCloudServiceProviderResponseItem]{}
	resp.Items = []SearchCloudServiceProviderResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []struct {
		model.CloudServiceProvider `bson:",inline"`
		AuditLogTypeNum            int `bson:"audit_log_type_num"`
	}{}

	opts := pq.FindOptions().
		SetProjection(bson.M{"uid": 1, "update_time": 1, "provider": 1, "name": 1, "audit_log_type_num": bson.M{"$size": "$audit_log_status"}}).
		SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}})
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, opts)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		resp.Items = append(resp.Items, SearchCloudServiceProviderResponseItem{
			Uid:             element.UID,
			UpdateTime:      element.UpdateTime,
			Provider:        element.Provider,
			Name:            element.Name,
			AuditLogTypeNum: element.AuditLogTypeNum,
		})
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
