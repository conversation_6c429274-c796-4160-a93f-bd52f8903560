package resources

import (
	"backend/handler"
	"backend/model"
	"backend/storage"
	"regexp"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchUserRequest struct {
	Keyword      string `json:"keyword"`
	MFAEnabled   *bool  `json:"mfa_enabled"`
	LoginAllowed *bool  `json:"login_allowed"`
	IsGlobal     *bool  `json:"is_global"`
}
type SearchUserResponseItem struct {
	Uid          string `json:"uid"`
	UpdateTime   int64  `json:"update_time"`
	Provider     string `json:"provider"`
	Name         string `json:"name"`
	DisplayName  string `json:"display_name"`
	MFAEnabled   bool   `json:"mfa_enabled"`
	LoginAllowed bool   `json:"login_allowed"`
}

func SearchUser(ctx *gin.Context) {
	pq, err := handler.DecodePageQueryFromCtx(ctx)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	req := &SearchUserRequest{}
	err = ctx.Bind(req)
	if err != nil {
		handler.InvalidRequest(ctx, err.Error())
		return
	}
	filter := bson.M{"kind": "user"}
	if req.Keyword != "" {
		filter["$or"] = []bson.M{
			{"name": primitive.Regex{Pattern: regexp.QuoteMeta(req.Keyword), Options: "i"}},
			{"display_name": bson.M{"$regex": regexp.QuoteMeta(req.Keyword)}},
			{"uid": req.Keyword},
			{"original_id": req.Keyword},
		}
	}
	if req.MFAEnabled != nil {
		filter["mfa_enabled"] = req.MFAEnabled
	}
	if req.LoginAllowed != nil {
		filter["login_allowed"] = req.LoginAllowed
	}
	if req.IsGlobal != nil {
		filter["is_global"] = *req.IsGlobal
	}
	resp := handler.PagnationResponseData[SearchUserResponseItem]{}
	resp.Items = []SearchUserResponseItem{}
	resp.Pagination.Page = pq.Page
	resp.Pagination.Size = pq.Size
	resp.Pagination.Count, err = storage.MDB.Collection("cspm_resources").CountDocuments(ctx, filter)
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	all := []model.User{}
	cur, err := storage.MDB.Collection("cspm_resources").Find(ctx, filter, pq.FindOptions().SetSort(bson.D{{Key: "provider", Value: 1}, {Key: "name", Value: 1}}))
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
		return
	}
	err = cur.All(ctx, &all)
	for _, element := range all {
		item := SearchUserResponseItem{
			Uid:          element.UID,
			UpdateTime:   element.UpdateTime,
			Provider:     element.Provider,
			Name:         element.Name,
			DisplayName:  element.DisplayName,
			MFAEnabled:   element.MFAEnabled,
			LoginAllowed: element.LoginAllowed,
		}

		resp.Items = append(resp.Items, item)
	}
	if err != nil {
		handler.DatabaseError(ctx, err.Error())
	} else {
		handler.Success(ctx, resp)
	}
}
