package infra

import (
	"context"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

var (
	Neo4j neo4j.DriverWithContext
)

func InitNeo4j() (err error) {
	Neo4j, err = neo4j.NewDriverWithContext("neo4j://172.24.131.118:7687", neo4j.BasicAuth("neo4j", "source-blitz-shampoo-urban-halt-9305", ""))
	if err != nil {
		return err
	}
	err = Neo4j.VerifyConnectivity(context.Background())
	if err != nil {
		Neo4j.Close(context.Background())
		Neo4j = nil
		return err
	}

	return nil
}
