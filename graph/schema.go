package graph

import (
	"fmt"

	"github.com/samber/lo"
)

// Property
type PropertyRef struct {
	Name               string
	SetInKwargs        bool
	ExtraIndex         bool
	IgnoreCase         bool
	FuzzyAndIgnoreCase bool
	OneToMany          bool
}

func (p PropertyRef) String() string {
	return lo.Ternary(p.SetInKwargs, fmt.Sprintf("$%s", p.Name), fmt.Sprintf("item.%s", p.Name))

}

// Node
type NodeProperties map[string]PropertyRef

type ExtraNodeLabels []string

type NodeSchema struct {
	Label                   string
	Properties              NodeProperties
	SubResourceRelationship *RelSchema
	OtherRelationships      OtherRelationships
	ExtraNodeLabels         ExtraNodeLabels
}

// Relationship
type LinkDirection string

const (
	INWARD  LinkDirection = "INWARD"
	OUTWARD LinkDirection = "OUTWARD"
)

type RelProperties map[string]PropertyRef

type TargetNodeMatcher map[string]PropertyRef

type RelSchema struct {
	Properties        RelProperties
	TargetNodeLabel   string
	TargetNodeMatcher TargetNodeMatcher
	RelLabel          string
	Direction         LinkDirection
}

type OtherRelationships []RelSchema
