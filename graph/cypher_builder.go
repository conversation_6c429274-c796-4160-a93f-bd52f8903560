package graph

import (
	"fmt"
	"strings"
	"sync"

	"github.com/samber/lo"
)

var indexCreatedMap = map[string]struct{}{}
var indexCreatedMutex = sync.RWMutex{}

func buildNodePropertiesStatement(nodePropertyMap map[string]PropertyRef, extraNodeLabels ExtraNodeLabels) string {
	ingestFieldTemplate := Template("i.$node_property = $property_ref")

	setProperties := lo.OmitBy<PERSON>eys(nodePropertyMap, []string{"uid"})
	if len(setProperties) == 0 && len(extraNodeLabels) == 0 {
		return ""
	}

	setClause := "SET\n    "
	setClause += strings.Join(
		lo.MapToSlice(
			setProperties,
			func(nodeProperty string, propertyRef PropertyRef) string {
				return ingestFieldTemplate.SafeSubstitute(map[string]string{
					"node_property": nodeProperty,
					"property_ref":  propertyRef.String(),
				})
			},
		),
		",\n    ",
	)

	if len(extraNodeLabels) > 0 {
		extraLabels := strings.Join(extraNodeLabels, ":")
		setClause += fmt.Sprintf(",\n                i:{%s}", extraLabels)
	}

	return setClause
}

func buildRelPropertiesStatement(relVar string, relProperties RelProperties) string {
	setClause := ""
	ingestFieldsTemplate := Template("$rel_var.$rel_property = $property_ref")

	if len(relProperties) > 0 {
		setClause = strings.Join(
			lo.MapToSlice(relProperties, func(relProperty string, propertyRef PropertyRef) string {
				return ingestFieldsTemplate.SafeSubstitute(map[string]string{
					"rel_var":      relVar,
					"rel_property": relProperty,
					"property_ref": propertyRef.String(),
				})
			}),
			",\n",
		)
	}

	return setClause
}

func buildMatchClause(nodeMatcher TargetNodeMatcher) string {
	match := Template("$Key: $PropRef")

	result := lo.MapToSlice(nodeMatcher, func(key string, propRef PropertyRef) string {
		return match.SafeSubstitute(map[string]string{
			"Key":     key,
			"PropRef": propRef.String(),
		})
	})

	return strings.Join(result, ",\n")
}

func buildWhereClauseForRelMatch(nodeVar string, relMatcher TargetNodeMatcher) string {
	match := Template("$node_var.$key = $prop_ref")
	caseInsensitiveMatch := Template("toLower($node_var.$key) = toLower($prop_ref)")
	fuzzyAndIgnorecaseMatch := Template("toLower($node_var.$key) CONTAINS toLower($prop_ref)")
	oneToManyMatch := Template("$node_var.$key IN $prop_ref")

	var result []string
	for key, propRef := range relMatcher {
		var propLine string
		if propRef.IgnoreCase {
			propLine = caseInsensitiveMatch.SafeSubstitute(map[string]string{
				"node_var": nodeVar,
				"key":      key,
				"prop_ref": propRef.String(),
			})
		} else if propRef.FuzzyAndIgnoreCase {
			propLine = fuzzyAndIgnorecaseMatch.SafeSubstitute(map[string]string{
				"node_var": nodeVar,
				"key":      key,
				"prop_ref": propRef.String(),
			})
		} else if propRef.OneToMany {
			propLine = oneToManyMatch.SafeSubstitute(map[string]string{
				"node_var": nodeVar,
				"key":      key,
				"prop_ref": propRef.String(),
			})
		} else {
			propLine = match.SafeSubstitute(map[string]string{
				"node_var": nodeVar,
				"key":      key,
				"prop_ref": propRef.String(),
			})
		}

		result = append(result, propLine)
	}

	return strings.Join(result, " AND\n")
}

func buildAttachSubResourceStatement(subResourceRel *RelSchema) string {
	if subResourceRel == nil {
		return ""
	}

	relMergeTemplate := lo.Ternary(
		subResourceRel.Direction == INWARD,
		Template("MERGE (i)<-[r:$SubResourceRelLabel]-(j)"),
		Template("MERGE (i)-[r:$SubResourceRelLabel]->(j)"),
	)

	relMergeClause := relMergeTemplate.SafeSubstitute(map[string]string{
		"SubResourceRelLabel": subResourceRel.RelLabel,
	})

	attachSubResourceStatement := Template(SUB_RESOURCE_ATTACH_TEMPLATE).SafeSubstitute(map[string]string{
		"SubResourceLabel":             subResourceRel.TargetNodeLabel,
		"MatchClause":                  buildMatchClause(subResourceRel.TargetNodeMatcher),
		"RelMergeClause":               relMergeClause,
		"SubResourceRelLabel":          subResourceRel.RelLabel,
		"set_rel_properties_statement": buildRelPropertiesStatement("r", subResourceRel.Properties),
	})

	return attachSubResourceStatement
}

func buildAttachAdditionalLinksStatement(otherRels OtherRelationships) string {
	if len(otherRels) == 0 {
		return ""
	}

	var links []string
	for i, link := range otherRels {
		nodeVar := fmt.Sprintf("n%d", i)
		relVar := fmt.Sprintf("r%d", i)

		relMergeTemplate := lo.Ternary(
			link.Direction == INWARD,
			Template("MERGE (i)<-[$rel_var:$AddlRelLabel]-($node_var)"),
			Template("MERGE (i)-[$rel_var:$AddlRelLabel]->($node_var)"),
		)

		relMerge := relMergeTemplate.SafeSubstitute(map[string]string{
			"rel_var":      relVar,
			"AddlRelLabel": link.RelLabel,
			"node_var":     nodeVar,
		})

		additionalRef := Template(ADDITIONAL_LINKS_TEMPLATE).SafeSubstitute(map[string]string{
			"AddlLabel":                    link.TargetNodeLabel,
			"WhereClause":                  buildWhereClauseForRelMatch(nodeVar, link.TargetNodeMatcher),
			"node_var":                     nodeVar,
			"rel_var":                      relVar,
			"RelMerge":                     relMerge,
			"set_rel_properties_statement": buildRelPropertiesStatement(relVar, link.Properties),
		})

		links = append(links, additionalRef)
	}

	return strings.Join(links, "UNION")
}

func buildAttachAdditionalLinksStatementUsingForeach(otherRels OtherRelationships) string {
	if len(otherRels) == 0 {
		return ""
	}

	var optionalMatchClauses []string
	var links []string
	for i, link := range otherRels {
		nodeVar := fmt.Sprintf("n%d", i)
		relVar := fmt.Sprintf("r%d", i)

		optionalMatchClause := Template(ADDITIONAL_LINKS_TEMPLATE_USING_FOREACH_OPTIONAL_MATCH_CLAUSE).SafeSubstitute(map[string]string{
			"node_var":    nodeVar,
			"AddlLabel":   link.TargetNodeLabel,
			"WhereClause": buildWhereClauseForRelMatch(nodeVar, link.TargetNodeMatcher),
		})

		optionalMatchClauses = append(optionalMatchClauses, optionalMatchClause)

		relMergeTemplate := lo.Ternary(
			link.Direction == INWARD,
			Template("MERGE (i)<-[$rel_var:$AddlRelLabel]-($node_var)"),
			Template("MERGE (i)-[$rel_var:$AddlRelLabel]->($node_var)"),
		)

		relMerge := relMergeTemplate.SafeSubstitute(map[string]string{
			"rel_var":      relVar,
			"AddlRelLabel": link.RelLabel,
			"node_var":     nodeVar,
		})

		additionalRef := Template(ADDITIONAL_LINKS_TEMPLATE_USING_FOREACH).SafeSubstitute(map[string]string{
			"AddlLabel":                    link.TargetNodeLabel,
			"node_var":                     nodeVar,
			"rel_var":                      relVar,
			"RelMerge":                     relMerge,
			"set_rel_properties_statement": buildRelPropertiesStatement(relVar, link.Properties),
		})

		links = append(links, additionalRef)
	}

	return Template(ATTACH_ADDITIONAL_LINKS_TEMPLATE_USING_FOREACH).SafeSubstitute(map[string]string{
		"optional_match_clause": strings.Join(optionalMatchClauses, ""),
		"foreach_clause":        strings.Join(links, ""),
	})
}

func buildAttachRelationshipsStatementUsingForeach(subResourceRel *RelSchema, otherRels OtherRelationships) string {
	if subResourceRel == nil && len(otherRels) == 0 {
		return ""
	}

	attachSubResourceStatement := buildAttachSubResourceStatement(subResourceRel)
	attachAdditionalLinksStatement := buildAttachAdditionalLinksStatementUsingForeach(otherRels)

	statements := []string{}

	if subResourceRel != nil {
		statements = append(statements, attachSubResourceStatement)
	}

	if len(otherRels) > 0 {
		statements = append(statements, attachAdditionalLinksStatement)
	}

	attachRelationshipsStatement := strings.Join(statements, "UNION")

	return Template(ATTACH_RELATIONSHIPS_TEMPLATE).SafeSubstitute(map[string]string{
		"attach_relationships_statement": attachRelationshipsStatement,
	})
}

func buildAttachRelationshipsStatement(subResourceRel *RelSchema, otherRels OtherRelationships) string {
	if subResourceRel == nil && len(otherRels) == 0 {
		return ""
	}

	attachSubResourceStatement := buildAttachSubResourceStatement(subResourceRel)
	attachAdditionalLinksStatement := buildAttachAdditionalLinksStatement(otherRels)

	statements := []string{}

	if subResourceRel != nil {
		statements = append(statements, attachSubResourceStatement)
	}

	if len(otherRels) > 0 {
		statements = append(statements, attachAdditionalLinksStatement)
	}

	attachRelationshipsStatement := strings.Join(statements, "UNION")

	return Template(ATTACH_RELATIONSHIPS_TEMPLATE).SafeSubstitute(map[string]string{
		"attach_relationships_statement": attachRelationshipsStatement,
	})
}

func filterSelectedRelationships(_ NodeSchema, _ []RelSchema) (*RelSchema, OtherRelationships) {
	// TODO: to be implemented
	return nil, nil
}

func buildUpdateQuery(nodeSchema NodeSchema, selectedRelationships []RelSchema) string {
	nodeProps := nodeSchema.Properties
	subResourceRel := nodeSchema.SubResourceRelationship
	otherRels := nodeSchema.OtherRelationships

	// TODO: selected relationships
	if len(selectedRelationships) > 0 {
		subResourceRel, otherRels = filterSelectedRelationships(nodeSchema, selectedRelationships)
	}

	ingestQuery := Template(INGESTION_TEMPLATE).SafeSubstitute(map[string]string{
		"node_label":                     nodeSchema.Label,
		"dict_id_field":                  nodeProps["uid"].String(),
		"set_node_properties_statement":  buildNodePropertiesStatement(nodeProps, nodeSchema.ExtraNodeLabels),
		"attach_relationships_statement": buildAttachRelationshipsStatement(subResourceRel, otherRels),
	})

	return ingestQuery
}

type IndexVar struct {
	NodeLabel string
	Attribute string
}

func buildCreateIndexQueries(nodeSchema NodeSchema) []string {
	indexTemplate := Template("CREATE INDEX IF NOT EXISTS FOR (n:$TargetNodeLabel) ON (n.$TargetAttribute);")
	indexVars := []IndexVar{}

	// common index
	indexVars = append(indexVars, IndexVar{NodeLabel: nodeSchema.Label, Attribute: "uid"})
	indexVars = append(indexVars, IndexVar{NodeLabel: nodeSchema.Label, Attribute: "last_seen"})

	// index for extra node labels
	if len(nodeSchema.ExtraNodeLabels) > 0 {
		for _, extraNodeLabel := range nodeSchema.ExtraNodeLabels {
			indexVars = append(indexVars, IndexVar{NodeLabel: extraNodeLabel, Attribute: "uid"})
		}
	}

	relSchemas := []RelSchema{}
	if nodeSchema.SubResourceRelationship != nil {
		relSchemas = append(relSchemas, *nodeSchema.SubResourceRelationship)
	}
	if len(nodeSchema.OtherRelationships) > 0 {
		relSchemas = append(relSchemas, nodeSchema.OtherRelationships...)
	}

	// index for node matcher in rel schemas
	for _, relSchema := range relSchemas {
		for _, property := range relSchema.TargetNodeMatcher {
			indexVars = append(indexVars, IndexVar{NodeLabel: relSchema.TargetNodeLabel, Attribute: property.Name})
		}
	}

	// user defined index
	for key, propRef := range nodeSchema.Properties {
		if propRef.ExtraIndex {
			indexVars = append(indexVars, IndexVar{NodeLabel: nodeSchema.Label, Attribute: key})
		}
	}

	queries := []string{}
	for _, indexVar := range indexVars {
		indexCreatedMutex.RLock()
		if _, ok := indexCreatedMap[indexVar.NodeLabel+"."+indexVar.Attribute]; ok {
			indexCreatedMutex.RUnlock()
			continue
		}
		indexCreatedMutex.RUnlock()

		indexCreatedMutex.Lock()
		indexCreatedMap[indexVar.NodeLabel+":"+indexVar.Attribute] = struct{}{}
		indexCreatedMutex.Unlock()

		queries = append(queries, indexTemplate.SafeSubstitute(map[string]string{
			"TargetNodeLabel": indexVar.NodeLabel,
			"TargetAttribute": indexVar.Attribute,
		}))
	}

	return queries
}
