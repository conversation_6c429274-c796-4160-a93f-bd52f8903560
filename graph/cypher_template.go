package graph

import (
	"regexp"
	"strings"
)

type Template string

func (t Template) SafeSubstitute(params map[string]string) string {
	re := regexp.MustCompile(`\$?(\w+)?`)

	result := re.ReplaceAllStringFunc(string(t), func(match string) string {
		varName := strings.Trim(match, "${}")

		if val, ok := params[varName]; ok {
			return val
		}
		return match
	})

	return result
}

const (
	INGEST_FIELD_TEMPLATE                            = "i.$node_property = $property_ref"
	INGEST_FIELDS_TEMPLATE                           = "$rel_var.$rel_property = $property_ref"
	MATCH_CLAUSE_TEMPLATE                            = "$Key: $PropRef"
	WHERE_CLAUSE_MATCH_TEMPLATE                      = "$node_var.$key = $prop_ref"
	WHERE_CLAUSE_CASE_INSENSITIVE_MATCH_TEMPLATE     = "toLower($node_var.$key) = toLower($prop_ref)"
	WHERE_CLAUSE_FUZZY_AND_IGNORECASE_MATCH_TEMPLATE = "toLower($node_var.$key) CONTAINS toLower($prop_ref)"
	WHERE_CLAUSE_ONE_TO_MANY_MATCH_TEMPLATE          = "$node_var.$key IN $prop_ref"
	WHERE_CLAUSE_TEMPLATE                            = "$node_var.$key = $prop_ref"

	SUB_RESOURCE_ATTACH_TEMPLATE = `
WITH i, item
OPTIONAL MATCH (j:$SubResourceLabel{$MatchClause})
WITH i, item, j WHERE j IS NOT NULL
$RelMergeClause
ON CREATE SET r.firstseen = timestamp()
SET
    $set_rel_properties_statement
`

	ADDITIONAL_LINKS_TEMPLATE = `
    WITH i, item
    OPTIONAL MATCH ($node_var:$AddlLabel)
    WHERE
        $WhereClause
    WITH i, item, $node_var WHERE $node_var IS NOT NULL
    $RelMerge
    ON CREATE SET $rel_var.firstseen = timestamp()
    SET
        $set_rel_properties_statement
`

	ADDITIONAL_LINKS_TEMPLATE_USING_FOREACH_OPTIONAL_MATCH_CLAUSE = `
    OPTIONAL MATCH ($node_var:$AddlLabel) WHERE $WhereClause
`

	ADDITIONAL_LINKS_TEMPLATE_USING_FOREACH = `
    FOREACH (_ IN CASE WHEN $node_var IS NOT NULL THEN [1] ELSE [] END |
        $RelMerge
        ON CREATE SET $rel_var.firstseen = timestamp()
        SET 
            $set_rel_properties_statement
    )
`

	ATTACH_ADDITIONAL_LINKS_TEMPLATE_USING_FOREACH = `
    WITH i, item
    $optional_match_clause

	$foreach_clause
`

	ATTACH_RELATIONSHIPS_TEMPLATE = `
WITH i, item
CALL {
    $attach_relationships_statement
}
`

	INGESTION_TEMPLATE = `
UNWIND $DictList AS item
MERGE (i:$node_label{uid: $dict_id_field})
ON CREATE SET i.firstseen = timestamp()
$set_node_properties_statement
$attach_relationships_statement
`
)
