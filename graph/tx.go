package graph

import (
	"context"
	"time"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

func updateGraphData(n4jSession neo4j.SessionWithContext, query string, dictList []map[string]any, kwargs map[string]any) {
	if kwargs == nil {
		kwargs = make(map[string]any)
	}

	dataBatch := lo.Chunk(dictList, 1000)
	for _, batch := range dataBatch {
		kwargs["DictList"] = batch

		var err error
		for range 3 {
			_, err = n4jSession.Run(context.Background(), query, kwargs)
			if err == nil {
				break
			}
			logger.DefaultLogger().Errorf("Failed to run query: %v", err)
			time.Sleep(3 * time.Second)
		}
		if err != nil {
			logger.DefaultLogger().<PERSON><PERSON>rf("max retry reached for query: %s", query)
		}
	}

}

func ensureIndexes(n4jSession neo4j.SessionWithContext, nodeSchema NodeSchema) {
	queries := buildCreateIndexQueries(nodeSchema)

	for _, query := range queries {
		_, err := n4jSession.Run(context.Background(), query, nil)
		if err != nil {
			logger.DefaultLogger().Errorf("Failed to run query: %v", err)
		}
	}
}

func Run(n4jSession neo4j.SessionWithContext, nodeSchema NodeSchema, dictList []map[string]any, kwargs map[string]any) {
	if len(dictList) == 0 {
		return
	}

	ensureIndexes(n4jSession, nodeSchema)
	updateQuery := buildUpdateQuery(nodeSchema, nil)
	updateGraphData(n4jSession, updateQuery, dictList, kwargs)
}
