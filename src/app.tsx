// 运行时配置
// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
//import { renderClient } from '@umijs/max';
//import { Select } from 'antd';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { history, Link } from '@umijs/max';
import { message } from 'antd';
import type { RequestConfig, RunTimeLayoutConfig } from 'umi';
import { useLocation } from 'umi';
import AreaSelect from './components/AreaSelect/AreaSelect';
import IconFont from './components/icon';
import AvatarDropdown from './components/IDaaS';
import { envConfig } from './services/baseUrl/baseUrl';
import auth from './utils/auth';
import { currentUser as queryCurrentUser } from './utils/login';

export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await queryCurrentUser({
        skipErrorHandler: true,
      });
      return msg.data;
    } catch (error) {
      history.push('/403');
    }
    return undefined;
  };
  const currentUser = await fetchUserInfo();
  return {
    fetchUserInfo,
    currentUser,
    settings: {} as Partial<LayoutSettings>,
  };
}

export const layout: RunTimeLayoutConfig = ({
  initialState,
  setInitialState,
}) => {
  // 创建一个组件来处理路径检查和渲染
  const ActionsRenderer = () => {
    const location = useLocation();
    if (location.pathname.startsWith('/cspm')) {
      return <AreaSelect />;
    }
    return null;
  };

  return {
    logo: <IconFont type="icon-lixiang" />,
    favicon: <IconFont type="icon-lixiang" />,
    layout: 'mix', // 设置布局为 'mix'
    menu: {
      locale: false,
    },
    header: {
      // top导航栏相关参数
      theme: 'light', // 主题 'light' 'dark' 'realDark'
      height: 48, // 具体数字，顶栏的高度
      menuRender: true,
    },
    actionsRender: () => [<ActionsRenderer key="actions-renderer" />],
    avatarProps: {
      src: initialState?.currentUser?.avatar,
      title: initialState?.currentUser?.name,
      render: (_, defaultDom) => {
        return <AvatarDropdown children={defaultDom} />;
      },
    },
    siderWidth: 210, // 具体数字，左侧菜单的宽度
    menuItemRender: (menuItemProps, defaultDom) => {
      // 支持二级菜单显示icon
      const content = (
        <div style={{ display: 'flex' }}>
          <span style={{ paddingRight: '8px' }}>
            {menuItemProps.pro_layout_parentKeys &&
              menuItemProps.pro_layout_parentKeys.length > 0 &&
              menuItemProps.icon}
          </span>
          {defaultDom}
        </div>
      );
      if (menuItemProps.name === '风险处置') {
        menuItemProps.path = envConfig.EcsAlertUrl;
      }
      if (menuItemProps.path && menuItemProps.path.startsWith('http')) {
        return (
          <a href={menuItemProps.path} target="_blank">
            {content}
          </a>
        );
      } else {
        return <Link to={menuItemProps.path || ''}>{content}</Link>;
      }
    },
  };
};

export const request: RequestConfig = {
  requestInterceptors: [
    async (config: any) => {
      try {
        const ak = await auth.getToken({ audience: envConfig.IDaasAudience });
        if (ak && config.headers) {
          config.headers.Authorization = `Bearer ${ak}`;
        }
      } catch (error) {
        console.error('token error', error);
      }
      return config;
    },
  ],
  errorConfig: {
    errorHandler: (error: any, opts: any) => {
      if (error.response && error?.response?.status === 401) {
        message.error('权限不足');
        return;
      }
    },
  },
};
