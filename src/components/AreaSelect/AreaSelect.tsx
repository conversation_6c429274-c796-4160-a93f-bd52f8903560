// src/components/RightContent/ProjectSelect.tsx
import { DownOutlined, GlobalOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Dropdown, message, Button } from 'antd';
import React from 'react';

const AreaSelect: React.FC = () => {
  // 使用我们刚刚创建的全局 model
  const { isGlobal, setIsGlobal } = useModel('global');

  // 获取当前选中区域的标签
  const getCurrentLabel = () => {
    switch (isGlobal) {
      case 'all':
        return '全部';
      case 'false':
        return '国内';
      case 'true':
        return '海外';
      default:
        return '请选择区域';
    }
  };

  const handleMenuClick = (e: { key: string }) => {
    const selectedIsGlobal = e.key;
    // 更新全局状态
    setIsGlobal(selectedIsGlobal);
    const areaLabel = selectedIsGlobal === 'all' ? '全部' : selectedIsGlobal === 'false' ? '国内' : '海外';
    message.success(`已成功切换到区域: ${areaLabel}`);
  };

  return (
    <Dropdown
      menu={{
        onClick: handleMenuClick,
        selectedKeys: [isGlobal],
        items: [
          {
            label: '全部',
            key: 'all',
          },
          {
            label: '国内', 
            key: 'false',
          },
          {
            label: '海外',
            key: 'true',
          },
        ],
      }}
      trigger={['hover', 'click']}
    >
      <Button
        type="text"
        style={{
          display: 'flex',
          alignItems: 'center',
          height: '32px',
          padding: '4px 8px',
          color: 'rgba(0, 0, 0, 0.65)',
          border: 'none',
          background: 'transparent',
        }}
      >
        <GlobalOutlined style={{ marginRight: 4 }} />
        {getCurrentLabel()}
        <DownOutlined style={{ marginLeft: 4, fontSize: '12px' }} />
      </Button>
    </Dropdown>
  );
};

export default AreaSelect;
