import { CSPM_KINDS_MAP } from '@/constants/cspm_kinds';
import { blue } from '@ant-design/colors';
import {
  ActionType,
  ProCard,
  ProColumns,
  ProFormText,
  ProTable,
  QueryFilter,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Form, Space, message } from 'antd';
import React, { useRef, useEffect, useState } from 'react';
import { getCSPMListCommon } from '../../../services/demo/CSPM';
import {
  adaptiveTableHeight,
  renderName,
  booleanOption,
  renderProvider,
} from '../../../utils/cspm';
import DescriptionDrawer, {
  DrawerState,
  closeDrawer,
  openDrawer,
} from '../../Common/cspm_resource_description';
import CSPMStyle from '../style/CSPMStyle.less';

const pageKind = CSPM_KINDS_MAP['security-group'];

const QueryFilterFields = [
  <ProFormText
    name="keyword"
    label="名称/ID"
    key="keyword"
    placeholder="支持名称/UID/原始ID"
  />,
];

const requestFunc = async (
  params: any,
  searchKeyword: any,
  setPageState: any,
) => {
  let resp = await getCSPMListCommon(
    {
      page: params.current,
      size: params.pageSize,
    },
    'SearchSecurityGroup',
    {
      ...searchKeyword,
      is_global: booleanOption(searchKeyword.is_global),
    },
  );
  setPageState({
    current: resp.data?.pagination.page,
    size: resp.data?.pagination.size,
    total: resp.data?.pagination.count,
  });
  return {
    data: resp.data?.items,
    total: resp.data?.pagination.count,
    success: resp.code === 0,
  };
};

const CSPMCommonPage: React.FC<API.CSPMTable> = () => {
  const searchFormRef = useRef<HTMLDivElement>(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const actionRef = useRef<ActionType>();

  const [searchForm] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [drawers, setDrawers] = useState<DrawerState[]>([]);
  const [tableHeight, setTableHeight] = useState(window.innerHeight);
  const [searchKeyword, setSearchKeyword] = useState<any>({});
  const [pageState, setPageState] = useState<{
    current: number;
    size: number;
    total: number;
  }>({
    current: 1,
    size: 10,
    total: 0,
  });

  const { isGlobal } = useModel('global');
  useEffect(() => {
    setSearchKeyword({ ...searchKeyword, is_global: isGlobal });
    actionRef.current?.reload(true);
  }, [isGlobal]);

  adaptiveTableHeight(tableRef, searchFormRef, setTableHeight);

  const columns: ProColumns<any>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      valueType: 'text',
      className: CSPMStyle.cspm_resource_table_name,
      copyable: true,
      ellipsis: true,
      width: '25%',
      fixed: 'left',
      render: (_: any, record: any) => {
        return renderName(record.name, record.uid, messageApi);
      },
    },
    {
      title: '资源提供商',
      dataIndex: 'provider',
      width: 100,
      valueType: 'text',
      render: (text: any) => {
        return renderProvider(text);
      },
    },
    {
      title: '规则数量',
      dataIndex: 'rule_num',
      valueType: 'text',
      align: 'center',
      width: 200,
    },
    {
      title: '同步时间',
      dataIndex: 'update_time',
      key: 'update_time',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 50,
      render: (_, record: any) => [
        <a
          key="view"
          style={{ color: blue.primary }}
          onClick={() => openDrawer(record?.uid, setDrawers, messageApi)}
        >
          详情
        </a>,
      ],
    },
  ];

  return (
    <>
      {contextHolder}
      <div
        style={{
          height: '100vh-120px',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div ref={searchFormRef}>
            <ProCard>
              <QueryFilter
                form={searchForm}
                labelWidth="auto"
                onReset={() => {
                  setSearchKeyword({});
                  actionRef.current?.reload();
                }}
                defaultCollapsed
                split
                onFinish={(values: any) => {
                  setSearchKeyword(values);
                  setPageState({ ...pageState, current: 1 });
                  actionRef.current?.reload();
                }}
              >
                {QueryFilterFields}
              </QueryFilter>
            </ProCard>
          </div>

          <div ref={tableRef} style={{ flex: 1, overflow: 'hidden' }}>
            <ProTable<any>
              columns={columns}
              actionRef={actionRef}
              className={CSPMStyle.cspm_resource_table}
              cardBordered
              request={async (params) =>
                requestFunc(params, searchKeyword, setPageState)
              }
              rowKey="uid"
              search={false}
              options={{
                reload: async () => {
                  actionRef.current?.reload();
                },
                density: false,
                setting: false,
              }}
              scroll={{ x: 'max-content', y: tableHeight }}
              pagination={{
                size: 'default',
                position: ['bottomCenter'],
                pageSize: pageState.size,
                pageSizeOptions: [10, 20, 50, 100],
                showSizeChanger: true,
                defaultCurrent: 1,
                current: pageState.current,
                total: pageState.total,
                showTotal: (total) => `总共 ${total} 条`,
                onChange: (page, pageSize) => {
                  if (pageState.size !== pageSize) {
                    page = 1;
                  }
                  setPageState({
                    ...pageState,
                    current: page,
                    size: pageSize,
                  });
                },
              }}
              dateFormatter="string"
              headerTitle={pageKind.label + '列表'}
            />
          </div>
        </Space>
      </div>

      {drawers.map((drawer) => {
        return (
          <DescriptionDrawer
            key={drawer.key}
            level={drawer.key}
            kind={drawer.kind}
            data={drawer.data}
            open={drawer.open}
            addDrawer={(uid) => openDrawer(uid, setDrawers, messageApi)}
            onClose={() => closeDrawer(drawer.key, setDrawers)}
          />
        );
      })}
    </>
  );
};

export default CSPMCommonPage;
