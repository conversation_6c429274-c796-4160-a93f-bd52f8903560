package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"disk_scanner/internal/types"

	"github.com/segmentio/kafka-go"
)

type TaskQueue interface {
	Publish(task *types.Task) error
	Consume(ctx context.Context) (*types.Task, error)
	Close() error
}

type KafkaTaskQueue struct {
	writer *kafka.Writer
	reader *kafka.Reader
	topic  string
}

type KafkaConfig struct {
	Brokers []string
	Topic   string
	GroupID string
}

func NewKafkaTaskQueue(config *KafkaConfig) *KafkaTaskQueue {
	writer := &kafka.Writer{
		Addr:         kafka.TCP(config.Brokers...),
		Topic:        config.Topic,
		Balancer:     &kafka.LeastBytes{},
		BatchTimeout: 10 * time.Millisecond,
		RequiredAcks: kafka.RequireOne,
		ErrorLogger:  kafka.LoggerFunc(log.Printf),
	}

	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:     config.Brokers,
		Topic:       config.Topic,
		GroupID:     config.GroupID,
		MinBytes:    10e3,
		MaxBytes:    10e6,
		MaxWait:     1 * time.Second,
		ErrorLogger: kafka.LoggerFunc(log.Printf),
	})

	return &KafkaTaskQueue{
		writer: writer,
		reader: reader,
		topic:  config.Topic,
	}
}

func (q *KafkaTaskQueue) Publish(task *types.Task) error {
	taskJSON, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal task: %w", err)
	}

	message := kafka.Message{
		Key:   []byte(task.TaskID),
		Value: taskJSON,
		Time:  time.Now(),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = q.writer.WriteMessages(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to publish task to Kafka: %w", err)
	}

	log.Printf("Published task %s to Kafka topic %s", task.TaskID, q.topic)
	return nil
}

func (q *KafkaTaskQueue) Consume(ctx context.Context) (*types.Task, error) {
	message, err := q.reader.ReadMessage(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to consume message from Kafka: %w", err)
	}

	var task types.Task
	err = json.Unmarshal(message.Value, &task)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal task: %w", err)
	}

	log.Printf("Consumed task %s from Kafka topic %s", task.TaskID, q.topic)
	return &task, nil
}

func (q *KafkaTaskQueue) Close() error {
	var errs []error

	if err := q.writer.Close(); err != nil {
		errs = append(errs, fmt.Errorf("failed to close Kafka writer: %w", err))
	}

	if err := q.reader.Close(); err != nil {
		errs = append(errs, fmt.Errorf("failed to close Kafka reader: %w", err))
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors closing Kafka queue: %v", errs)
	}

	return nil
}
