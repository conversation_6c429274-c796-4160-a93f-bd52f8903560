package scanner

import (
	"os/exec"
)

// TrivyScanner implements the IScanner interface using Trivy
type TrivyScanner struct{}

// NewTrivyScanner creates a new Trivy scanner instance
func NewTrivyScanner() IScanner {
	return &TrivyScanner{}
}

// ScanFS scans the filesystem at the given path using Trivy
func (s *TrivyScanner) ScanFS(path string) (string, error) {
	cmd := exec.Command("trivy", "fs", "--format", "json", "--quiet", path)
	
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	
	return string(output), nil
}