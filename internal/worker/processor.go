package worker

import (
	"log"

	"disk_scanner/internal/cloud"
	"disk_scanner/internal/scanner"
	"disk_scanner/internal/types"
)

// TaskProcessor handles the processing of vulnerability scanning tasks
type TaskProcessor struct {
	instanceID string
}

// NewTaskProcessor creates a new task processor instance
func NewTaskProcessor(instanceID string) *TaskProcessor {
	return &TaskProcessor{
		instanceID: instanceID,
	}
}

// ProcessTask processes a single vulnerability scanning task with robust cleanup
func (tp *TaskProcessor) ProcessTask(task *types.Task) (*types.ScanResult, error) {
	log.Printf("Starting processing for task ID: %s", task.TaskID)

	// Get the appropriate cloud provider
	provider, err := cloud.GetProvider(task.CloudProvider, task.Region)
	if err != nil {
		return nil, err
	}

	// Step 1: Create snapshot
	snapshotID, err := provider.CreateSnapshot(task.DiskID)
	if err != nil {
		return nil, err
	}
	// Ensure snapshot cleanup happens in reverse order
	defer func() {
		if err := provider.DeleteSnapshot(snapshotID); err != nil {
			log.Printf("Error deleting snapshot %s: %v", snapshotID, err)
		}
	}()

	// Step 2: Create disk from snapshot
	diskID, err := provider.CreateDiskFromSnapshot(snapshotID, task.Region+"a") // Assuming zone format
	if err != nil {
		return nil, err
	}
	// Ensure disk cleanup happens in reverse order
	defer func() {
		if err := provider.DeleteDisk(diskID); err != nil {
			log.Printf("Error deleting disk %s: %v", diskID, err)
		}
	}()

	// Step 3: Attach disk to this instance
	devicePath, err := provider.AttachDisk(tp.instanceID, diskID)
	if err != nil {
		return nil, err
	}
	// Ensure disk detachment happens in reverse order
	defer func() {
		if err := provider.DetachDisk(tp.instanceID, diskID); err != nil {
			log.Printf("Error detaching disk %s: %v", diskID, err)
		}
	}()

	// Step 4: Simulate mounting the device
	log.Printf("Mounting device %s to /mnt/scan_target", devicePath)
	// Ensure unmounting happens in reverse order
	defer log.Println("Unmounting /mnt/scan_target")

	// Step 5: Run vulnerability scan
	trivyScanner := scanner.NewTrivyScanner()
	rawJSON, err := trivyScanner.ScanFS("/mnt/scan_target")
	if err != nil {
		return nil, err
	}

	// Step 6: Create and return the scan result
	result := &types.ScanResult{
		TaskID:  task.TaskID,
		RawJSON: rawJSON,
	}

	log.Printf("Successfully completed processing for task ID: %s", task.TaskID)
	return result, nil
}
