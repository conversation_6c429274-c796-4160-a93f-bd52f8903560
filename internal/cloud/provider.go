package cloud

// ICloudProvider defines the interface for cloud provider operations
type ICloudProvider interface {
	CreateSnapshot(diskID string) (snapshotID string, err error)
	CreateDiskFromSnapshot(snapshotID, zone string) (diskID string, err error)
	AttachDisk(instanceID, diskID string) (devicePath string, err error)
	DetachDisk(instanceID, diskID string) error
	DeleteDisk(diskID string) error
	DeleteSnapshot(snapshotID string) error
}