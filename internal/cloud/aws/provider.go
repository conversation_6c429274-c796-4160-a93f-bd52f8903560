package aws

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
)

type AWSProvider struct {
	client *ec2.Client
	region string
}

type RetryConfig struct {
	MaxRetries int
	BackoffFactor time.Duration
}

func NewAWSProvider(region string) (*AWSProvider, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	client := ec2.NewFromConfig(cfg)
	
	return &AWSProvider{
		client: client,
		region: region,
	}, nil
}

func (p *AWSProvider) CreateSnapshot(diskID string) (string, error) {
	retryConfig := RetryConfig{MaxRetries: 3, BackoffFactor: 2 * time.Second}
	
	return p.withRetry(func() (string, error) {
		input := &ec2.CreateSnapshotInput{
			VolumeId:    aws.String(diskID),
			Description: aws.String(fmt.Sprintf("Snapshot for vulnerability scanning of volume %s", diskID)),
			TagSpecifications: []types.TagSpecification{
				{
					ResourceType: types.ResourceTypeSnapshot,
					Tags: []types.Tag{
						{
							Key:   aws.String("Purpose"),
							Value: aws.String("VulnerabilityScan"),
						},
						{
							Key:   aws.String("CreatedBy"),
							Value: aws.String("DiskScanner"),
						},
						{
							Key:   aws.String("SourceVolume"),
							Value: aws.String(diskID),
						},
					},
				},
			},
		}

		result, err := p.client.CreateSnapshot(context.TODO(), input)
		if err != nil {
			return "", fmt.Errorf("failed to create snapshot for volume %s: %w", diskID, err)
		}

		if result.SnapshotId == nil {
			return "", fmt.Errorf("snapshot ID is nil in response")
		}

		snapshotID := *result.SnapshotId

		err = p.waitForSnapshotCompletion(snapshotID)
		if err != nil {
			return "", fmt.Errorf("snapshot %s creation failed: %w", snapshotID, err)
		}

		return snapshotID, nil
	}, retryConfig)
}

func (p *AWSProvider) CreateDiskFromSnapshot(snapshotID, zone string) (string, error) {
	retryConfig := RetryConfig{MaxRetries: 3, BackoffFactor: 2 * time.Second}
	
	return p.withRetry(func() (string, error) {
		snapshotInfo, err := p.getSnapshotInfo(snapshotID)
		if err != nil {
			return "", fmt.Errorf("failed to get snapshot info: %w", err)
		}

		input := &ec2.CreateVolumeInput{
			SnapshotId:       aws.String(snapshotID),
			AvailabilityZone: aws.String(zone),
			Size:             snapshotInfo.VolumeSize,
			TagSpecifications: []types.TagSpecification{
				{
					ResourceType: types.ResourceTypeVolume,
					Tags: []types.Tag{
						{
							Key:   aws.String("Purpose"),
							Value: aws.String("VulnerabilityScan"),
						},
						{
							Key:   aws.String("CreatedBy"),
							Value: aws.String("DiskScanner"),
						},
						{
							Key:   aws.String("SourceSnapshot"),
							Value: aws.String(snapshotID),
						},
					},
				},
			},
		}

		result, err := p.client.CreateVolume(context.TODO(), input)
		if err != nil {
			return "", fmt.Errorf("failed to create volume from snapshot %s: %w", snapshotID, err)
		}

		if result.VolumeId == nil {
			return "", fmt.Errorf("volume ID is nil in response")
		}

		volumeID := *result.VolumeId

		err = p.waitForVolumeAvailable(volumeID)
		if err != nil {
			return "", fmt.Errorf("volume %s creation failed: %w", volumeID, err)
		}

		return volumeID, nil
	}, retryConfig)
}

func (p *AWSProvider) AttachDisk(instanceID, diskID string) (string, error) {
	retryConfig := RetryConfig{MaxRetries: 3, BackoffFactor: 2 * time.Second}
	
	return p.withRetry(func() (string, error) {
		deviceName := p.getNextAvailableDevice(instanceID)
		
		input := &ec2.AttachVolumeInput{
			VolumeId:   aws.String(diskID),
			InstanceId: aws.String(instanceID),
			Device:     aws.String(deviceName),
		}

		result, err := p.client.AttachVolume(context.TODO(), input)
		if err != nil {
			return "", fmt.Errorf("failed to attach volume %s to instance %s: %w", diskID, instanceID, err)
		}

		if result.Device == nil {
			return "", fmt.Errorf("device path is nil in response")
		}

		err = p.waitForVolumeAttached(diskID, instanceID)
		if err != nil {
			return "", fmt.Errorf("volume %s attachment to instance %s failed: %w", diskID, instanceID, err)
		}

		return *result.Device, nil
	}, retryConfig)
}

func (p *AWSProvider) DetachDisk(instanceID, diskID string) error {
	retryConfig := RetryConfig{MaxRetries: 3, BackoffFactor: 2 * time.Second}
	
	_, err := p.withRetry(func() (string, error) {
		input := &ec2.DetachVolumeInput{
			VolumeId:   aws.String(diskID),
			InstanceId: aws.String(instanceID),
			Force:      aws.Bool(false),
		}

		_, err := p.client.DetachVolume(context.TODO(), input)
		if err != nil {
			return "", fmt.Errorf("failed to detach volume %s from instance %s: %w", diskID, instanceID, err)
		}

		err = p.waitForVolumeDetached(diskID)
		if err != nil {
			return "", fmt.Errorf("volume %s detachment from instance %s failed: %w", diskID, instanceID, err)
		}

		return "", nil
	}, retryConfig)
	
	return err
}

func (p *AWSProvider) DeleteDisk(diskID string) error {
	retryConfig := RetryConfig{MaxRetries: 3, BackoffFactor: 2 * time.Second}
	
	_, err := p.withRetry(func() (string, error) {
		input := &ec2.DeleteVolumeInput{
			VolumeId: aws.String(diskID),
		}

		_, err := p.client.DeleteVolume(context.TODO(), input)
		if err != nil {
			return "", fmt.Errorf("failed to delete volume %s: %w", diskID, err)
		}

		return "", nil
	}, retryConfig)
	
	return err
}

func (p *AWSProvider) DeleteSnapshot(snapshotID string) error {
	retryConfig := RetryConfig{MaxRetries: 3, BackoffFactor: 2 * time.Second}
	
	_, err := p.withRetry(func() (string, error) {
		input := &ec2.DeleteSnapshotInput{
			SnapshotId: aws.String(snapshotID),
		}

		_, err := p.client.DeleteSnapshot(context.TODO(), input)
		if err != nil {
			return "", fmt.Errorf("failed to delete snapshot %s: %w", snapshotID, err)
		}

		return "", nil
	}, retryConfig)
	
	return err
}

func (p *AWSProvider) withRetry(operation func() (string, error), config RetryConfig) (string, error) {
	var lastErr error
	
	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		result, err := operation()
		if err == nil {
			return result, nil
		}
		
		lastErr = err
		
		if attempt < config.MaxRetries {
			backoff := time.Duration(attempt+1) * config.BackoffFactor
			time.Sleep(backoff)
		}
	}
	
	return "", fmt.Errorf("operation failed after %d attempts: %w", config.MaxRetries+1, lastErr)
}

func (p *AWSProvider) waitForSnapshotCompletion(snapshotID string) error {
	waiter := ec2.NewSnapshotCompletedWaiter(p.client)
	
	input := &ec2.DescribeSnapshotsInput{
		SnapshotIds: []string{snapshotID},
	}
	
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Minute)
	defer cancel()
	
	return waiter.Wait(ctx, input, 30*time.Second)
}

func (p *AWSProvider) waitForVolumeAvailable(volumeID string) error {
	waiter := ec2.NewVolumeAvailableWaiter(p.client)
	
	input := &ec2.DescribeVolumesInput{
		VolumeIds: []string{volumeID},
	}
	
	ctx, cancel := context.WithTimeout(context.TODO(), 10*time.Minute)
	defer cancel()
	
	return waiter.Wait(ctx, input, 5*time.Second)
}

func (p *AWSProvider) waitForVolumeAttached(volumeID, _ string) error {
	waiter := ec2.NewVolumeInUseWaiter(p.client)
	
	input := &ec2.DescribeVolumesInput{
		VolumeIds: []string{volumeID},
	}
	
	ctx, cancel := context.WithTimeout(context.TODO(), 5*time.Minute)
	defer cancel()
	
	return waiter.Wait(ctx, input, 5*time.Second)
}

func (p *AWSProvider) waitForVolumeDetached(volumeID string) error {
	waiter := ec2.NewVolumeAvailableWaiter(p.client)
	
	input := &ec2.DescribeVolumesInput{
		VolumeIds: []string{volumeID},
	}
	
	ctx, cancel := context.WithTimeout(context.TODO(), 5*time.Minute)
	defer cancel()
	
	return waiter.Wait(ctx, input, 5*time.Second)
}

func (p *AWSProvider) getSnapshotInfo(snapshotID string) (*types.Snapshot, error) {
	input := &ec2.DescribeSnapshotsInput{
		SnapshotIds: []string{snapshotID},
	}
	
	result, err := p.client.DescribeSnapshots(context.TODO(), input)
	if err != nil {
		return nil, err
	}
	
	if len(result.Snapshots) == 0 {
		return nil, fmt.Errorf("snapshot %s not found", snapshotID)
	}
	
	return &result.Snapshots[0], nil
}

func (p *AWSProvider) getNextAvailableDevice(instanceID string) string {
	usedDevices := p.getUsedDevices(instanceID)
	
	availableDevices := []string{
		"/dev/sdf", "/dev/sdg", "/dev/sdh", "/dev/sdi", "/dev/sdj",
		"/dev/sdk", "/dev/sdl", "/dev/sdm", "/dev/sdn", "/dev/sdo", "/dev/sdp",
	}
	
	for _, device := range availableDevices {
		if !contains(usedDevices, device) {
			return device
		}
	}
	
	return "/dev/sdf"
}

func (p *AWSProvider) getUsedDevices(instanceID string) []string {
	input := &ec2.DescribeVolumesInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("attachment.instance-id"),
				Values: []string{instanceID},
			},
		},
	}
	
	result, err := p.client.DescribeVolumes(context.TODO(), input)
	if err != nil {
		return []string{}
	}
	
	var devices []string
	for _, volume := range result.Volumes {
		for _, attachment := range volume.Attachments {
			if attachment.Device != nil {
				devices = append(devices, *attachment.Device)
			}
		}
	}
	
	return devices
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}