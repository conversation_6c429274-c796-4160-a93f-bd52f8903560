package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/samber/lo"
	"gopkg.in/yaml.v3"
)

type WorkerConfig struct {
	CloudProvider string        `yaml:"cloud_provider"`
	Region        string        `yaml:"region"`
	Zone          string        `yaml:"zone"`
	ProviderParam ProviderParam `yaml:"provider_param"`
	Kafka         KafkaConfig   `yaml:"kafka"`
}

type ProviderParam struct {
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
}

type OrchestratorConfig struct {
	APIPort     int             `yaml:"api_port"`
	EnableAPI   bool            `yaml:"enable_api"`
	EnableCron  bool            `yaml:"enable_cron"`
	EnableQueue bool            `yaml:"enable_queue"`
	Kafka       KafkaConfig     `yaml:"kafka"`
	Scheduler   SchedulerConfig `yaml:"scheduler"`
}

type SchedulerConfig struct {
	Enabled        bool     `yaml:"enabled"`
	CronExpression string   `yaml:"cron_expression"`
	Interval       string   `yaml:"interval"`
	CloudProviders []string `yaml:"cloud_providers"`
	Regions        []string `yaml:"regions"`
	MaxConcurrency int      `yaml:"max_concurrency"`
}

type KafkaConfig struct {
	Brokers []string `yaml:"brokers"`
	Topic   string   `yaml:"topic"`
	GroupID string   `yaml:"group_id"`
}

func InitConfig[T OrchestratorConfig | WorkerConfig]() (*T, error) {
	var err error
	var config T

	nodeRole := os.Getenv("NODE_ROLE")
	configDir := lo.CoalesceOrEmpty(os.Getenv("CONFIG_DIR"), ".")

	switch nodeRole {
	case "orchestrator", "worker":
		err = loadConfig(configDir, &config)
	default:
		err = fmt.Errorf("invalid node role, expected: worker/orchestrator, got: %s", nodeRole)
	}
	if err != nil {
		return nil, err
	}

	return &config, err
}

func loadConfig(dir string, config any) error {
	data, err := os.ReadFile(filepath.Join(dir, "config.yaml"))
	if err != nil {
		return err
	}

	err = yaml.Unmarshal(data, config)
	return err
}
