package scheduler

import (
	"context"
	"fmt"
	"log"
	"time"

	"disk_scanner/internal/config"
	"disk_scanner/internal/queue"
	"disk_scanner/internal/types"

	"github.com/robfig/cron/v3"
)

// TaskGenerator defines the interface for generating scan tasks
type TaskGenerator interface {
	GenerateTasks() ([]*types.Task, error)
}

// Scheduler manages scheduled task generation
type Scheduler struct {
	config        *config.SchedulerConfig
	taskQueue     queue.TaskQueue
	taskGenerator TaskGenerator
	cron          *cron.Cron
	ctx           context.Context
	cancel        context.CancelFunc
}

// NewScheduler creates a new scheduler instance
func NewScheduler(cfg *config.SchedulerConfig, taskQueue queue.TaskQueue, taskGenerator TaskGenerator) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &Scheduler{
		config:        cfg,
		taskQueue:     taskQueue,
		taskGenerator: taskGenerator,
		cron:          cron.New(cron.WithSeconds()),
		ctx:           ctx,
		cancel:        cancel,
	}
}

// Start begins the scheduled task generation
func (s *Scheduler) Start() error {
	if !s.config.Enabled {
		log.Println("Scheduler is disabled, skipping start")
		return nil
	}

	log.Println("Starting task scheduler...")

	// Add scheduled job based on configuration
	if s.config.CronExpression != "" {
		_, err := s.cron.AddFunc(s.config.CronExpression, s.generateAndPublishTasks)
		if err != nil {
			return fmt.Errorf("failed to add cron job: %w", err)
		}
		log.Printf("Scheduled task generation with cron expression: %s", s.config.CronExpression)
	} else if s.config.Interval != "" {
		interval, err := time.ParseDuration(s.config.Interval)
		if err != nil {
			return fmt.Errorf("invalid interval format: %w", err)
		}
		
		_, err = s.cron.AddFunc(fmt.Sprintf("@every %s", interval), s.generateAndPublishTasks)
		if err != nil {
			return fmt.Errorf("failed to add interval job: %w", err)
		}
		log.Printf("Scheduled task generation with interval: %s", interval)
	} else {
		return fmt.Errorf("neither cron_expression nor interval is configured")
	}

	// Start the cron scheduler
	s.cron.Start()
	log.Println("Task scheduler started successfully")

	return nil
}

// Stop gracefully stops the scheduler
func (s *Scheduler) Stop() error {
	log.Println("Stopping task scheduler...")
	
	if s.cron != nil {
		ctx := s.cron.Stop()
		select {
		case <-ctx.Done():
			log.Println("Scheduler stopped gracefully")
		case <-time.After(30 * time.Second):
			log.Println("Scheduler stop timeout, forcing shutdown")
		}
	}
	
	s.cancel()
	log.Println("Task scheduler stopped")
	return nil
}

// generateAndPublishTasks generates tasks and publishes them to the queue
func (s *Scheduler) generateAndPublishTasks() {
	log.Println("Generating scheduled scan tasks...")
	
	tasks, err := s.taskGenerator.GenerateTasks()
	if err != nil {
		log.Printf("Failed to generate tasks: %v", err)
		return
	}

	if len(tasks) == 0 {
		log.Println("No tasks generated")
		return
	}

	// Publish tasks to queue with concurrency control
	semaphore := make(chan struct{}, s.getMaxConcurrency())
	
	for _, task := range tasks {
		select {
		case <-s.ctx.Done():
			log.Println("Scheduler context cancelled, stopping task publication")
			return
		case semaphore <- struct{}{}:
			go func(t *types.Task) {
				defer func() { <-semaphore }()
				
				if err := s.taskQueue.Publish(t); err != nil {
					log.Printf("Failed to publish task %s: %v", t.TaskID, err)
				} else {
					log.Printf("Successfully published scheduled task %s", t.TaskID)
				}
			}(task)
		}
	}

	log.Printf("Initiated publication of %d scheduled tasks", len(tasks))
}

// getMaxConcurrency returns the configured max concurrency or a default value
func (s *Scheduler) getMaxConcurrency() int {
	if s.config.MaxConcurrency > 0 {
		return s.config.MaxConcurrency
	}
	return 10 // Default concurrency limit
}

// DefaultTaskGenerator provides a default implementation of TaskGenerator
type DefaultTaskGenerator struct {
	config *config.SchedulerConfig
}

// NewDefaultTaskGenerator creates a new default task generator
func NewDefaultTaskGenerator(cfg *config.SchedulerConfig) *DefaultTaskGenerator {
	return &DefaultTaskGenerator{
		config: cfg,
	}
}

// GenerateTasks generates scan tasks based on configured cloud providers and regions
func (d *DefaultTaskGenerator) GenerateTasks() ([]*types.Task, error) {
	var tasks []*types.Task
	
	// Use configured cloud providers or default ones
	cloudProviders := d.config.CloudProviders
	if len(cloudProviders) == 0 {
		cloudProviders = []string{"aws", "azure", "gcp"} // Default providers
	}
	
	// Use configured regions or default ones
	regions := d.config.Regions
	if len(regions) == 0 {
		regions = []string{"us-east-1", "us-west-2", "eu-west-1"} // Default regions
	}
	
	// Generate tasks for each provider-region combination
	for _, provider := range cloudProviders {
		for _, region := range regions {
			task := &types.Task{
				TaskID:        generateTaskID(),
				HostID:        fmt.Sprintf("scheduled-host-%s-%s", provider, region),
				DiskID:        fmt.Sprintf("scheduled-disk-%s-%s", provider, region),
				CloudProvider: provider,
				Region:        region,
			}
			tasks = append(tasks, task)
		}
	}
	
	log.Printf("Generated %d scheduled tasks for %d providers and %d regions", 
		len(tasks), len(cloudProviders), len(regions))
	
	return tasks, nil
}

// generateTaskID generates a unique task ID
func generateTaskID() string {
	return fmt.Sprintf("scheduled-task-%d", time.Now().UnixNano())
}
