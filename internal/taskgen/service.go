package taskgen

import (
	"fmt"
	"log"
	"time"

	"disk_scanner/internal/types"
)

// TaskGenerationRequest represents a request to generate scan tasks
type TaskGenerationRequest struct {
	CloudProviders []string `json:"cloud_providers,omitempty"`
	Regions        []string `json:"regions,omitempty"`
	HostIDs        []string `json:"host_ids,omitempty"`
	DiskIDs        []string `json:"disk_ids,omitempty"`
	TaskType       string   `json:"task_type,omitempty"`
	Priority       string   `json:"priority,omitempty"`
	MaxTasks       int      `json:"max_tasks,omitempty"`
}

// TaskGenerationResponse represents the response from task generation
type TaskGenerationResponse struct {
	Tasks     []*types.Task `json:"tasks"`
	Count     int           `json:"count"`
	RequestID string        `json:"request_id"`
	Timestamp time.Time     `json:"timestamp"`
}

// Service provides centralized task generation functionality
type Service struct {
	defaultProviders []string
	defaultRegions   []string
	maxTasksLimit    int
}

// NewService creates a new task generation service
func NewService() *Service {
	return &Service{
		defaultProviders: []string{"aws", "azure", "gcp"},
		defaultRegions:   []string{"us-east-1", "us-west-2", "eu-west-1"},
		maxTasksLimit:    1000, // Prevent excessive task generation
	}
}

// GenerateTasks generates scan tasks based on the provided request
func (s *Service) GenerateTasks(req *TaskGenerationRequest) (*TaskGenerationResponse, error) {
	if req == nil {
		req = &TaskGenerationRequest{}
	}

	// Validate and set defaults
	if err := s.validateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	requestID := generateRequestID()
	log.Printf("Generating tasks for request %s", requestID)

	var tasks []*types.Task

	// Generate tasks based on request type
	if len(req.HostIDs) > 0 && len(req.DiskIDs) > 0 {
		// Generate tasks for specific host-disk combinations
		tasks = s.generateSpecificTasks(req)
	} else {
		// Generate tasks for cloud provider-region combinations
		tasks = s.generateCloudTasks(req)
	}

	// Apply max tasks limit
	if req.MaxTasks > 0 && len(tasks) > req.MaxTasks {
		tasks = tasks[:req.MaxTasks]
		log.Printf("Limited tasks to %d as requested", req.MaxTasks)
	} else if len(tasks) > s.maxTasksLimit {
		tasks = tasks[:s.maxTasksLimit]
		log.Printf("Limited tasks to %d (service limit)", s.maxTasksLimit)
	}

	response := &TaskGenerationResponse{
		Tasks:     tasks,
		Count:     len(tasks),
		RequestID: requestID,
		Timestamp: time.Now(),
	}

	log.Printf("Generated %d tasks for request %s", len(tasks), requestID)
	return response, nil
}

// generateSpecificTasks generates tasks for specific host-disk combinations
func (s *Service) generateSpecificTasks(req *TaskGenerationRequest) []*types.Task {
	var tasks []*types.Task

	providers := req.CloudProviders
	if len(providers) == 0 {
		providers = s.defaultProviders
	}

	regions := req.Regions
	if len(regions) == 0 {
		regions = s.defaultRegions
	}

	// Generate tasks for each host-disk combination
	for i, hostID := range req.HostIDs {
		if i >= len(req.DiskIDs) {
			break // Ensure we don't go out of bounds
		}
		
		diskID := req.DiskIDs[i]
		
		// Create task for each provider-region combination
		for _, provider := range providers {
			for _, region := range regions {
				task := &types.Task{
					TaskID:        generateTaskID(),
					HostID:        hostID,
					DiskID:        diskID,
					CloudProvider: provider,
					Region:        region,
				}
				tasks = append(tasks, task)
			}
		}
	}

	return tasks
}

// generateCloudTasks generates tasks for cloud provider-region combinations
func (s *Service) generateCloudTasks(req *TaskGenerationRequest) []*types.Task {
	var tasks []*types.Task

	providers := req.CloudProviders
	if len(providers) == 0 {
		providers = s.defaultProviders
	}

	regions := req.Regions
	if len(regions) == 0 {
		regions = s.defaultRegions
	}

	// Generate tasks for each provider-region combination
	for _, provider := range providers {
		for _, region := range regions {
			task := &types.Task{
				TaskID:        generateTaskID(),
				HostID:        fmt.Sprintf("auto-host-%s-%s", provider, region),
				DiskID:        fmt.Sprintf("auto-disk-%s-%s", provider, region),
				CloudProvider: provider,
				Region:        region,
			}
			tasks = append(tasks, task)
		}
	}

	return tasks
}

// validateRequest validates the task generation request
func (s *Service) validateRequest(req *TaskGenerationRequest) error {
	// Validate cloud providers
	for _, provider := range req.CloudProviders {
		if !isValidCloudProvider(provider) {
			return fmt.Errorf("invalid cloud provider: %s", provider)
		}
	}

	// Validate regions
	for _, region := range req.Regions {
		if region == "" {
			return fmt.Errorf("empty region not allowed")
		}
	}

	// Validate host-disk combination
	if len(req.HostIDs) != len(req.DiskIDs) && len(req.HostIDs) > 0 && len(req.DiskIDs) > 0 {
		return fmt.Errorf("host_ids and disk_ids must have the same length when both are provided")
	}

	// Validate max tasks
	if req.MaxTasks < 0 {
		return fmt.Errorf("max_tasks cannot be negative")
	}

	return nil
}

// isValidCloudProvider checks if the cloud provider is supported
func isValidCloudProvider(provider string) bool {
	validProviders := map[string]bool{
		"aws":   true,
		"azure": true,
		"gcp":   true,
		"ali":   true, // Alibaba Cloud
	}
	return validProviders[provider]
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("req-%d", time.Now().UnixNano())
}

// generateTaskID generates a unique task ID
func generateTaskID() string {
	return fmt.Sprintf("task-%d", time.Now().UnixNano())
}

// GetSupportedProviders returns the list of supported cloud providers
func (s *Service) GetSupportedProviders() []string {
	return []string{"aws", "azure", "gcp", "ali"}
}

// GetDefaultRegions returns the default regions for a cloud provider
func (s *Service) GetDefaultRegions(provider string) []string {
	regionMap := map[string][]string{
		"aws":   {"us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"},
		"azure": {"eastus", "westus2", "westeurope", "southeastasia"},
		"gcp":   {"us-central1", "us-west1", "europe-west1", "asia-southeast1"},
		"ali":   {"cn-hangzhou", "cn-beijing", "cn-shanghai", "cn-shenzhen"},
	}
	
	if regions, exists := regionMap[provider]; exists {
		return regions
	}
	return s.defaultRegions
}
