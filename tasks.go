package main

import (
	"cloud-asset-migration/config"
	aliyun_api "cloud-asset-migration/provider/aliyun/API"
	aliyun_auditlog "cloud-asset-migration/provider/aliyun/AuditLog"
	aliyun_cdn "cloud-asset-migration/provider/aliyun/CDN"
	aliyun_cen "cloud-asset-migration/provider/aliyun/CEN"
	aliyun_cfw "cloud-asset-migration/provider/aliyun/CFW"
	aliyun_ecs "cloud-asset-migration/provider/aliyun/ECS"
	aliyun_es "cloud-asset-migration/provider/aliyun/ES"
	aliyun_fc "cloud-asset-migration/provider/aliyun/FC"
	aliyun_k8s "cloud-asset-migration/provider/aliyun/K8S"
	aliyun_lb "cloud-asset-migration/provider/aliyun/LB"
	aliyun_nas "cloud-asset-migration/provider/aliyun/NAS"
	aliyun_nat "cloud-asset-migration/provider/aliyun/NAT"
	aliyun_oss "cloud-asset-migration/provider/aliyun/OSS"
	aliyun_pc "cloud-asset-migration/provider/aliyun/PeerConnection"
	aliyun_ram "cloud-asset-migration/provider/aliyun/RAM"
	aliyun_rds "cloud-asset-migration/provider/aliyun/RDS"
	aliyun_redis "cloud-asset-migration/provider/aliyun/Redis"
	aliyun_sls "cloud-asset-migration/provider/aliyun/SLS"
	aliyun_vpc "cloud-asset-migration/provider/aliyun/VPC"
	aliyun_global_api "cloud-asset-migration/provider/aliyun_global/API"
	aliyun_global_cdn "cloud-asset-migration/provider/aliyun_global/CDN"
	aliyun_global_cen "cloud-asset-migration/provider/aliyun_global/CEN"
	aliyun_global_cfw "cloud-asset-migration/provider/aliyun_global/CFW"
	aliyun_global_ecs "cloud-asset-migration/provider/aliyun_global/ECS"
	aliyun_global_es "cloud-asset-migration/provider/aliyun_global/ES"
	aliyun_global_fc "cloud-asset-migration/provider/aliyun_global/FC"
	aliyun_global_k8s "cloud-asset-migration/provider/aliyun_global/K8S"
	aliyun_global_lb "cloud-asset-migration/provider/aliyun_global/LB"
	aliyun_global_nas "cloud-asset-migration/provider/aliyun_global/NAS"
	aliyun_global_nat "cloud-asset-migration/provider/aliyun_global/NAT"
	aliyun_global_oss "cloud-asset-migration/provider/aliyun_global/OSS"
	aliyun_global_pc "cloud-asset-migration/provider/aliyun_global/PeerConnection"
	aliyun_global_ram "cloud-asset-migration/provider/aliyun_global/RAM"
	aliyun_global_rds "cloud-asset-migration/provider/aliyun_global/RDS"
	aliyun_global_redis "cloud-asset-migration/provider/aliyun_global/Redis"
	aliyun_global_sls "cloud-asset-migration/provider/aliyun_global/SLS"
	aliyun_global_vpc "cloud-asset-migration/provider/aliyun_global/VPC"
	aws_auditlog "cloud-asset-migration/provider/aws/AuditLog"
	aws_cfw "cloud-asset-migration/provider/aws/CFW"
	aws_ec2 "cloud-asset-migration/provider/aws/EC2"
	aws_efs "cloud-asset-migration/provider/aws/EFS"
	aws_es "cloud-asset-migration/provider/aws/ES"
	aws_iam "cloud-asset-migration/provider/aws/IAM"
	aws_k8s "cloud-asset-migration/provider/aws/K8S"
	aws_kms "cloud-asset-migration/provider/aws/KMS"
	aws_lb "cloud-asset-migration/provider/aws/LB"
	aws_lambda "cloud-asset-migration/provider/aws/Lambda"
	aws_nat "cloud-asset-migration/provider/aws/NAT"
	aws_rds "cloud-asset-migration/provider/aws/RDS"
	aws_s3 "cloud-asset-migration/provider/aws/S3"
	aws_vpc "cloud-asset-migration/provider/aws/VPC"
	az_iam "cloud-asset-migration/provider/azure/IAM"
	az_k8s "cloud-asset-migration/provider/azure/K8S"
	az_lb "cloud-asset-migration/provider/azure/LB"
	az_rds "cloud-asset-migration/provider/azure/RDS"
	az_sg "cloud-asset-migration/provider/azure/SG"
	az_vm "cloud-asset-migration/provider/azure/VM"
	baidu_auditlog "cloud-asset-migration/provider/baidu/AuditLog"
	baidu_bbc "cloud-asset-migration/provider/baidu/BBC"
	baidu_bec "cloud-asset-migration/provider/baidu/BEC"
	baidu_blb "cloud-asset-migration/provider/baidu/BLB"
	baidu_bos "cloud-asset-migration/provider/baidu/BOS"
	baidu_cdn "cloud-asset-migration/provider/baidu/CDN"
	baidu_cfs "cloud-asset-migration/provider/baidu/CFS"
	baidu_cfw "cloud-asset-migration/provider/baidu/CFW"
	baidu_csn "cloud-asset-migration/provider/baidu/CSN"
	baidu_ecs "cloud-asset-migration/provider/baidu/ECS"
	baidu_eip "cloud-asset-migration/provider/baidu/EIP"
	baidu_es "cloud-asset-migration/provider/baidu/ES"
	baidu_cfc "cloud-asset-migration/provider/baidu/FC"
	baidu_iam "cloud-asset-migration/provider/baidu/IAM"
	baidu_k8s "cloud-asset-migration/provider/baidu/K8S"
	baidu_mq "cloud-asset-migration/provider/baidu/MQ"
	baidu_nat "cloud-asset-migration/provider/baidu/NAT"
	baidu_rds "cloud-asset-migration/provider/baidu/RDS"
	baidu_vpc "cloud-asset-migration/provider/baidu/VPC"
	feishu_app "cloud-asset-migration/provider/feishu/application"
	lx_apollo "cloud-asset-migration/provider/lixiang/Apollo"
	lx_itserver "cloud-asset-migration/provider/lixiang/IT-Server"
	lx_k8s "cloud-asset-migration/provider/lixiang/K8S"
	lx_ngfw "cloud-asset-migration/provider/lixiang/Paloalto-NGFW"
	lx_tm "cloud-asset-migration/provider/lixiang/TrafficMirror"
	volcengine_auditlog "cloud-asset-migration/provider/volcengine/AuditLog"
	volcengine_cdn "cloud-asset-migration/provider/volcengine/CDN"
	volcengine_cen "cloud-asset-migration/provider/volcengine/Cen"
	volcengine_ecs "cloud-asset-migration/provider/volcengine/ECS"
	volcengine_es "cloud-asset-migration/provider/volcengine/ES"
	volcengine_faas "cloud-asset-migration/provider/volcengine/FaaS"
	volcengine_iam "cloud-asset-migration/provider/volcengine/IAM"
	volcengine_k8s "cloud-asset-migration/provider/volcengine/K8S"
	volcengine_lb "cloud-asset-migration/provider/volcengine/LB"
	volcengine_nas "cloud-asset-migration/provider/volcengine/NAS"
	volcengine_nat "cloud-asset-migration/provider/volcengine/NAT"
	volcengine_rds "cloud-asset-migration/provider/volcengine/RDS"
	volcengine_redis "cloud-asset-migration/provider/volcengine/Redis"
	volcengine_tls "cloud-asset-migration/provider/volcengine/TLS"
	volcengine_tos "cloud-asset-migration/provider/volcengine/TOS"
	volcengine_veen "cloud-asset-migration/provider/volcengine/VEEN"
	volcengine_vpc "cloud-asset-migration/provider/volcengine/VPC"
)

type CronTask struct {
	F        func()
	CronExpr *string
	DevMode  bool
}

var (
	tasks = []CronTask{
		{F: aliyun_ecs.EcsMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: aws_ec2.Ec2Main, CronExpr: &config.CronExpr, DevMode: false},
		{F: baidu_ecs.EcsMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_ecs.EcsMain, CronExpr: &config.CronExpr},

		{F: aliyun_ram.RamMain, CronExpr: &config.CronExpr},
		{F: aws_iam.IamMain, CronExpr: &config.CronExpr},
		{F: baidu_iam.IamMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_iam.IamMain, CronExpr: &config.CronExpr},

		{F: aliyun_oss.OssMain, CronExpr: &config.CronExpr},
		{F: aws_s3.S3Main, CronExpr: &config.CronExpr},
		{F: baidu_bos.BosMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_tos.TosMain, CronExpr: &config.CronExpr},

		{F: aliyun_rds.RdsMain, CronExpr: &config.CronExpr},
		{F: aws_rds.RdsMain, CronExpr: &config.CronExpr},
		{F: baidu_rds.RdsMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_rds.RdsMain, CronExpr: &config.CronExpr},

		{F: aliyun_nat.NatMain, CronExpr: &config.CronExpr},
		{F: aws_nat.NatMain, CronExpr: &config.CronExpr},
		{F: baidu_nat.NatMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_nat.NatMain, CronExpr: &config.CronExpr},

		{F: aliyun_vpc.VpcMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: aws_vpc.VpcMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: baidu_vpc.VpcMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_vpc.VpcMain, CronExpr: &config.CronExpr, DevMode: false},

		{F: aliyun_es.EsMain, CronExpr: &config.CronExpr},
		{F: aws_es.EsMain, CronExpr: &config.CronExpr},
		{F: baidu_es.EsMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_es.EsMain, CronExpr: &config.CronExpr},

		{F: aliyun_cfw.CfwMain, CronExpr: &config.CronExpr},
		{F: aws_cfw.CfwMain, CronExpr: &config.CronExpr},
		{F: baidu_cfw.CfwMain, CronExpr: &config.CronExpr, DevMode: false},

		{F: aliyun_lb.LbMain, CronExpr: &config.CronExpr},
		{F: aws_lb.LbMain, CronExpr: &config.CronExpr},
		{F: baidu_blb.BlbMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_lb.LbMain, CronExpr: &config.CronExpr},

		{F: az_vm.VmMain, CronExpr: &config.CronExpr},
		{F: az_sg.SgMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: az_lb.LbMain, CronExpr: &config.CronExpr},
		{F: az_rds.RdsMain, CronExpr: &config.CronExpr},
		{F: az_iam.IamMain, CronExpr: &config.CronExpr},

		{F: aliyun_k8s.K8sMain, CronExpr: &config.CronExpr},
		{F: aws_k8s.K8sMain, CronExpr: &config.CronExpr},
		{F: az_k8s.K8sMain, CronExpr: &config.CronExpr},
		{F: baidu_k8s.K8sMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_k8s.K8sMain, CronExpr: &config.CronExpr},

		{F: lx_tm.TmMain, CronExpr: &config.CronExpr},
		{F: lx_k8s.K8sMain, CronExpr: &config.CronExpr},
		{F: lx_apollo.ApolloMain, CronExpr: &config.CronExpr},
		{F: lx_itserver.ItserverMain, CronExpr: &config.CronExpr},

		{F: aliyun_cdn.CdnMain, CronExpr: &config.CronExpr},
		{F: baidu_cdn.CdnMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_cdn.CdnMain, CronExpr: &config.CronExpr},

		{F: aliyun_nas.NasMain, CronExpr: &config.CronExpr},
		{F: aws_efs.EfsMain, CronExpr: &config.CronExpr},
		{F: baidu_cfs.CfsMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_nas.NasMain, CronExpr: &config.CronExpr},

		{F: aliyun_api.ApiMain, CronExpr: &config.CronExpr},
		{F: aws_kms.KmsMain, CronExpr: &config.CronExpr},

		{F: aliyun_pc.PeerConnectionMain, CronExpr: &config.CronExpr},
		{F: aliyun_cen.CenMain, CronExpr: &config.CronExpr},
		{F: baidu_csn.CsnMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_cen.CenMain, CronExpr: &config.CronExpr},

		{F: baidu_bec.BecMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_veen.VeenMain, CronExpr: &config.CronExpr, DevMode: false},

		{F: aliyun_redis.RedisMain, CronExpr: &config.CronExpr},
		{F: volcengine_redis.RedisMain, CronExpr: &config.CronExpr},

		{F: baidu_eip.EipMain, CronExpr: &config.CronExpr, DevMode: false},

		{F: feishu_app.AppMain, CronExpr: &config.CronExpr},
		{F: baidu_bbc.BbcMain, CronExpr: &config.CronExpr, DevMode: false},

		{F: aliyun_fc.FcMain, CronExpr: &config.CronExpr},
		{F: baidu_cfc.CfcMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_faas.FcMain, CronExpr: &config.CronExpr},
		{F: aws_lambda.LambdaMain, CronExpr: &config.CronExpr},

		{F: lx_ngfw.NGFWMain, CronExpr: &config.CronExpr},

		{F: aliyun_sls.SlsMain, CronExpr: &config.CronExpr},
		{F: volcengine_tls.TlsMain, CronExpr: &config.CronExpr},

		{F: aliyun_auditlog.AuditLogMain, CronExpr: &config.CronExpr},
		{F: aws_auditlog.AuditLogMain, CronExpr: &config.CronExpr},
		{F: baidu_auditlog.AuditLogMain, CronExpr: &config.CronExpr, DevMode: false},
		{F: volcengine_auditlog.AuditLogMain, CronExpr: &config.CronExpr},

		{F: baidu_mq.MqMain, CronExpr: &config.CronExpr, DevMode: false},

		{F: aliyun_global_api.ApiMain, CronExpr: &config.CronExpr, DevMode: true},
		// {F: aliyun_global_auditlog.AuditLogMain, CronExpr: &config.CronExpr},
		{F: aliyun_global_cdn.CdnMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_cen.CenMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_cfw.CfwMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_ecs.EcsMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_es.EsMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_fc.FcMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_k8s.K8sMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_lb.LbMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_nas.NasMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_nat.NatMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_oss.OssMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_pc.PeerConnectionMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_ram.RamMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_rds.RdsMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_redis.RedisMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_sls.SlsMain, CronExpr: &config.CronExpr, DevMode: true},
		{F: aliyun_global_vpc.VpcMain, CronExpr: &config.CronExpr, DevMode: true},
	}
)
