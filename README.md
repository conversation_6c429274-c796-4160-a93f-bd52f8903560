### **LLM Prompt for Generating the Cloud Disk Vulnerability Scanner**

**Role:** You are an expert Go developer with extensive experience in building robust, scalable, and cloud-native distributed systems. You are proficient with multi-cloud environments (AWS, GCP), message queues, and writing clean, maintainable, and testable code.

**Project Goal:**
I need you to generate the complete Go source code for a project named **"VulcanScan"**. The purpose of VulcanScan is to perform offline vulnerability scanning of cloud virtual machine disks across multiple cloud providers. The core workflow is to create a snapshot of a target disk, create a new disk from that snapshot, attach it to a dedicated scanner VM, run a vulnerability scan using tools like Trivy or Grype, and then clean up all temporary resources.

**Core Architectural Principles & Constraints:**

1.  **Message-Driven & Asynchronous:** The system is composed of decoupled services that communicate via message queues.
2.  **Partitioned Workers:** A critical constraint is that a disk can only be attached to a VM within the same cloud provider and region. Therefore, we will have dedicated "Scanner Worker" VMs deployed in each cloud provider/region we want to support (e.g., an EC2 instance in AWS `us-east-1` scans disks in that region).
3.  **Cloud Abstraction:** All cloud-specific operations (like creating snapshots or disks) must be behind a common Go interface (`ICloudProvider`) to easily support new cloud providers in the future.
4.  **Robust Cleanup:** The system must be resilient. It must guarantee the cleanup of temporary resources (snapshots, disks) even if the scanning process fails. The use of `defer` in Go is essential for this.
5.  **Configuration-Driven:** The behavior of each service should be controlled by YAML configuration files and environment variables.

---

### **System Components & Workflow**

1.  **Orchestrator:** A central service that reads a list of target VMs/disks from a source (e.g., a database, to be mocked for now). It creates a `Task` message for each disk.
2.  **Task Queues:** The Orchestrator pushes tasks to provider-specific queues. For example, a task for a disk in AWS `us-east-1` goes to a queue named `vulcan-tasks-aws-us-east-1`.
3.  **Scanner Worker:** A service running on a VM within a specific cloud/region. It listens _only_ to its corresponding task queue.
4.  **Worker's Process:**
    - Pulls a `Task` message.
    - Uses the Cloud Abstraction Layer to perform:
      a. `CreateSnapshot`
      b. `CreateDiskFromSnapshot`
      c. `AttachDisk` to itself.
    - Mounts the new disk's filesystem.
    - Uses a Scanner Abstraction Layer to run Trivy/Grype on the mounted path.
    - Pushes the JSON scan result to a unified, global `Result Queue`.
    - **Crucially, performs cleanup in reverse order:** unmount, `DetachDisk`, `DeleteDisk`, `DeleteSnapshot`.
5.  **Result Processor:** A service that consumes from the `Result Queue`, parses the results, and stores them (e.g., logs to console or saves to a database).

---

### **Detailed Project Structure & Implementation Request**

Please generate the Go code for the following project structure. For each file, I will describe the required structs, interfaces, and logic.

```
vulcan-scan/
├── cmd/
│   ├── orchestrator/main.go
│   └── scanner-worker/main.go
├── internal/
│   ├── cloud/
│   │   ├── provider.go       # Interface definition
│   │   ├── factory.go        # Factory to get provider instance
│   │   └── aws/provider.go   # AWS implementation
│   ├── config/
│   │   └── config.go
│   ├── queue/
│   │   └── queue.go          # Mock queue for now
│   ├── scanner/
│   │   ├── scanner.go        # Interface definition
│   │   └── trivy.go          # Trivy implementation
│   ├── types/
│   │   ├── task.go
│   │   └── result.go
│   └── worker/
│       └── processor.go
├── configs/
│   └── worker.yaml
└── go.mod
```

---

### **File-by-File Implementation Details:**

**1. `internal/types/task.go`**

- Define a `Task` struct.
- It must include fields like `TaskID` (string), `HostID` (string), `DiskID` (string), `CloudProvider` (string, e.g., "aws"), and `Region` (string).
- Use JSON struct tags for serialization.

**2. `internal/types/result.go`**

- Define a `ScanResult` struct.
- This struct will hold the parsed vulnerability data. For now, a simple structure is fine. It should contain the original `TaskID` and a field `RawJSON` (string) to hold the raw JSON output from the scanner.

**3. `internal/cloud/provider.go`**

- Define the core `ICloudProvider` interface.
- It must have the following methods. Pay close attention to the signatures:
  ```go
  type ICloudProvider interface {
      CreateSnapshot(diskID string) (snapshotID string, err error)
      CreateDiskFromSnapshot(snapshotID, zone string) (diskID string, err error)
      AttachDisk(instanceID, diskID string) (devicePath string, err error)
      DetachDisk(instanceID, diskID string) error
      DeleteDisk(diskID string) error
      DeleteSnapshot(snapshotID string) error
  }
  ```

**4. `internal/cloud/aws/provider.go`**

- Create a struct `AWSProvider` that implements the `ICloudProvider` interface.
- For the implementation, **use comments as placeholders for the actual AWS Go SDK v2 calls**. You don't need to write the full SDK logic, but show where the calls would be made.
- Example for a method:
  ```go
  func (p *AWSProvider) CreateSnapshot(diskID string) (string, error) {
      log.Printf("AWS_SDK_CALL: Creating snapshot for disk %s", diskID)
      // Placeholder for ec2.CreateSnapshotInput and client.CreateSnapshot() call
      // On success, would return the new snapshot ID.
      return "snap-" + uuid.NewString(), nil // Return a mock ID
  }
  ```
- Implement all interface methods with similar mock logic and logging statements.

**5. `internal/cloud/factory.go`**

- Create a function `GetProvider(providerName string) (ICloudProvider, error)`.
- It should use a `switch` statement on `providerName`.
- If `providerName` is "aws", it should return a new instance of `aws.AWSProvider`.
- Return an error for any unsupported provider name.

**6. `internal/scanner/scanner.go`**

- Define an `IScanner` interface with one method: `ScanFS(path string) (string, error)`. It should return the raw JSON output as a string.

**7. `internal/scanner/trivy.go`**

- Create a struct `TrivyScanner` that implements `IScanner`.
- The `ScanFS` method should use the `os/exec` package to run the command: `trivy fs --format json --quiet [path]`.
- It should capture the command's stdout (the JSON result). If the command fails, it should return an error.

**8. `internal/worker/processor.go`**

- This is the most critical piece. Create a `TaskProcessor` struct.
- It should have a method `ProcessTask(task *types.Task) (*types.ScanResult, error)`.
- **Implement the full logic with robust cleanup using `defer`**.
- The steps inside the method are:
  1.  Log the start of processing for `task.TaskID`.
  2.  Use the `cloud.GetProvider` factory to get the correct provider instance.
  3.  Call `provider.CreateSnapshot`. Handle errors.
  4.  **`defer provider.DeleteSnapshot(snapshotID)`** immediately after a successful creation.
  5.  Call `provider.CreateDiskFromSnapshot`. Handle errors.
  6.  **`defer provider.DeleteDisk(diskID)`** immediately after a successful creation.
  7.  Call `provider.AttachDisk`. Handle errors.
  8.  **`defer provider.DetachDisk(instanceID, diskID)`** immediately after a successful creation.
  9.  Simulate mounting: Log "Mounting device [devicePath] to /mnt/scan_target".
  10. **`defer log.Println("Unmounting /mnt/scan_target")`**.
  11. Create an instance of `scanner.TrivyScanner`.
  12. Call `scanner.ScanFS("/mnt/scan_target")` to get the raw JSON result. Handle errors.
  13. Create a `types.ScanResult` object, populate it, and return it.
  14. If any step fails, the function should return an error, and the `defer` statements will ensure cleanup happens automatically.

**9. `internal/queue/queue.go`**

- For this prompt, create a simple in-memory mock queue.
- Define a `MockTaskQueue` struct with a channel: `Tasks chan *types.Task`.
- Implement `Publish(task *types.Task)` and `Consume() *types.Task` methods.

**10. `internal/config/config.go`**

- Define a `Config` struct for the worker. It should include `CloudProvider` (string), `Region` (string), `Zone` (string), and `InstanceID` (string).
- Create a `LoadConfig(path string) (*Config, error)` function that uses the `gopkg.in/yaml.v3` library to unmarshal the config file at the given path into the `Config` struct.

**11. `cmd/scanner-worker/main.go`**

- This is the entry point for the worker service.
- It should:
  1.  Load the configuration from `configs/worker.yaml` using your `config.LoadConfig` function.
  2.  Create a `worker.TaskProcessor`.
  3.  (For demonstration) Create a sample `types.Task` that matches the config (e.g., provider "aws").
  4.  Call `processor.ProcessTask` with the sample task.
  5.  Log the result or the error.

**12. `cmd/orchestrator/main.go`**

- Keep this very simple for now.
- It should:
  1.  Create a `MockTaskQueue`.
  2.  Create a few sample `types.Task` objects for different providers (e.g., one for "aws", one for "gcp").
  3.  Publish them to the mock queue.
  4.  Log that the tasks have been published.

**13. `configs/worker.yaml`**

- Create a sample YAML file with the following content:
  ```yaml
  cloud_provider: "aws"
  region: "us-east-1"
  zone: "us-east-1a"
  instance_id: "i-1234567890abcdef0"
  ```

**14. `go.mod`**

- Please generate a `go.mod` file, declaring the module `disk_scanner` and including dependencies like `gopkg.in/yaml.v3` and `github.com/google/uuid`.

Please generate all the code based on these detailed instructions. Ensure the code is clean, well-commented, and follows standard Go idioms.
