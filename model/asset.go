package model

type Asset map[string]any

type KV struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ObjectMeta struct {
	Provider       string `json:"provider"`
	OriginalLabels []KV   `json:"-"`
	OriginalID     string `json:"original_id"`
	OriginalObject any    `json:"original_object"`
	Region         string `json:"region"`
	UID            string `json:"uid"`
	UpdateTime     int64  `json:"update_time"`
	Description    string `json:"description"`
	Kind           string `json:"kind"`
	Category       string `json:"category"`
	Name           string `json:"name"`
}

type Eip struct {
	IP        string `json:"ip"`
	Status    string `json:"status"`
	ECSID     string `json:"ecs_id"`
	ENIID     string `json:"eni_id"`
	LBID      string `json:"lb_id"`
	NATID     string `json:"nat_id"`
	Bandwidth int64  `json:"bandwidth"`
	ISP       string `json:"isp"`
}

type LB struct {
	Class            string     `json:"class"`
	PrivateIPList    []string   `json:"private_ip_list"`
	PublicIPList     []string   `json:"public_ip_list"`
	Status           string     `json:"status"`
	VPCID            string     `json:"vpc_id"`
	ListenerIDList   []string   `json:"listener_id_list"`
	SGIDList         []string   `json:"sg_id_list"`
	Servers          []LbServer `json:"servers"`
	DeleteProtection *bool      `json:"delete_protection"`
}

type LbServer struct {
	OriginalID string `json:"original_id"`
	Uid        string `json:"uid"`
	// ecs/ip/eni
	Class  string `json:"class"`
	IP     string `json:"ip"`
	Port   int    `json:"port"`
	Weight int    `json:"weight"`
}

type LbListener struct {
	LbID               string `json:"lb_id"`
	LbOriginalID       string `json:"lb_original_id"`
	Status             string `json:"status"`
	Protocol           string `json:"protocol"`
	Port               int    `json:"port"`
	PortStart          int    `json:"port_start"`
	PortEnd            int    `json:"port_end"`
	HealthCheckEnabled *bool  `json:"health_check_enabled"`
	CertExists         *bool  `json:"cert_exists"`
	AclStatus          string `json:"acl_status"`
	AclType            string `json:"acl_type"`
	Authorized         *bool  `json:"authorized"`
}

type SG struct {
	VPCID     string   `json:"vpc_id"`
	IsDefault bool     `json:"is_default"`
	Rules     []SGRule `json:"rules"`
}

type SGRule struct {
	Description string `json:"description"`
	Protocol    string `json:"protocol"`
	Policy      string `json:"policy"`
	Priority    int    `json:"priority"`
	Direction   string `json:"direction"`
	PeerSGID    string `json:"peer_sg_id"`
	PeerCIDR    string `json:"peer_cidr"`
	PortStart   int    `json:"port_start"`
	PortEnd     int    `json:"port_end"`
}

type ECS struct {
	Hostname         string   `json:"hostname"`
	Status           string   `json:"status"`
	PrimaryPrivateIP string   `json:"primary_private_ip"`
	PrimaryPublicIP  string   `json:"primary_public_ip"`
	PrivateIPList    []string `json:"private_ip_list"`
	PublicIPList     []string `json:"public_ip_list"`
	Spec             string   `json:"spec"`
	SGIDList         []string `json:"sg_id_list"`
	VPCID            string   `json:"vpc_id"`
	ENIIDList        []string `json:"eni_id_list"`
	OSName           string   `json:"os_name"`
	OSType           string   `json:"os_type"`
	DeleteProtection *bool    `json:"delete_protection"`
	// cvm/edge
	Class string `json:"class"`
}

type ENI struct {
	VPCID         string   `json:"vpc_id"`
	SubnetID      string   `json:"subnet_id"`
	MacAddress    string   `json:"mac_address"`
	ECSID         string   `json:"ecs_id"`
	Status        string   `json:"status"`
	SGIDList      []string `json:"sg_id_list"`
	PrivateIPList []string `json:"private_ip_list"`
	PublicIPList  []string `json:"public_ip_list"`
	Class         string   `json:"class"`
	PrimaryIP     string   `json:"primary_ip"`
	EIPIDList     []string `json:"eip_id_list"`
}
type User struct {
	DisplayName     string          `json:"display_name"`
	PolicyIDList    []string        `json:"policy_id_list"`
	AccessKeyIDList []string        `json:"access_key_id_list"`
	MFAEnabled      bool            `json:"mfa_enabled"`
	LoginAllowed    bool            `json:"login_allowed"`
	CreatedAt       int64           `json:"created_at"`
	LastLoginAt     int64           `json:"last_login_at"`
	Enabled         bool            `json:"enabled"`
	PasswordPolicy  *PasswordPolicy `json:"password_policy"`
}

type PasswordPolicy struct {
	HardExpiry                 bool `json:"hard_expiry"`
	MaxPasswordAge             int  `json:"max_password_age"`
	MinimumPasswordLength      int  `json:"minimum_password_length"`
	PasswordReusePrevention    int  `json:"password_reuse_prevention"`
	RequireSymbols             bool `json:"require_symbols"`
	RequireNumbers             bool `json:"require_numbers"`
	RequireUppercaseCharacters bool `json:"require_uppercase_characters"`
	RequireLowercaseCharacters bool `json:"require_lowercase_characters"`
}

type Policy struct {
	// managed/custom/inline
	Class          string           `json:"class"`
	PolicyDocument []PolicyDocument `json:"policy_document"`
}

type PolicyDocument struct {
	Effect    string   `json:"effect"`
	Action    []string `json:"action"`
	Resource  []string `json:"resource"`
	Condition string   `json:"condition"`
	Principal string   `json:"principal"`
}

type AK struct {
	UserID     string `json:"user_id"`
	UserName   string `json:"user_name"`
	Enabled    *bool  `json:"enabled"`
	CreatedAt  *int64 `json:"created_at"`
	LastUsedAt *int64 `json:"last_used_at"`
}
type Role struct {
	MaxSessionDuration  *int     `json:"max_session_duration"`
	PolicyIDList        []string `json:"policy_id_list"`
	AssumeRolePrincipal []string `json:"assume_role_principal"`
}
type RDS struct {
	Engine            string       `json:"engine"`
	EngineVersion     string       `json:"engine_version"`
	ConnectionAddress string       `json:"connection_address"`
	VPCID             string       `json:"vpc_id"`
	PublicAllowed     bool         `json:"public_allowed"`
	IpWhiteList       []string     `json:"ip_white_list"`
	SGIDList          []string     `json:"sg_id_list"`
	Accounts          []RDSAccount `json:"accounts"`
	TDEEnabled        *bool        `json:"tde_enabled"`
	BackupAvailable   bool         `json:"backup_available"`
	// auto/manual/none/na
	BackupMethod   string `json:"backup_method"`
	LastBackupTime int64  `json:"last_backup_time"`
	LogFileExists  bool   `json:"log_file_exists"`
}

type RDSAccount struct {
	Name    string `json:"name"`
	Enabled bool   `json:"enabled"`
	// admin/user
	Class       string `json:"class"`
	Host        string `json:"host"`
	Description string `json:"description"`
}

type Redis struct {
	Engine            string         `json:"engine"`
	EngineVersion     string         `json:"engine_version"`
	ConnectionAddress string         `json:"connection_address"`
	VPCID             string         `json:"vpc_id"`
	SubnetID          string         `json:"subnet_id"`
	PublicAllowed     bool           `json:"public_allowed"`
	IpWhiteList       []string       `json:"ip_white_list"`
	SGIDList          []string       `json:"sg_id_list"`
	Accounts          []RedisAccount `json:"accounts"`
	DeleteProtection  *bool          `json:"delete_protection"`
	TDEEnabled        *bool          `json:"tde_enabled"`
}

type RedisAccount struct {
	Name    string `json:"name"`
	Enabled bool   `json:"enabled"`
	// admin/user
	Class       string `json:"class"`
	Description string `json:"description"`
}

type NAT struct {
	VPCID     string     `json:"vpcid"`
	SubnetID  string     `json:"subnet_id"`
	Status    string     `json:"status"`
	Spec      string     `json:"spec"`
	EipIDList []string   `json:"eip_id_list"`
	SNATRules []SNATRule `json:"snat_rules"`
	DNATRules []DNATRule `json:"dnat_rules"`
}

type SNATRule struct {
	RuleID string   `json:"rule_id"`
	Name   string   `json:"name"`
	Status string   `json:"status"`
	CIDR   string   `json:"cidr"`
	Eip    []string `json:"eip"`
}

type DNATRule struct {
	RuleID       string `json:"rule_id"`
	Name         string `json:"name"`
	Status       string `json:"status"`
	Protocol     string `json:"protocol"`
	InternalIP   string `json:"internal_ip"`
	InternalPort int    `json:"internal_port"`
	ExternalIP   string `json:"external_ip"`
	ExternalPort int    `json:"external_port"`
}

type BucketCORSRule struct {
	AllowedOrigins []string `json:"allowed_origins"`
	AllowedMethods []string `json:"allowed_methods"`
	AllowedHeaders []string `json:"allowed_headers"`
	ExposeHeaders  []string `json:"expose_headers"`
	MaxAgeSeconds  int      `json:"max_age_seconds"`
}

type BucketReferer struct {
	AllowEmptyReferer        bool     `json:"allow_empty_referer"`
	AllowTruncateQueryString bool     `json:"allow_truncate_query_string"`
	TruncatePath             bool     `json:"truncate_path"`
	RefererWhiteList         []string `json:"referer_white_list"`
	RefererBlackList         []string `json:"referer_black_list"`
}

type BucketLogging struct {
	Enabled bool `json:"enabled"`
}

type BucketEncryption struct {
	Enabled   bool   `json:"enabled"`
	Algorithm string `json:"algorithm"`
}

type Bucket struct {
	PrivateEndpoint string           `json:"private_endpoint"`
	PublicEndpoint  string           `json:"public_endpoint"`
	Policy          []PolicyDocument `json:"policy"`
	ACL             []string         `json:"acl"`
	CORS            []BucketCORSRule `json:"cors"`
	Referer         *BucketReferer   `json:"referer"`
	Logging         BucketLogging    `json:"logging"`
	Encryption      BucketEncryption `json:"encryption"`
}

type VPC struct {
	IsDefault      bool     `json:"is_default"`
	CIDR           string   `json:"cidr"`
	CIDRv6         string   `json:"cidr_v6"`
	SecondaryCIDRs []string `json:"secondary_cidr"`
	SubnetIDList   []string `json:"subnet_id_list"`
}

type Subnet struct {
	IsDefault        bool      `json:"is_default"`
	CIDR             string    `json:"cidr"`
	CIDRv6           string    `json:"cidr_v6"`
	VPCID            string    `json:"vpc_id"`
	AvailableIPCount int       `json:"available_ip_count"`
	ACL              []ACLRule `json:"acl"`
}

type ACLRule struct {
	RuleID      string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Protocol    string `json:"protocol"`
	IPVersion   string `json:"ip_version"`
	Policy      string `json:"policy"`
	Priority    int    `json:"priority"`
	Direction   string `json:"direction"`
	PeerCIDR    string `json:"peer_cidr"`
	PortStart   int    `json:"port_start"`
	PortEnd     int    `json:"port_end"`
}

type ES struct {
	Status                string   `json:"status"`
	EIPIDList             []string `json:"eip_id_list"`
	PublicEndpoint        string   `json:"public_endpoint"`
	PrivateEndpoint       string   `json:"private_endpoint"`
	IPWhiteList           []string `json:"ip_white_list"`
	IPBlackList           []string `json:"ip_black_list"`
	SGIDList              []string `json:"sg_id_list"`
	VPCID                 string   `json:"vpc_id"`
	EngineVersion         string   `json:"engine_version"`
	PublicAllowed         bool     `json:"public_allowed"`
	Nodes                 []ESNode `json:"nodes"`
	KibanaPublicEndpoint  string   `json:"kibana_public_endpoint"`
	KibanaPrivateEndpoint string   `json:"kibana_private_endpoint"`
	KibanaProtocol        string   `json:"kibana_protocol"`
	KibanaIPWhiteList     []string `json:"kibana_ip_white_list"`
	KibanaIPBlackList     []string `json:"kibana_ip_black_list"`
	KibanaPublicAllowed   bool     `json:"kibana_public_allowed"`
}

type ESNode struct {
	NodeID string `json:"node_id"`
	Name   string `json:"name"`
	IP     string `json:"ip"`
	Class  string `json:"class"`
}

type CFW struct {
	Status            string              `json:"status"`
	Rules             []CFWRule           `json:"cfw_rules"`
	ProtectedResource []ProtectedResource `json:"protected_resource"`
}

type ProtectedResource struct {
	Class string `json:"class"`
	ID    string `json:"id"`
}

type CFWRule struct {
	RuleID          string      `json:"rule_id"`
	Description     string      `json:"description"`
	IPVersion       string      `json:"ip_version"`
	Priority        int         `json:"priority"`
	Protocol        []string    `json:"protocol"`
	Direction       string      `json:"direction"`
	SourceCidr      []string    `json:"source_cidr"`
	SourcePortRange []PortRange `json:"source_port_range"`
	DestCidr        []string    `json:"dest_cidr"`
	DestPortRange   []PortRange `json:"dest_port_range"`
	Policy          string      `json:"policy"`
}

type PortRange struct {
	PortStart int `json:"port_start"`
	PortEnd   int `json:"port_end"`
}

type K8S struct {
	Status          string   `json:"status"`
	EngineVersion   string   `json:"engine_version"`
	PrivateEndpoint string   `json:"private_endpoint"`
	PublicEndpoint  string   `json:"public_endpoint"`
	VPCID           string   `json:"vpc_id"`
	LBIDList        []string `json:"lb_id_list"`
	SubnetIDList    []string `json:"subnet_id_list"`
	IpWhiteList     []string `json:"ip_white_list"`
	SGIDList        []string `json:"sg_id_list"`
	PodCIDRs        []string `json:"pod_cidrs"`
	ServiceCIDRs    []string `json:"service_cidrs"`
	AuditLogEnabled *bool    `json:"audit_log_enabled"`
}

type Waf struct {
	Domain    *string `json:"domain"`
	Cname     *string `json:"cname"`
	IP        *string `json:"ip"`
	NodeGroup *string `json:"node_group"`
	Internal  *bool   `json:"internal"`
	Version   int     `json:"version"`
}

type EBS struct {
	Status string `json:"status"`
	// volume/snapshot/image
	Class               string `json:"class"`
	Encrypted           bool   `json:"encrypted"`
	AutoSnapshotEnabled *bool  `json:"auto_snapshot_enabled"`
	// used by volume
	ImageId string   `json:"image_id"`
	ECSIDs  []string `json:"ecs_ids"`
	// used by snapshot
	VolumeId string `json:"volume_id"`
}

type CDN struct {
	// running/stopped
	Status       string `json:"status"`
	HTTPSEnabled bool   `json:"https_enabled"`
	ForceHTTPS   bool   `json:"force_https"`
	// domestic/global/overseas
	Coverage   string       `json:"coverage"`
	CNAME      string       `json:"cname"`
	SourceSite []SourceSite `json:"source_site"`
	// web/download/video/dynamic/default/image
	Class                 string   `json:"class"`
	IPWhiteList           []string `json:"ip_white_list"`
	IPBlackList           []string `json:"ip_black_list"`
	RateLimitEnabled      bool     `json:"rate_limit_enabled"`
	FreqLimitEnabled      bool     `json:"freq_limit_enabled"`
	BandwidthLimitEnabled bool     `json:"bandwidth_limit_enabled"`

	AllowEmptyReferer bool     `json:"allow_empty_referer"`
	RefererWhiteList  []string `json:"referer_white_list"`
	RefererBlackList  []string `json:"referer_black_list"`
}

type SourceSite struct {
	URL    string `json:"url"`
	Weight int    `json:"weight"`
}

type NAS struct {
	ACLRules []NasACLRule `json:"acl_rules"`
	Status   string       `json:"status"`
	// nfs/smb/cpfs
	Class        string   `json:"class"`
	Encrypt      bool     `json:"encrypt"`
	Domains      []string `json:"domains"`
	IPs          []string `json:"ips"`
	VpcIdList    []string `json:"vpc_id_list"`
	SubnetIdList []string `json:"subnet_id_list"`
}

type NasACLRule struct {
	AclID      string `json:"acl_id"`
	Priority   int    `json:"priority"`
	SourceCidr string `json:"source_cidr"`
	RWAccess   string `json:"rw_access"`
	// no_squash/root_squash/all_squash
	UserAccess string `json:"user_access"`
}

type ApiGateway struct {
	// running/stopped
	Status string `json:"status"`
	// HTTPS1_1_TLS1_0/HTTPS2_TLS1_0/HTTPS2_TLS1_2
	HTTPSPolicies []string        `json:"https_policies"`
	ApiInfos      []ApiInfo       `json:"api_infos"`
	Acls          []ApiGatewayAcl `json:"acls"`
}

type ApiInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	// APP/APPOPENID/ANONYMOUS
	AuthType    string `json:"auth_type"`
	Description string `json:"description"`
}

type ApiGatewayAcl struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	// on/off
	Status string `json:"status"`
	// 4/6
	IPVersion int                 `json:"ip_version"`
	ACLRules  []ApiGatewayACLRule `json:"acl_rules"`
}

type ApiGatewayACLRule struct {
	IP          string `json:"ip"`
	Description string `json:"description"`
}

type LaunchTemplate struct{}

type KMS struct {
	Enabled                     bool  `json:"enabled"`
	CreatedAt                   int64 `json:"created_at"`
	DeletedAt                   int64 `json:"deleted_at"`
	PendingDeletionWindowInDays int   `json:"pending_deletion_window_in_days"`
}

type PeerConnection struct {
	Status    string   `json:"status"`
	VpcIdList []string `json:"vpc_id_list"`
}

type Cen struct {
	Status            string                `json:"status"`
	AttachedInstances []CenAttachedInstance `json:"attached_instances"`
	CenRouteEntries   []CenRouteEntry       `json:"cen_route_entries"`
}

type CenAttachedInstance struct {
	InstanceID   string `json:"instance_id"`
	InstanceUID  string `json:"instance_uid"`
	InstanceName string `json:"instance_name"`
	InstanceType string `json:"instance_type"`
}

type CenRouteEntry struct {
	SrcInstanceID   string `json:"src_instance_id"`
	SrcInstanceUID  string `json:"src_instance_uid"`
	NextInstanceID  string `json:"next_instance_id"`
	NextInstanceUID string `json:"next_instance_uid"`
	DstCidrBlock    string `json:"dst_cidr_block"`
	Status          string `json:"status"`
	// system/custom
	Class string `json:"class"`
}

type ApolloApp struct {
	OrgID      string         `json:"org_id"`
	OrgName    string         `json:"org_name"`
	OwnerName  string         `json:"owner_name"`
	OwnerEmail string         `json:"owner_email"`
	Envs       []ApolloAppEnv `json:"envs"`
}

type ApolloAppEnv struct {
	EnvName       string         `json:"env_name"`
	Cluster       string         `json:"cluster"`
	Namespace     string         `json:"namespace"`
	ConfigExists  bool           `json:"config_exists"`
	Encrypted     bool           `json:"encrypted"`
	Signed        bool           `json:"signed"`
	SensitiveConf map[string]any `json:"sensitive_conf"`
	ExistAk       bool           `json:"exist_ak"`
}

type Function struct {
	Runtime                     string   `json:"runtime"`
	DirectInternetAccessAllowed bool     `json:"direct_internet_access_allowed"`
	VpcId                       string   `json:"vpc_id"`
	SubnetIdList                []string `json:"subnet_id_list"`
	SGIdList                    []string `json:"sg_id_list"`
	LogEnabled                  bool     `json:"log_enabled"`
	LogPath                     string   `json:"log_path"`
	NasMountPoints              []string `json:"nas_mount_points"`
	BucketMountPoints           []string `json:"bucket_mount_points"`
}

type PaloaltoFirewall struct {
	Version              string            `json:"version"`
	Devices              []*PaloaltoDevice `json:"devices"`
	InternetRulesUpdated bool              `json:"internet_rules_updated"`
}

type PaloaltoDevice struct {
	Name   string          `json:"name"`
	Vsyses []*PaloaltoVsys `json:"vsyses"`
}

type PaloaltoVsys struct {
	Name  string          `json:"name"`
	Rules []*PaloaltoRule `json:"rules"`
}

type PaloaltoIPRange struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type PaloaltoPortRange struct {
	Start int `json:"start"`
	End   int `json:"end"`
}

type PaloaltoPortRangeWithProtocol struct {
	PaloaltoPortRange `json:",inline"`
	Protocol          string `json:"protocol"`
}

type PaloaltoRule struct {
	Name         string                          `json:"name"`
	Uuid         string                          `json:"uuid"`
	RuleType     string                          `json:"rule_type"`
	SrcAddresses []PaloaltoIPRange               `json:"src_addresses"`
	DstAddresses []PaloaltoIPRange               `json:"dst_addresses"`
	DstPorts     []PaloaltoPortRangeWithProtocol `json:"dst_ports"`
	SrcZones     []string                        `json:"src_zones"`
	DstZones     []string                        `json:"dst_zones"`
	Action       string                          `json:"action"`
	Applications []string                        `json:"applications"`
	Tag          string                          `json:"tag"`
}

type LogService struct {
	Project         string           `json:"project_name"`
	LogStoreName    string           `json:"log_store_name"`
	LogStoreConfigs []map[string]any `json:"log_store_configs"`
	PolicyDocuments []PolicyDocument `json:"policy_documents"`
}

type CloudServiceProvider struct {
	AuditLogStatus []AuditLogStatus `json:"audit_log_status"`
}

type AuditLogStatus struct {
	ServiceName  string `json:"service_name"`
	EventName    string `json:"event_name"`
	LastIngestAt int64  `json:"last_ingest_at"`
}

type Kafka struct {
	Status          string   `json:"status"`
	Version         string   `json:"version"`
	SGIDList        []string `json:"sg_id_list"`
	VPCID           string   `json:"vpc_id"`
	SubnetIDList    []string `json:"subnet_id_list"`
	EipIDList       []string `json:"eip_id_list"`
	NodeNum         int      `json:"node_num"`
	NodeSpec        string   `json:"node_spec"`
	PublicIPEnabled bool     `json:"public_ip_enabled"`
	// None/SASL_SCRAM/SASL_PLAIN/SSL
	Authentications []string `json:"authentications"`
}
