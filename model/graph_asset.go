package model

type BaseNode struct {
	UID       string `json:"uid,omitempty" graph:"uid"`
	TargetUID string `json:"-" graph:"target_uid"`
}

type KVGraph struct {
	BaseNode `graph:",squash"`
	Key      string `json:"key,omitempty" graph:"key"`
	Value    string `json:"value,omitempty" graph:"value"`
}

type ObjectMetaGraph struct {
	Provider          string     `json:"provider,omitempty" graph:"provider"`
	OriginalID        string     `json:"original_id,omitempty" graph:"original_id"`
	OriginalLabels    []*KVGraph `json:"original_labels,omitempty" graph:"-"`
	OriginalObject    string     `json:"original_object,omitempty" graph:"-"`
	TransformedObject string     `json:"transformed_object,omitempty" graph:"transformed_object"`
	Region            string     `json:"region,omitempty" graph:"region"`
	Category          string     `json:"category,omitempty" graph:"category"`
	Kind              string     `json:"kind,omitempty" graph:"kind"`
	Name              string     `json:"name,omitempty" graph:"name"`
	Description       string     `json:"description,omitempty" graph:"description"`
	LastSeen          int64      `json:"last_seen,omitempty" graph:"last_seen"`
}

type EipGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	IP              string      `json:"ip,omitempty" graph:"ip"`
	Status          string      `json:"status,omitempty" graph:"status"`
	Bandwidth       int         `json:"bandwidth,omitempty" graph:"bandwidth"`
	ISP             string      `json:"isp,omitempty" graph:"isp"`
	ECS             []*ECSGraph `json:"ecs,omitempty" graph:"-"`
	ENI             []*ENIGraph `json:"eni,omitempty" graph:"-"`
	LB              []*LBGraph  `json:"lb,omitempty" graph:"-"`
	NAT             []*NATGraph `json:"nat,omitempty" graph:"-"`
}

type AkGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Enabled         *bool        `json:"enabled,omitempty" graph:"enabled"`
	CreatedAt       int64        `json:"created_at,omitempty" graph:"created_at"`
	LastUsedAt      int64        `json:"last_used_at,omitempty" graph:"last_used_at"`
	User            []*UserGraph `json:"user,omitempty" graph:"-"`
}

type UserGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	DisplayName     string         `json:"display_name,omitempty" graph:"display_name"`
	Enabled         bool           `json:"enabled,omitempty" graph:"enabled"`
	MFAEnabled      bool           `json:"mfa_enabled,omitempty" graph:"mfa_enabled"`
	LoginAllowed    bool           `json:"login_allowed,omitempty" graph:"login_allowed"`
	CreatedAt       int64          `json:"created_at,omitempty" graph:"created_at"`
	LastLoginAt     int64          `json:"last_login_at,omitempty" graph:"last_login_at"`
	Policy          []*PolicyGraph `json:"policy,omitempty" graph:"-"`
	AccessKey       []*AkGraph     `json:"access_key,omitempty" graph:"-"`
}

type PolicyGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	// managed/custom/inline
	Class           string                  `json:"class,omitempty" graph:"class"`
	PolicyStatement []*PolicyStatementGraph `json:"policy_statement,omitempty" graph:"-"`
}

type PolicyStatementGraph struct {
	BaseNode  `graph:",squash"`
	Effect    string   `json:"effect,omitempty" graph:"effect"`
	Action    []string `json:"action,omitempty" graph:"action"`
	Resource  []string `json:"resource,omitempty" graph:"resource"`
	Condition string   `json:"condition,omitempty" graph:"condition"`
	Principal string   `json:"principal,omitempty" graph:"principal"`
}

type RoleGraph struct {
	BaseNode            `graph:",squash"`
	ObjectMetaGraph     `graph:",squash"`
	MaxSessionDuration  int            `json:"max_session_duration,omitempty" graph:"max_session_duration"`
	AssumeRolePrincipal []string       `json:"assume_role_principal,omitempty" graph:"assume_role_principal"`
	Policy              []*PolicyGraph `json:"policy,omitempty" graph:"-"`
}

type ECSGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	// cvm/edge
	Class    string `json:"class,omitempty" graph:"class"`
	Hostname string `json:"hostname,omitempty" graph:"hostname"`
	// running/stopped
	Status           string   `json:"status,omitempty" graph:"status"`
	PrimaryPrivateIP string   `json:"primary_private_ip,omitempty" graph:"primary_private_ip"`
	PrimaryPublicIP  string   `json:"primary_public_ip,omitempty" graph:"primary_public_ip"`
	PrivateIPList    []string `json:"private_ip_list,omitempty" graph:"private_ip_list"`
	PublicIPList     []string `json:"public_ip_list,omitempty" graph:"public_ip_list"`
	Spec             string   `json:"spec,omitempty" graph:"spec"`
	OSName           string   `json:"os_name,omitempty" graph:"os_name"`
	OSType           string   `json:"os_type,omitempty" graph:"os_type"`
	DeleteProtection *bool    `json:"delete_protection,omitempty" graph:"delete_protection"`

	VPC    []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Subnet []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
	SG     []*SGGraph     `json:"sg,omitempty" graph:"-"`
	ENI    []*ENIGraph    `json:"eni,omitempty" graph:"-"`
	Policy []*PolicyGraph `json:"policy,omitempty" graph:"-"`
}

type SGGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	IsDefault       bool           `json:"is_default,omitempty" graph:"is_default"`
	VPC             []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Rules           []*SGRuleGraph `json:"rules,omitempty" graph:"-"`
}

type SGRuleGraph struct {
	BaseNode     `graph:",squash"`
	RuleID       string            `json:"rule_id,omitempty" graph:"rule_id"`
	Description  string            `json:"description,omitempty" graph:"description"`
	Protocol     string            `json:"protocol,omitempty" graph:"protocol"`
	Policy       string            `json:"policy,omitempty" graph:"policy"`
	Priority     int               `json:"priority,omitempty" graph:"priority"`
	Direction    string            `json:"direction,omitempty" graph:"direction"`
	PeerSG       []*SGGraph        `json:"peer_sg,omitempty" graph:"-"`
	SrcIPRange   []*IPRangeGraph   `json:"src_ip_range,omitempty" graph:"-"`
	SrcPortRange []*PortRangeGraph `json:"src_port_range,omitempty" graph:"-"`
	DstIPRange   []*IPRangeGraph   `json:"dst_ip_range,omitempty" graph:"-"`
	DstPortRange []*PortRangeGraph `json:"dst_port_range,omitempty" graph:"-"`
}

type IPRangeGraph struct {
	BaseNode `graph:",squash"`
	Start    string `json:"start,omitempty" graph:"start"`
	End      string `json:"end,omitempty" graph:"end"`
}

type PortRangeGraph struct {
	BaseNode `graph:",squash"`
	Start    int `json:"start,omitempty" graph:"start"`
	End      int `json:"end,omitempty" graph:"end"`
}

type VPCGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	IsDefault       bool           `json:"is_default,omitempty" graph:"is_default"`
	CIDR            string         `json:"cidr,omitempty" graph:"cidr"`
	CIDRv6          string         `json:"cidr_v6,omitempty" graph:"cidr_v6"`
	SecondaryCIDRs  []string       `json:"secondary_cidrs,omitempty" graph:"secondary_cidrs"`
	Subnet          []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
}

type SubnetGraph struct {
	BaseNode         `graph:",squash"`
	ObjectMetaGraph  `graph:",squash"`
	IsDefault        bool            `json:"is_default,omitempty" graph:"is_default"`
	CIDR             string          `json:"cidr,omitempty" graph:"cidr"`
	CIDRv6           string          `json:"cidr_v6,omitempty" graph:"cidr_v6"`
	AvailableIPCount int             `json:"available_ip_count,omitempty" graph:"available_ip_count"`
	VPC              []*VPCGraph     `json:"vpc,omitempty" graph:"-"`
	ACL              []*ACLRuleGraph `json:"acl,omitempty" graph:"-"`
}

type ACLRuleGraph struct {
	BaseNode     `graph:",squash"`
	RuleID       string            `json:"rule_id,omitempty" graph:"rule_id"`
	Name         string            `json:"name,omitempty" graph:"name"`
	Description  string            `json:"description,omitempty" graph:"description"`
	Protocol     string            `json:"protocol,omitempty" graph:"protocol"`
	IPVersion    string            `json:"ip_version,omitempty" graph:"ip_version"`
	Policy       string            `json:"policy,omitempty" graph:"policy"`
	Priority     int               `json:"priority,omitempty" graph:"priority"`
	Direction    string            `json:"direction,omitempty" graph:"direction"`
	SrcIPRange   []*IPRangeGraph   `json:"src_ip_range,omitempty" graph:"-"`
	SrcPortRange []*PortRangeGraph `json:"src_port_range,omitempty" graph:"-"`
	DstIPRange   []*IPRangeGraph   `json:"dst_ip_range,omitempty" graph:"-"`
	DstPortRange []*PortRangeGraph `json:"dst_port_range,omitempty" graph:"-"`
}

type LBGraph struct {
	BaseNode         `graph:",squash"`
	ObjectMetaGraph  `graph:",squash"`
	Class            string   `json:"class,omitempty" graph:"class"`
	PrivateIPList    []string `json:"private_ip_list,omitempty" graph:"private_ip_list"`
	PublicIPList     []string `json:"public_ip_list,omitempty" graph:"public_ip_list"`
	Status           string   `json:"status,omitempty" graph:"status"`
	DeleteProtection *bool    `json:"delete_protection,omitempty" graph:"delete_protection"`

	VPC     []*VPCGraph      `json:"vpc,omitempty" graph:"-"`
	Subnet  []*SubnetGraph   `json:"subnet,omitempty" graph:"-"`
	SG      []*SGGraph       `json:"sg,omitempty" graph:"-"`
	Servers []*LbServerGraph `json:"server,omitempty" graph:"-"`
}

type LbServerGraph struct {
	BaseNode   `graph:",squash"`
	InstanceID string `json:"instance_id,omitempty" graph:"instance_id"`
	// ecs/ip/eni
	Class  string `json:"class,omitempty" graph:"class"`
	IP     string `json:"ip,omitempty" graph:"ip"`
	Port   int    `json:"port,omitempty" graph:"port"`
	Weight int    `json:"weight,omitempty" graph:"weight"`
}

type LbListenerGraph struct {
	BaseNode           `graph:",squash"`
	ObjectMetaGraph    `graph:",squash"`
	Status             string            `json:"status,omitempty" graph:"status"`
	Protocol           string            `json:"protocol,omitempty" graph:"protocol"`
	HealthCheckEnabled *bool             `json:"health_check_enabled,omitempty" graph:"health_check_enabled"`
	CertExists         *bool             `json:"cert_exists,omitempty" graph:"cert_exists"`
	AclStatus          string            `json:"acl_status,omitempty" graph:"acl_status"`
	AclType            string            `json:"acl_type,omitempty" graph:"acl_type"`
	Authorized         *bool             `json:"authorized,omitempty" graph:"authorized"`
	PortRange          []*PortRangeGraph `json:"port_range,omitempty" graph:"-"`
	LB                 []*LBGraph        `json:"lb,omitempty" graph:"-"`
}

type ENIGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	MacAddress      string         `json:"mac_address,omitempty" graph:"mac_address"`
	Status          string         `json:"status,omitempty" graph:"status"`
	PrivateIPList   []string       `json:"private_ip_list,omitempty" graph:"private_ip_list"`
	PublicIPList    []string       `json:"public_ip_list,omitempty" graph:"public_ip_list"`
	Class           string         `json:"class,omitempty" graph:"class"`
	PrimaryIP       string         `json:"primary_ip,omitempty" graph:"primary_ip"`
	EIP             []*EipGraph    `json:"eip,omitempty" graph:"-"`
	VPC             []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Subnet          []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
	ECS             []*ECSGraph    `json:"ecs,omitempty" graph:"-"`
	SG              []*SGGraph     `json:"sg,omitempty" graph:"-"`
}

type NATGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Status          string `json:"status,omitempty" graph:"status"`
	Spec            string `json:"spec,omitempty" graph:"spec"`

	EIP       []*EipGraph      `json:"eip,omitempty" graph:"-"`
	SNATRules []*SNATRuleGraph `json:"snat_rule,omitempty" graph:"-"`
	DNATRules []*DNATRuleGraph `json:"dnat_rule,omitempty" graph:"-"`
	VPC       []*VPCGraph      `json:"vpc,omitempty" graph:"-"`
	Subnet    []*SubnetGraph   `json:"subnet,omitempty" graph:"-"`
}

type SNATRuleGraph struct {
	BaseNode `graph:",squash"`
	RuleID   string          `json:"rule_id,omitempty" graph:"rule_id"`
	Name     string          `json:"name,omitempty" graph:"name"`
	Status   string          `json:"status,omitempty" graph:"status"`
	IPRange  []*IPRangeGraph `json:"ip_range,omitempty" graph:"-"`
	EIP      []*EipGraph     `json:"eip,omitempty" graph:"-"`
}

type DNATRuleGraph struct {
	BaseNode     `graph:",squash"`
	RuleID       string `json:"rule_id,omitempty" graph:"rule_id"`
	Name         string `json:"name,omitempty" graph:"name"`
	Status       string `json:"status,omitempty" graph:"status"`
	Protocol     string `json:"protocol,omitempty" graph:"protocol"`
	InternalIP   string `json:"internal_ip,omitempty" graph:"internal_ip"`
	InternalPort int    `json:"internal_port,omitempty" graph:"internal_port"`
	ExternalIP   string `json:"external_ip,omitempty" graph:"external_ip"`
	ExternalPort int    `json:"external_port,omitempty" graph:"external_port"`
}

type BucketCORSRuleGraph struct {
	BaseNode       `graph:",squash"`
	AllowedOrigins []string `json:"allowed_origins,omitempty" graph:"allowed_origins"`
	AllowedMethods []string `json:"allowed_methods,omitempty" graph:"allowed_methods"`
	AllowedHeaders []string `json:"allowed_headers,omitempty" graph:"allowed_headers"`
	ExposeHeaders  []string `json:"expose_headers,omitempty" graph:"expose_headers"`
	MaxAgeSeconds  int      `json:"max_age_seconds,omitempty" graph:"max_age_seconds"`
}

type BucketGraph struct {
	BaseNode                 `graph:",squash"`
	ObjectMetaGraph          `graph:",squash"`
	PrivateEndpoint          string                  `json:"private_endpoint,omitempty" graph:"private_endpoint"`
	PublicEndpoint           string                  `json:"public_endpoint,omitempty" graph:"public_endpoint"`
	ACL                      []string                `json:"acl,omitempty" graph:"acl"`
	LoggingEnabled           bool                    `json:"logging_enabled,omitempty" graph:"logging_enabled"`
	EncryptionEnabled        bool                    `json:"encryption_enabled,omitempty" graph:"encryption_enabled"`
	EncryptionAlgorithm      string                  `json:"encryption_algorithm,omitempty" graph:"encryption_algorithm"`
	AllowEmptyReferer        bool                    `json:"allow_empty_referer,omitempty" graph:"allow_empty_referer"`
	AllowTruncateQueryString bool                    `json:"allow_truncate_query_string,omitempty" graph:"allow_truncate_query_string"`
	TruncatePath             bool                    `json:"truncate_path,omitempty" graph:"truncate_path"`
	RefererWhiteList         []string                `json:"referer_white_list,omitempty" graph:"referer_white_list"`
	RefererBlackList         []string                `json:"referer_black_list,omitempty" graph:"referer_black_list"`
	Policy                   []*PolicyStatementGraph `json:"policy,omitempty" graph:"-"`
	CORS                     []*BucketCORSRuleGraph  `json:"cors,omitempty" graph:"-"`
}

type PeerConnectionGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Status          string      `json:"status,omitempty" graph:"status"`
	FromVPC         []*VPCGraph `json:"from_vpc,omitempty" graph:"-"`
	ToVPC           []*VPCGraph `json:"to_vpc,omitempty" graph:"-"`
}

type CDNGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	// running/stopped
	Status       string `json:"status,omitempty" graph:"status"`
	HTTPSEnabled bool   `json:"https_enabled,omitempty" graph:"https_enabled"`
	ForceHTTPS   bool   `json:"force_https,omitempty" graph:"force_https"`
	// domestic/global/overseas
	Coverage string `json:"coverage,omitempty" graph:"coverage"`
	CNAME    string `json:"cname,omitempty" graph:"cname"`
	// web/download/video/dynamic/default/image
	Class                 string   `json:"class,omitempty" graph:"class"`
	IPWhiteList           []string `json:"ip_white_list,omitempty" graph:"ip_white_list"`
	IPBlackList           []string `json:"ip_black_list,omitempty" graph:"ip_black_list"`
	RateLimitEnabled      bool     `json:"rate_limit_enabled,omitempty" graph:"rate_limit_enabled"`
	FreqLimitEnabled      bool     `json:"freq_limit_enabled,omitempty" graph:"freq_limit_enabled"`
	BandwidthLimitEnabled bool     `json:"bandwidth_limit_enabled,omitempty" graph:"bandwidth_limit_enabled"`

	AllowEmptyReferer bool     `json:"allow_empty_referer,omitempty" graph:"allow_empty_referer"`
	RefererWhiteList  []string `json:"referer_white_list,omitempty" graph:"referer_white_list"`
	RefererBlackList  []string `json:"referer_black_list,omitempty" graph:"referer_black_list"`

	SourceSite []*SourceSiteGraph `json:"source_site,omitempty" graph:"-"`
}

type SourceSiteGraph struct {
	BaseNode `graph:",squash"`
	URL      string `json:"url,omitempty" graph:"url"`
	Weight   int    `json:"weight,omitempty" graph:"weight"`
}

type RedisGraph struct {
	BaseNode          `graph:",squash"`
	ObjectMetaGraph   `graph:",squash"`
	Engine            string   `json:"engine,omitempty" graph:"engine"`
	EngineVersion     string   `json:"engine_version,omitempty" graph:"engine_version"`
	ConnectionAddress string   `json:"connection_address,omitempty" graph:"connection_address"`
	PublicAllowed     bool     `json:"public_allowed,omitempty" graph:"public_allowed"`
	IpWhiteList       []string `json:"ip_white_list,omitempty" graph:"ip_white_list"`
	DeleteProtection  *bool    `json:"delete_protection,omitempty" graph:"delete_protection"`
	TDEEnabled        *bool    `json:"tde_enabled,omitempty" graph:"tde_enabled"`

	VPC      []*VPCGraph          `json:"vpc,omitempty" graph:"-"`
	Subnet   []*SubnetGraph       `json:"subnet,omitempty" graph:"-"`
	SG       []*SGGraph           `json:"sg,omitempty" graph:"-"`
	Accounts []*RedisAccountGraph `json:"account,omitempty" graph:"-"`
}

type RedisAccountGraph struct {
	BaseNode `graph:",squash"`
	Name     string `json:"name,omitempty" graph:"name"`
	Enabled  bool   `json:"enabled,omitempty" graph:"enabled"`
	// admin/user
	Class       string `json:"class,omitempty" graph:"class"`
	Description string `json:"description,omitempty" graph:"description"`
}

type EBSGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Status          string `json:"status,omitempty" graph:"status"`
	// volume/snapshot/image
	Class               string `json:"class,omitempty" graph:"class"`
	Encrypted           bool   `json:"encrypted,omitempty" graph:"encrypted"`
	AutoSnapshotEnabled *bool  `json:"auto_snapshot_enabled,omitempty" graph:"auto_snapshot_enabled"`
	// used by volume
	Image []*EBSGraph `json:"image_id,omitempty" graph:"-"`
	ECS   []*ECSGraph `json:"ecs,omitempty" graph:"-"`
	// used by snapshot
	Volume []*EBSGraph `json:"volume,omitempty" graph:"-"`
}

type ApiGatewayGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	// running/stopped
	Status string `json:"status,omitempty" graph:"status"`
	// HTTPS1_1_TLS1_0/HTTPS2_TLS1_0/HTTPS2_TLS1_2
	HTTPSPolicies []string              `json:"https_policies,omitempty" graph:"https_policies"`
	ApiInfo       []*ApiInfoGraph       `json:"api_info,omitempty" graph:"-"`
	Acl           []*ApiGatewayAclGraph `json:"acl,omitempty" graph:"-"`
}

type ApiInfoGraph struct {
	BaseNode `graph:",squash"`
	ID       string `json:"id,omitempty" graph:"id"`
	Name     string `json:"name,omitempty" graph:"name"`
	// APP/APPOPENID/ANONYMOUS
	AuthType    string `json:"auth_type,omitempty" graph:"auth_type"`
	Description string `json:"description,omitempty" graph:"description"`
}

type ApiGatewayAclGraph struct {
	BaseNode `graph:",squash"`
	ID       string `json:"id,omitempty" graph:"id"`
	Name     string `json:"name,omitempty" graph:"name"`
	// on/off
	Status string `json:"status,omitempty" graph:"status"`
	// 4/6
	IPVersion int                       `json:"ip_version,omitempty" graph:"ip_version"`
	ACLRule   []*ApiGatewayACLRuleGraph `json:"acl_rule,omitempty" graph:"-"`
}

type ApiGatewayACLRuleGraph struct {
	BaseNode    `graph:",squash"`
	IP          string `json:"ip,omitempty" graph:"ip"`
	Description string `json:"description,omitempty" graph:"description"`
}

type CenGraph struct {
	BaseNode         `graph:",squash"`
	ObjectMetaGraph  `graph:",squash"`
	Status           string                      `json:"status,omitempty" graph:"status"`
	AttachedInstance []*CenAttachedInstanceGraph `json:"attached_instance,omitempty" graph:"-"`
	CenRouteEntry    []*CenRouteEntryGraph       `json:"cen_route_entry,omitempty" graph:"-"`
}

type CenAttachedInstanceGraph struct {
	BaseNode     `graph:",squash"`
	InstanceID   string `json:"instance_id,omitempty" graph:"instance_id"`
	InstanceName string `json:"instance_name,omitempty" graph:"instance_name"`
	InstanceType string `json:"instance_type,omitempty" graph:"instance_type"`
}

type CenRouteEntryGraph struct {
	BaseNode        `graph:",squash"`
	SrcInstanceID   string `json:"src_instance_id,omitempty" graph:"src_instance_id"`
	SrcInstanceUID  string `json:"src_instance_uid,omitempty" graph:"src_instance_uid"`
	NextInstanceID  string `json:"next_instance_id,omitempty" graph:"next_instance_id"`
	NextInstanceUID string `json:"next_instance_uid,omitempty" graph:"next_instance_uid"`
	DstCidrBlock    string `json:"dst_cidr_block,omitempty" graph:"dst_cidr_block"`
	Status          string `json:"status,omitempty" graph:"status"`
	// system/custom
	Class string `json:"class,omitempty" graph:"class"`
}

type RDSGraph struct {
	BaseNode          `graph:",squash"`
	ObjectMetaGraph   `graph:",squash"`
	Engine            string   `json:"engine,omitempty" graph:"engine"`
	EngineVersion     string   `json:"engine_version,omitempty" graph:"engine_version"`
	ConnectionAddress string   `json:"connection_address,omitempty" graph:"connection_address"`
	PublicAllowed     bool     `json:"public_allowed,omitempty" graph:"public_allowed"`
	IpWhiteList       []string `json:"ip_white_list,omitempty" graph:"ip_white_list"`
	TDEEnabled        *bool    `json:"tde_enabled,omitempty" graph:"tde_enabled"`
	BackupAvailable   bool     `json:"backup_available,omitempty" graph:"backup_available"`
	// auto/manual/none/na
	BackupMethod   string `json:"backup_method,omitempty" graph:"backup_method"`
	LastBackupTime int64  `json:"last_backup_time,omitempty" graph:"last_backup_time"`
	LogFileExists  bool   `json:"log_file_exists,omitempty" graph:"log_file_exists"`

	VPC      []*VPCGraph        `json:"vpc,omitempty" graph:"-"`
	Subnet   []*SubnetGraph     `json:"subnet,omitempty" graph:"-"`
	SG       []*SGGraph         `json:"sg,omitempty" graph:"-"`
	Accounts []*RDSAccountGraph `json:"account,omitempty" graph:"-"`
}

type RDSAccountGraph struct {
	BaseNode `graph:",squash"`
	Name     string `json:"name,omitempty" graph:"name"`
	Enabled  bool   `json:"enabled,omitempty" graph:"enabled"`
	// admin/user
	Class       string `json:"class,omitempty" graph:"class"`
	Host        string `json:"host,omitempty" graph:"host"`
	Description string `json:"description,omitempty" graph:"description"`
}

type ESGraph struct {
	BaseNode              `graph:",squash"`
	ObjectMetaGraph       `graph:",squash"`
	Status                string   `json:"status,omitempty" graph:"status"`
	PublicEndpoint        string   `json:"public_endpoint,omitempty" graph:"public_endpoint"`
	PrivateEndpoint       string   `json:"private_endpoint,omitempty" graph:"private_endpoint"`
	IPWhiteList           []string `json:"ip_white_list,omitempty" graph:"ip_white_list"`
	IPBlackList           []string `json:"ip_black_list,omitempty" graph:"ip_black_list"`
	EngineVersion         string   `json:"engine_version,omitempty" graph:"engine_version"`
	PublicAllowed         bool     `json:"public_allowed,omitempty" graph:"public_allowed"`
	KibanaPublicEndpoint  string   `json:"kibana_public_endpoint,omitempty" graph:"kibana_public_endpoint"`
	KibanaPrivateEndpoint string   `json:"kibana_private_endpoint,omitempty" graph:"kibana_private_endpoint"`
	KibanaProtocol        string   `json:"kibana_protocol,omitempty" graph:"kibana_protocol"`
	KibanaIPWhiteList     []string `json:"kibana_ip_white_list,omitempty" graph:"kibana_ip_white_list"`
	KibanaIPBlackList     []string `json:"kibana_ip_black_list,omitempty" graph:"kibana_ip_black_list"`
	KibanaPublicAllowed   bool     `json:"kibana_public_allowed,omitempty" graph:"kibana_public_allowed"`

	VPC    []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Subnet []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
	SG     []*SGGraph     `json:"sg,omitempty" graph:"-"`
	Node   []*ESNodeGraph `json:"node,omitempty" graph:"-"`
	EIP    []*EipGraph    `json:"eip,omitempty" graph:"-"`
}

type ESNodeGraph struct {
	BaseNode `graph:",squash"`
	NodeID   string `json:"node_id,omitempty" graph:"node_id"`
	Name     string `json:"name,omitempty" graph:"name"`
	IP       string `json:"ip,omitempty" graph:"ip"`
	Class    string `json:"class,omitempty" graph:"class"`
}

type FunctionGraph struct {
	BaseNode                    `graph:",squash"`
	ObjectMetaGraph             `graph:",squash"`
	Runtime                     string         `json:"runtime,omitempty" graph:"runtime"`
	DirectInternetAccessAllowed bool           `json:"direct_internet_access_allowed,omitempty" graph:"direct_internet_access_allowed"`
	LogEnabled                  bool           `json:"log_enabled,omitempty" graph:"log_enabled"`
	LogPath                     string         `json:"log_path,omitempty" graph:"log_path"`
	NasMountPoints              []string       `json:"nas_mount_points,omitempty" graph:"nas_mount_points"`
	BucketMountPoints           []string       `json:"bucket_mount_points,omitempty" graph:"bucket_mount_points"`
	VPC                         []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Subnet                      []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
	SG                          []*SGGraph     `json:"sg,omitempty" graph:"-"`
}

type NASGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Status          string `json:"status,omitempty" graph:"status"`
	// nfs/smb/cpfs
	Class   string             `json:"class,omitempty" graph:"class"`
	Encrypt bool               `json:"encrypt,omitempty" graph:"encrypt"`
	Domains []string           `json:"domains,omitempty" graph:"domains"`
	IPs     []string           `json:"ips,omitempty" graph:"ips"`
	VPC     []*VPCGraph        `json:"vpc,omitempty" graph:"-"`
	Subnet  []*SubnetGraph     `json:"subnet,omitempty" graph:"-"`
	ACLRule []*NasACLRuleGraph `json:"acl_rule,omitempty" graph:"-"`
}

type NasACLRuleGraph struct {
	BaseNode   `graph:",squash"`
	AclID      string `json:"acl_id,omitempty" graph:"acl_id"`
	Priority   int    `json:"priority,omitempty" graph:"priority"`
	SourceCidr string `json:"source_cidr,omitempty" graph:"source_cidr"`
	RWAccess   string `json:"rw_access,omitempty" graph:"rw_access"`
	// no_squash/root_squash/all_squash
	UserAccess string `json:"user_access,omitempty" graph:"user_access"`
}

type K8SGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Status          string   `json:"status,omitempty" graph:"status"`
	EngineVersion   string   `json:"engine_version,omitempty" graph:"engine_version"`
	PrivateEndpoint string   `json:"private_endpoint,omitempty" graph:"private_endpoint"`
	PublicEndpoint  string   `json:"public_endpoint,omitempty" graph:"public_endpoint"`
	IpWhiteList     []string `json:"ip_white_list,omitempty" graph:"ip_white_list"`
	PodCIDRs        []string `json:"pod_cidrs,omitempty" graph:"pod_cidrs"`
	ServiceCIDRs    []string `json:"service_cidrs,omitempty" graph:"service_cidrs"`
	AuditLogEnabled *bool    `json:"audit_log_enabled,omitempty" graph:"audit_log_enabled"`

	VPC    []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Subnet []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
	SG     []*SGGraph     `json:"sg,omitempty" graph:"-"`
	LB     []*LBGraph     `json:"lb,omitempty" graph:"-"`
}

type LogServiceGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Project         string                  `json:"project_name,omitempty" graph:"project_name"`
	LogStoreName    string                  `json:"log_store_name,omitempty" graph:"log_store_name"`
	LogStoreConfigs string                  `json:"log_store_configs,omitempty" graph:"log_store_configs"`
	PolicyDocument  []*PolicyStatementGraph `json:"policy_document,omitempty" graph:"-"`
}

type AuditLogStatusGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Entries         []*AuditLogStatusEntryGraph `json:"entries,omitempty" graph:"-"`
}

type AuditLogStatusEntryGraph struct {
	BaseNode     `graph:",squash"`
	ServiceName  string `json:"service_name,omitempty" graph:"service_name"`
	EventName    string `json:"event_name,omitempty" graph:"event_name"`
	LastIngestAt int64  `json:"last_ingest_at,omitempty" graph:"last_ingest_at"`
}

type LaunchTemplateGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
}

type KMSGraph struct {
	BaseNode                    `graph:",squash"`
	ObjectMetaGraph             `graph:",squash"`
	Enabled                     bool  `json:"enabled,omitempty" graph:"enabled"`
	CreatedAt                   int64 `json:"created_at,omitempty" graph:"created_at"`
	DeletedAt                   int64 `json:"deleted_at,omitempty" graph:"deleted_at"`
	PendingDeletionWindowInDays int   `json:"pending_deletion_window_in_days,omitempty" graph:"pending_deletion_window_in_days"`
}

type CFWGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`
	Status          string          `json:"status,omitempty" graph:"status"`
	Rules           []*CFWRuleGraph `json:"cfw_rules,omitempty" graph:"-"`
	VPC             []*VPCGraph     `json:"vpc,omitempty" graph:"-"`
	NAT             []*NATGraph     `json:"nat,omitempty" graph:"-"`
	EIP             []*EipGraph     `json:"eip,omitempty" graph:"-"`
}

type CFWRuleGraph struct {
	BaseNode     `graph:",squash"`
	RuleID       string            `json:"rule_id,omitempty" graph:"rule_id"`
	Description  string            `json:"description,omitempty" graph:"description"`
	IPVersion    []string          `json:"ip_version,omitempty" graph:"ip_version"`
	Priority     int               `json:"priority,omitempty" graph:"priority"`
	Protocol     []string          `json:"protocol,omitempty" graph:"protocol"`
	Direction    string            `json:"direction,omitempty" graph:"direction"`
	Policy       string            `json:"policy,omitempty" graph:"policy"`
	SrcIPRange   []*IPRangeGraph   `json:"src_ip_range,omitempty" graph:"-"`
	SrcPortRange []*PortRangeGraph `json:"src_port_range,omitempty" graph:"-"`
	DstIPRange   []*IPRangeGraph   `json:"dst_ip_range,omitempty" graph:"-"`
	DstPortRange []*PortRangeGraph `json:"dst_port_range,omitempty" graph:"-"`
}

type KafkaGraph struct {
	BaseNode        `graph:",squash"`
	ObjectMetaGraph `graph:",squash"`

	Status          string `json:"status,omitempty" graph:"status"`
	Version         string `json:"version,omitempty" graph:"version"`
	NodeNum         int    `json:"node_num,omitempty" graph:"node_num"`
	NodeSpec        string `json:"node_spec,omitempty" graph:"node_spec"`
	PublicIPEnabled bool   `json:"public_ip_enabled,omitempty" graph:"public_ip_enabled"`
	// None/SASL_SCRAM/SASL_PLAIN/SSL
	Authentications []string       `json:"authentications,omitempty" graph:"authentications"`
	SG              []*SGGraph     `json:"sg,omitempty" graph:"-"`
	VPC             []*VPCGraph    `json:"vpc,omitempty" graph:"-"`
	Subnet          []*SubnetGraph `json:"subnet,omitempty" graph:"-"`
	Eip             []*EipGraph    `json:"eip,omitempty" graph:"-"`
}
