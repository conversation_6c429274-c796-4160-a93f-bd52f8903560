package model

import (
	"errors"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

type AssetMessage struct {
	Source string                 `json:"source"`
	Type   string                 `json:"type"`
	RawLog sonic.NoCopyRawMessage `json:"raw_log"`
	Region string                 `json:"region"`
}

type ResourceService interface {
	GetMessageType() string
	GetAssetMsgCh() chan *AssetMessage
	Validate() error
	Start()
}

type CommonResourceService struct {
	MessageType     string
	AssetMsgCh      chan *AssetMessage
	BufferTimeout   time.Duration
	Transform       func([]*AssetMessage) (map[string][]map[string]any, error)
	UpdateResources func(map[string][]map[string]any)
}

func (c *CommonResourceService) GetMessageType() string {
	return c.MessageType
}

func (c *CommonResourceService) GetAssetMsgCh() chan *AssetMessage {
	if c.AssetMsgCh == nil {
		c.AssetMsgCh = make(chan *AssetMessage, 100)
	}
	return c.AssetMsgCh
}

func (c *CommonResourceService) Validate() error {
	if c.MessageType == "" {
		return errors.New("message type is empty")
	}
	if c.Transform == nil {
		return errors.New("transform is nil")
	}
	if c.UpdateResources == nil {
		return errors.New("update resources is nil")
	}
	if c.AssetMsgCh == nil {
		c.AssetMsgCh = make(chan *AssetMessage, 100)
	}
	if c.BufferTimeout == 0 {
		c.BufferTimeout = time.Second * 10
	}
	return nil
}

func (c *CommonResourceService) Start() {
	maxFlushSize := 128
	buf := make([]*AssetMessage, 0, 32)
	timer := time.NewTicker(c.BufferTimeout)

	updateBulk := func() error {
		if len(buf) == 0 {
			return nil
		}

		data, err := c.Transform(buf)
		if err != nil {
			return err
		}
		c.UpdateResources(data)
		return nil
	}

	addToBuf := func(r *AssetMessage) error {
		buf = append(buf, r)
		var err error
		if len(buf) == maxFlushSize {
			err = updateBulk()
			buf = buf[:0]
		}
		return err
	}

	flushBuf := func() error {
		if len(buf) == 0 {
			return nil
		}

		err := updateBulk()
		buf = buf[:0]
		return err
	}

	go func() {
		for {
			select {
			case obj := <-c.AssetMsgCh:
				err := addToBuf(obj)
				if err != nil {
					logger.Error(err)
				}
			case <-timer.C:
				err := flushBuf()
				if err != nil {
					logger.Error(err)
				}
			}
		}
	}()
}
