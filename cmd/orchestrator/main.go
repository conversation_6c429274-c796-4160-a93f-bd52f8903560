package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"disk_scanner/internal/config"
	"disk_scanner/internal/queue"
	"disk_scanner/internal/types"

	"github.com/gorilla/mux"
	"github.com/segmentio/kafka-go"
)

type Orchestrator struct {
	config     *config.OrchestratorConfig
	taskWriter *kafka.Writer
	server     *http.Server
}

type APIResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

func main() {
	log.Println("Starting VulcanScan Orchestrator...")

	cfg, err := config.InitConfig[config.OrchestratorConfig]()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	orchestrator, err := NewOrchestrator(cfg)
	if err != nil {
		log.Fatalf("Failed to create orchestrator: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal, gracefully shutting down...")
		cancel()
	}()

	err = orchestrator.Start(ctx)
	if err != nil {
		log.Fatalf("Orchestrator failed: %v", err)
	}

	log.Println("Orchestrator completed successfully")
}

func NewOrchestrator(cfg *config.OrchestratorConfig) (*Orchestrator, error) {
	orchestrator := &Orchestrator{
		config: cfg,
	}

	if cfg.EnableCron {
		kafkaConfig := &queue.KafkaConfig{
			Brokers: cfg.Kafka.Brokers,
			Topic:   cfg.Kafka.Topic,
			GroupID: cfg.Kafka.GroupID,
		}
		orchestrator.taskWriter = queue.NewKafkaTaskWriter(kafkaConfig)
		log.Printf("Kafka queue initialized with brokers: %v, topic: %s", cfg.Kafka.Brokers, cfg.Kafka.Topic)
	}

	if cfg.EnableAPI {
		router := mux.NewRouter()
		orchestrator.setupAPIRoutes(router)

		orchestrator.server = &http.Server{
			Addr:    fmt.Sprintf(":%d", cfg.APIPort),
			Handler: router,
		}
		log.Printf("HTTP API server configured on port %d", cfg.APIPort)
	}

	return orchestrator, nil
}

func (o *Orchestrator) Start(ctx context.Context) error {
	errChan := make(chan error, 1)

	if o.config.EnableAPI {
		go func() {
			log.Printf("Starting HTTP API server on port %d", o.config.APIPort)
			if err := o.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				errChan <- fmt.Errorf("HTTP server failed: %w", err)
			}
		}()
	}

	if o.config.EnableCron {
		go func() {
			log.Println("Starting task queue consumer...")
			if err := o.consumeTasksFromQueue(ctx); err != nil {
				errChan <- fmt.Errorf("queue consumer failed: %w", err)
			}
		}()
	}

	select {
	case err := <-errChan:
		return err
	case <-ctx.Done():
		return o.shutdown()
	}
}

func (o *Orchestrator) consumeTasksFromQueue(ctx context.Context) error {
	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			task, err := o.taskQueue.Consume(ctx)
			if err != nil {
				log.Printf("Failed to consume task from queue: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}

			log.Printf("Received task from queue: %+v", task)

			if err := o.processTask(task); err != nil {
				log.Printf("Failed to process task %s: %v", task.TaskID, err)
			}
		}
	}
}

func (o *Orchestrator) processTask(task *types.Task) error {
	log.Printf("Processing task: %s for provider: %s, region: %s",
		task.TaskID, task.CloudProvider, task.Region)

	return nil
}

func (o *Orchestrator) setupAPIRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/v1").Subrouter()

	api.HandleFunc("/tasks", o.handleCreateTask).Methods("POST")
	api.HandleFunc("/tasks", o.handleListTasks).Methods("GET")
	api.HandleFunc("/tasks/{taskId}", o.handleGetTask).Methods("GET")
	api.HandleFunc("/health", o.handleHealth).Methods("GET")
}

func (o *Orchestrator) handleCreateTask(w http.ResponseWriter, r *http.Request) {
	var task types.Task
	if err := json.NewDecoder(r.Body).Decode(&task); err != nil {
		o.sendErrorResponse(w, http.StatusBadRequest, "Invalid JSON payload")
		return
	}

	if err := o.validateTask(&task); err != nil {
		o.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid task: %v", err))
		return
	}

	if task.TaskID == "" {
		task.TaskID = generateTaskID()
	}

	if o.config.EnableQueue && o.taskQueue != nil {
		if err := o.taskQueue.Publish(&task); err != nil {
			log.Printf("Failed to publish task to queue: %v", err)
			o.sendErrorResponse(w, http.StatusInternalServerError, "Failed to queue task")
			return
		}
		log.Printf("Task %s published to queue via API", task.TaskID)
	} else {
		if err := o.processTask(&task); err != nil {
			log.Printf("Failed to process task directly: %v", err)
			o.sendErrorResponse(w, http.StatusInternalServerError, "Failed to process task")
			return
		}
		log.Printf("Task %s processed directly via API", task.TaskID)
	}

	o.sendSuccessResponse(w, http.StatusCreated, "Task created successfully", map[string]string{"task_id": task.TaskID})
}

func (o *Orchestrator) handleListTasks(w http.ResponseWriter, r *http.Request) {
	o.sendSuccessResponse(w, http.StatusOK, "Tasks retrieved successfully", []types.Task{})
}

func (o *Orchestrator) handleGetTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["taskId"]

	if taskID == "" {
		o.sendErrorResponse(w, http.StatusBadRequest, "Task ID is required")
		return
	}

	o.sendSuccessResponse(w, http.StatusOK, "Task retrieved successfully", map[string]string{"task_id": taskID, "status": "not_implemented"})
}

func (o *Orchestrator) handleHealth(w http.ResponseWriter, r *http.Request) {
	health := map[string]any{
		"status":        "healthy",
		"timestamp":     time.Now().UTC(),
		"queue_enabled": o.config.EnableQueue,
		"api_enabled":   o.config.EnableAPI,
	}

	o.sendSuccessResponse(w, http.StatusOK, "Service is healthy", health)
}

func (o *Orchestrator) validateTask(task *types.Task) error {
	if task.HostID == "" {
		return fmt.Errorf("host_id is required")
	}
	if task.DiskID == "" {
		return fmt.Errorf("disk_id is required")
	}
	if task.CloudProvider == "" {
		return fmt.Errorf("cloud_provider is required")
	}
	if task.Region == "" {
		return fmt.Errorf("region is required")
	}
	return nil
}

func (o *Orchestrator) sendSuccessResponse(w http.ResponseWriter, statusCode int, message string, data any) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}

	json.NewEncoder(w).Encode(response)
}

func (o *Orchestrator) sendErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := APIResponse{
		Success: false,
		Message: message,
	}

	json.NewEncoder(w).Encode(response)
}

func (o *Orchestrator) shutdown() error {
	log.Println("Shutting down orchestrator...")

	if o.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := o.server.Shutdown(ctx); err != nil {
			log.Printf("Failed to shutdown HTTP server gracefully: %v", err)
		} else {
			log.Println("HTTP server shutdown complete")
		}
	}

	if o.taskQueue != nil {
		if err := o.taskQueue.Close(); err != nil {
			log.Printf("Failed to close task queue: %v", err)
		} else {
			log.Println("Task queue closed")
		}
	}

	return nil
}

func generateTaskID() string {
	return fmt.Sprintf("task-%d", time.Now().UnixNano())
}
