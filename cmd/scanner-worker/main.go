package main

import (
	"log"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"disk_scanner/internal/config"
	"disk_scanner/internal/worker"
)

func main() {
	logger.Infof("Starting VulcanScan Scanner Worker...")

	// Load configuration
	cfg, err := config.InitConfig[config.WorkerConfig]()
	if err != nil {
		logger.Fatalf("Failed to load configuration: %v", err)
	}

	logger.Infof("Loaded configuration: Provider=%s, Region=%s, Zone=%s",
		cfg.CloudProvider, cfg.Region, cfg.Zone)

	// Create task processor
	processor := worker.NewTaskProcessor(cfg.InstanceID)

	log.Printf("Processing sample task: %+v", sampleTask)

	// Process the task
	result, err := processor.ProcessTask(sampleTask)
	if err != nil {
		log.Fatalf("Failed to process task: %v", err)
	}

	log.Printf("Task processing completed successfully. Result: TaskID=%s, RawJSON length=%d",
		result.TaskID, len(result.RawJSON))

	log.Println("Scanner Worker completed successfully")
}
