package main

import (
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	aliyun_graph "AssetStandardizer/transform/aliyun"
	aws_graph "AssetStandardizer/transform/aws"
	azure_graph "AssetStandardizer/transform/azure"
	baidu_graph "AssetStandardizer/transform/baidu"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	maxWorkerNum = 1
)

var (
	ch      = make(chan map[string]any, 8)
	graphCh = make(chan []byte, 8)
)

func main() {
	if err := logger.InitDefaultLogger(); err != nil {
		panic(err)
	}
	if err := infra.InitMQ(); err != nil {
		logger.Fatal(err)
		return
	}
	if err := infra.InitMongo(); err != nil {
		logger.Fatal(err)
		return
	}
	if err := infra.InitNeo4j(); err != nil {
		logger.Fatal(err)
		return
	}

	for range maxWorkerNum {
		go graphWorker(graphCh)
	}
	go removeExpiredResources(5*time.Minute, -6*time.Hour)
	go removeExpiredHidsResources(30*time.Minute, -30*time.Minute)

	for {
		msg, err := infra.RawdataReader.ReadMessage(context.Background())
		if err != nil {
			logger.Error(err)
			continue
		}
		rawAsset := map[string]any{}
		_ = sonic.Unmarshal(msg.Value, &rawAsset)

		graphCh <- msg.Value
	}
}

func graphWorker(ch <-chan []byte) {

	for rawMessage := range ch {
		var err error

		assetMsg := &model.AssetMessage{}
		if err = sonic.Unmarshal(rawMessage, assetMsg); err != nil {
			logger.DefaultLogger().Error(err)
			continue
		}

		switch assetMsg.Source {
		case "alibaba":
			aliyun_graph.AssetMessageChan <- assetMsg
		case "aws":
			aws_graph.AssetMessageChan <- assetMsg
		case "azure":
			azure_graph.AssetMessageChan <- assetMsg
		case "baidu":
			baidu_graph.AssetMessageChan <- assetMsg
			// case "volces":
			// 	asset, err = volcengine_graph.Parse(rawAsset)
			// case "azure":
			// 	asset, err = azure_graph.Parse(rawAsset)
			// case "lixiang":
			// 	asset, err = lixiang_graph.Parse(rawAsset)
			// case "inner":
			// 	err = rebuildOriginalData(rawAsset)
		}

	}
}

func removeExpiredResources(checkInterval time.Duration, expiredDuration time.Duration) {
	expiredDuration = -1 * expiredDuration.Abs()
	ticker := time.NewTicker(checkInterval)

	r := []struct {
		Kind       string `bson:"kind"`
		Provider   string `bson:"provider"`
		UpdateTime int64  `bson:"update_time"`
	}{}

	pipeline := mongo.Pipeline{
		bson.D{{Key: "$group", Value: bson.M{"_id": bson.M{"kind": "$kind", "provider": "$provider"}, "update_time": bson.M{"$max": "$update_time"}}}},
		bson.D{{Key: "$project", Value: bson.M{"kind": "$_id.kind", "provider": "$_id.provider", "update_time": "$update_time"}}},
	}

	for range ticker.C {
		cursor, err := infra.Mongo.Collection("cspm_resources").Aggregate(context.TODO(), pipeline)
		if err != nil {
			logger.DefaultLogger().Errorf("failed to get latest resource: %s", err)
			continue
		}
		err = cursor.All(context.TODO(), &r)
		if err != nil {
			logger.DefaultLogger().Errorf("failed to get latest resource: %s", err)
			continue
		}

		subFilters := make([]bson.M, 0, len(r))
		for _, item := range r {
			latest := time.UnixMilli(item.UpdateTime)
			expired := latest.Add(expiredDuration).UnixMilli()
			subFilters = append(subFilters, bson.M{"kind": item.Kind, "provider": item.Provider, "update_time": bson.M{"$lt": expired}})
		}

		filter := bson.M{"$or": subFilters}
		result, err := infra.Mongo.Collection("cspm_resources").DeleteMany(context.TODO(), filter)
		if err != nil {
			logger.DefaultLogger().Error(err)
			continue
		}
		if result.DeletedCount != 0 {
			logger.DefaultLogger().Infof("deleted %d outdated resources", result.DeletedCount)
		}
	}
}

func removeExpiredHidsResources(checkInterval time.Duration, expiredDuration time.Duration) {
	expiredDuration = -1 * expiredDuration.Abs()
	expiredTime := time.Now().Add(expiredDuration).UnixMilli()
	ticker := time.NewTicker(checkInterval)

	filter := bson.M{"kind": "ecs", "status": "running", "$or": []bson.M{{"hids_info.sync_at": bson.M{"$lt": expiredTime}}, {"hids_info.sync_at": bson.M{"$exists": false}}}}

	for range ticker.C {
		result, err := infra.Mongo.Collection("cspm_resources").UpdateMany(context.TODO(), filter, bson.M{"$unset": bson.M{"hids_info": ""}})
		if err != nil {
			logger.DefaultLogger().Errorf("failed to remove expired hids resources: %s", err)
			continue
		}

		if result.ModifiedCount != 0 {
			logger.DefaultLogger().Infof("deleted %d outdated hids resources", result.ModifiedCount)
		}
	}
}
