package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type User struct {
	AccessKeys   []AccessKey   `json:"accessKeys"`
	LoginProfile *LoginProfile `json:"loginProfile"`
	Policies     []Policy      `json:"policies"`
	User         UserClass     `json:"user"`
}

type AccessKey struct {
	AccessKeyID string `json:"AccessKeyId"`
	CreateDate  string `json:"CreateDate"`
	Status      string `json:"Status"`
	UpdateDate  string `json:"UpdateDate"`
	UserName    string `json:"UserName"`
}

type LoginProfile struct {
	CreateDate            string `json:"CreateDate"`
	LastLoginDate         string `json:"LastLoginDate"`
	LastLoginIP           string `json:"LastLoginIp"`
	LastResetPasswordTime int64  `json:"LastResetPasswordTime"`
	LoginAllowed          bool   `json:"LoginAllowed"`
	LoginLocked           bool   `json:"LoginLocked"`
	PasswordExpireAt      int64  `json:"PasswordExpireAt"`
	PasswordResetRequired bool   `json:"PasswordResetRequired"`
	Status                string `json:"Status"`
	UpdateDate            string `json:"UpdateDate"`
	UserID                int64  `json:"UserId"`
	UserName              string `json:"UserName"`
	SafeAuthFlag          bool   `json:"SafeAuthFlag"`
}

type Policy struct {
	AttachDate  string `json:"AttachDate"`
	Description string `json:"Description"`
	PolicyName  string `json:"PolicyName"`
	PolicyTrn   string `json:"PolicyTrn"`
	PolicyType  string `json:"PolicyType"`
}

type UserClass struct {
	AccountID           int64  `json:"AccountId"`
	CreateDate          string `json:"CreateDate"`
	Description         string `json:"Description"`
	DisplayName         string `json:"DisplayName"`
	Email               string `json:"Email"`
	EmailIsVerify       bool   `json:"EmailIsVerify"`
	ID                  int64  `json:"Id"`
	MobilePhone         string `json:"MobilePhone"`
	MobilePhoneIsVerify bool   `json:"MobilePhoneIsVerify"`
	Trn                 string `json:"Trn"`
	UpdateDate          string `json:"UpdateDate"`
	UserName            string `json:"UserName"`
}

func parseUser(raw map[string]any) model.Asset {
	original := &User{}
	meta := &model.ObjectMeta{}
	user := &model.User{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.User.Trn
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "user", original.User.UserName)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "user"
	meta.Name = original.User.UserName
	meta.Description = original.User.Description

	user.DisplayName = original.User.DisplayName
	user.Enabled = true
	createdAt, _ := time.Parse("20060102T150405Z0700", original.User.CreateDate)
	user.CreatedAt = utils.UnwrapOr(createdAt.UnixMilli(), createdAt.UnixMilli() >= 0, 0)
	user.PolicyIDList = utils.Map(original.Policies, func(policy Policy) string {
		return utils.GenerateUID(meta.Provider, "policy", policy.PolicyName)
	})
	user.AccessKeyIDList = utils.Map(original.AccessKeys, func(key AccessKey) string {
		return utils.GenerateUID(meta.Provider, "ak", key.AccessKeyID)
	})
	if original.LoginProfile != nil {
		user.LoginAllowed = original.LoginProfile.LoginAllowed
		t, _ := time.Parse("20060102T150405Z0700", original.LoginProfile.LastLoginDate)
		user.LastLoginAt = utils.UnwrapOr(t.UnixMilli(), t.UnixMilli() >= 0, 0)
		user.MFAEnabled = original.LoginProfile.SafeAuthFlag
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, user)
	return asset
}
