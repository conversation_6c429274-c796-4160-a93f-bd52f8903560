package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

type Es struct {
	Instance EsInstance `json:"instance"`
	Nodes    []Node     `json:"nodes"`
}

type EsInstance struct {
	ChargeEnabled                   bool                  `json:"ChargeEnabled"`
	CreateTime                      string                `json:"CreateTime"`
	DeletionProtection              bool                  `json:"DeletionProtection"`
	KibanaPrivateIPWhitelist        string                `json:"KibanaPrivateIpWhitelist"`
	KibanaPublicIPWhitelist         string                `json:"KibanaPublicIpWhitelist"`
	EnableESPrivateNetwork          bool                  `json:"EnableESPrivateNetwork"`
	EnableESPublicNetwork           bool                  `json:"EnableESPublicNetwork"`
	EnableKibanaPrivateNetwork      bool                  `json:"EnableKibanaPrivateNetwork"`
	EnableKibanaPublicNetwork       bool                  `json:"EnableKibanaPublicNetwork"`
	EnableESPrivateDomainPublic     bool                  `json:"EnableESPrivateDomainPublic"`
	EnableKibanaPrivateDomainPublic bool                  `json:"EnableKibanaPrivateDomainPublic"`
	KibanaEipID                     string                `json:"KibanaEipId"`
	KibanaEip                       string                `json:"KibanaEip"`
	ExpireDate                      string                `json:"ExpireDate"`
	InstanceConfiguration           InstanceConfiguration `json:"InstanceConfiguration"`
	MetricsVersion                  string                `json:"MetricsVersion"`
	InstanceID                      string                `json:"InstanceId"`
	KibanaPrivateDomain             string                `json:"KibanaPrivateDomain"`
	KibanaPublicDomain              string                `json:"KibanaPublicDomain"`
	KibanaConfig                    KibanaConfig          `json:"KibanaConfig"`
	MaintenanceDay                  []string              `json:"MaintenanceDay"`
	MaintenanceTime                 string                `json:"MaintenanceTime"`
	Status                          string                `json:"Status"`
	TransferInfo                    TransferInfo          `json:"TransferInfo"`
	ResourceTags                    []ResourceTag         `json:"ResourceTags"`
	TotalNodes                      int64                 `json:"TotalNodes"`
	UserID                          string                `json:"UserId"`
	SubInstances                    []interface{}         `json:"SubInstances"`
	Encrypted                       bool                  `json:"Encrypted"`
	CerebroEnabled                  bool                  `json:"CerebroEnabled"`
	CerebroPrivateDomain            string                `json:"CerebroPrivateDomain"`
	CerebroPublicDomain             string                `json:"CerebroPublicDomain"`
	ESInnerEndpoint                 string                `json:"ESInnerEndpoint"`
	ESPrivateDomain                 string                `json:"ESPrivateDomain"`
	ESPrivateEndpoint               string                `json:"ESPrivateEndpoint"`
	ESPublicDomain                  string                `json:"ESPublicDomain"`
	ESPublicEndpoint                string                `json:"ESPublicEndpoint"`
	ESPrivateIPWhitelist            string                `json:"ESPrivateIpWhitelist"`
	ESPublicIPWhitelist             string                `json:"ESPublicIpWhitelist"`
	ESEipID                         string                `json:"ESEipId"`
	ESEip                           string                `json:"ESEip"`
}

type InstanceConfiguration struct {
	AdminUserName          string           `json:"AdminUserName"`
	ChargeType             string           `json:"ChargeType"`
	EnableHTTPS            bool             `json:"EnableHttps"`
	EnablePureMaster       bool             `json:"EnablePureMaster"`
	ProjectName            string           `json:"ProjectName"`
	RegionID               string           `json:"RegionId"`
	ZoneID                 string           `json:"ZoneId"`
	ZoneNumber             int64            `json:"ZoneNumber"`
	Subnet                 EsSubnet         `json:"Subnet"`
	Version                string           `json:"Version"`
	ColdNodeNumber         int64            `json:"ColdNodeNumber"`
	HotNodeNumber          int64            `json:"HotNodeNumber"`
	HotNodeResourceSpec    NodeResourceSpec `json:"HotNodeResourceSpec"`
	HotNodeStorageSpec     NodeStorageSpec  `json:"HotNodeStorageSpec"`
	CoordinatorNodeNumber  int64            `json:"CoordinatorNodeNumber"`
	InstanceName           string           `json:"InstanceName"`
	KibanaNodeNumber       int64            `json:"KibanaNodeNumber"`
	KibanaNodeResourceSpec NodeResourceSpec `json:"KibanaNodeResourceSpec"`
	MasterNodeNumber       int64            `json:"MasterNodeNumber"`
	MasterNodeResourceSpec NodeResourceSpec `json:"MasterNodeResourceSpec"`
	MasterNodeStorageSpec  NodeStorageSpec  `json:"MasterNodeStorageSpec"`
	WarmNodeNumber         int64            `json:"WarmNodeNumber"`
	Vpc                    EsVpc            `json:"VPC"`
}

type NodeResourceSpec struct {
	Name        string `json:"Name"`
	DisplayName string `json:"DisplayName"`
	Description string `json:"Description"`
	Memory      int64  `json:"Memory"`
	CPU         int64  `json:"CPU"`
}

type NodeStorageSpec struct {
	Name        string `json:"Name"`
	DisplayName string `json:"DisplayName"`
	Description string `json:"Description"`
	MaxSize     int64  `json:"MaxSize"`
	MinSize     int64  `json:"MinSize"`
	Size        int64  `json:"Size"`
	Type        string `json:"Type"`
}

type EsSubnet struct {
	SubnetID   string `json:"SubnetId"`
	SubnetName string `json:"SubnetName"`
}

type EsVpc struct {
	VpcID   string `json:"VpcId"`
	VpcName string `json:"VpcName"`
}

type KibanaConfig struct {
	CookieTTL        int64 `json:"CookieTTL"`
	SessionTTL       int64 `json:"SessionTTL"`
	SessionKeepAlive bool  `json:"SessionKeepAlive"`
	RequestTimeout   int64 `json:"RequestTimeout"`
}

type ResourceTag struct {
	Type   string            `json:"Type"`
	TagKvs map[string]string `json:"TagKvs"`
}

type TransferInfo struct {
}

type Node struct {
	InstanceID      string       `json:"InstanceId"`
	NodeName        string       `json:"NodeName"`
	NodeDisplayName string       `json:"NodeDisplayName"`
	Status          string       `json:"Status"`
	StartTime       string       `json:"StartTime"`
	RestartNumber   int64        `json:"RestartNumber"`
	IsMaster        bool         `json:"IsMaster"`
	IsHot           bool         `json:"IsHot"`
	IsCoordinator   bool         `json:"IsCoordinator"`
	IsWarm          bool         `json:"IsWarm"`
	IsCold          bool         `json:"IsCold"`
	IsKibana        bool         `json:"IsKibana"`
	HaveLog         bool         `json:"HaveLog"`
	ResourceSpec    ResourceSpec `json:"ResourceSpec"`
	StorageSpec     StorageSpec  `json:"StorageSpec"`
}

type ResourceSpec struct {
	Description string `json:"Description"`
	DisplayName string `json:"DisplayName"`
	Memory      int64  `json:"Memory"`
	CPU         int64  `json:"CPU"`
}

type StorageSpec struct {
	Description string `json:"Description"`
	DisplayName string `json:"DisplayName"`
	MinSize     int64  `json:"MinSize"`
	MaxSize     int64  `json:"MaxSize"`
	Type        string `json:"Type"`
	Size        int64  `json:"Size"`
}

func parseES(raw map[string]any) model.Asset {
	original := &Es{}
	meta := &model.ObjectMeta{}
	es := &model.ES{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Instance.InstanceID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "es", original.Instance.InstanceID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "es"
	meta.Name = original.Instance.InstanceConfiguration.InstanceName
	meta.OriginalLabels = utils.Flatten(utils.Map(original.Instance.ResourceTags, func(tag ResourceTag) []model.KV {
		kvs := make([]model.KV, 0, len(tag.TagKvs))
		for k, v := range tag.TagKvs {
			kvs = append(kvs, model.KV{Key: k, Value: v})
		}
		return kvs
	}))

	es.Status = utils.UnwrapOr("running", original.Instance.Status == "Running", "stopped")
	es.EIPIDList = utils.FilterMap([]string{original.Instance.ESEipID, original.Instance.KibanaEipID}, func(eipId string) (string, bool) {
		return utils.GenerateUID(meta.Provider, "eip", eipId), eipId != ""
	})
	es.PublicEndpoint = original.Instance.ESPublicEndpoint
	es.PrivateEndpoint = original.Instance.ESPrivateEndpoint
	es.IPWhiteList = append(es.IPWhiteList, utils.Map(strings.Split(original.Instance.ESPrivateIPWhitelist, ","), formatCIDR)...)
	es.IPWhiteList = append(es.IPWhiteList, utils.Map(strings.Split(original.Instance.ESPublicIPWhitelist, ","), formatCIDR)...)
	es.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Instance.InstanceConfiguration.Vpc.VpcID)
	es.EngineVersion = original.Instance.InstanceConfiguration.Version
	es.PublicAllowed = original.Instance.EnableESPublicNetwork

	es.Nodes = utils.Map(original.Nodes, func(node Node) model.ESNode {
		return model.ESNode{
			NodeID: node.InstanceID,
			Name:   node.NodeName,
			IP:     node.InstanceID,
			Class:  utils.UnwrapOr("kibana", node.IsKibana, "es"),
		}
	})

	es.KibanaPublicEndpoint = original.Instance.KibanaPublicDomain
	es.KibanaPrivateEndpoint = original.Instance.KibanaPrivateDomain
	es.KibanaProtocol = utils.UnwrapOr("https", original.Instance.InstanceConfiguration.EnableHTTPS, "http")
	es.KibanaPublicAllowed = original.Instance.EnableKibanaPublicNetwork
	es.KibanaIPWhiteList = append(es.KibanaIPWhiteList, utils.Map(strings.Split(original.Instance.KibanaPrivateIPWhitelist, ","), formatCIDR)...)
	es.KibanaIPWhiteList = append(es.KibanaIPWhiteList, utils.Map(strings.Split(original.Instance.KibanaPublicIPWhitelist, ","), formatCIDR)...)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, es)
	return asset
}

func formatCIDR(cidr string) string {
	cidr = strings.TrimSpace(cidr)
	if strings.Contains(cidr, "/") {
		return cidr
	}
	return cidr + "/32"
}
