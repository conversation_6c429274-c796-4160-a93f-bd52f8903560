package nas

import "AssetStandardizer/graph"

var nasSchema = graph.NodeSchema{
	Label: "NAS",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"status":             {Name: "status"},
		"class":              {Name: "class"},
		"encrypt":            {Name: "encrypt"},
		"domains":            {Name: "domains"},
		"ips":                {Name: "ips"},
	},
}

var nasACLRuleSchema = graph.NodeSchema{
	Label: "NASACLRule",
	Properties: map[string]graph.PropertyRef{
		"uid":         {Name: "uid"},
		"acl_id":      {Name: "acl_id"},
		"priority":    {Name: "priority"},
		"source_cidr": {Name: "source_cidr"},
		"rw_access":   {Name: "rw_access"},
		"user_access": {Name: "user_access"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_ACL_RULE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var labelSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"key":   {Name: "key"},
		"value": {Name: "value"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LABEL",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var subnetSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
