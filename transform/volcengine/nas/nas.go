package nas

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nas"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "nas_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nas_list"] = append(resourceData["nas_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["nas_acl_rule_list"] = append(resourceData["nas_acl_rule_list"],
			utils.GenParamsFromStructSlice(resource.ACLRule)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, nasSchema, resourceData["nas_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, nasACLRuleSchema, resourceData["nas_acl_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type NAS struct {
	MountPoints     []MountPoint     `json:"mountPoints"`
	NAS             NASClass         `json:"nas"`
	PermissionRules []PermissionRule `json:"permissionRules"`
}

type MountPoint struct {
	CreateTime      string          `json:"CreateTime"`
	Domain          string          `json:"Domain"`
	IP              string          `json:"Ip"`
	MountPointID    string          `json:"MountPointId"`
	MountPointName  string          `json:"MountPointName"`
	PermissionGroup PermissionGroup `json:"PermissionGroup"`
	Status          string          `json:"Status"`
	SubnetID        string          `json:"SubnetId"`
	SubnetName      string          `json:"SubnetName"`
	UpdateTime      string          `json:"UpdateTime"`
	VpcID           string          `json:"VpcId"`
	VpcName         string          `json:"VpcName"`
}

type PermissionGroup struct {
	PermissionGroupID   string `json:"PermissionGroupId"`
	PermissionGroupName string `json:"PermissionGroupName"`
}

type NASClass struct {
	CachePerformance CachePerformance `json:"CachePerformance"`
	ChargeType       string           `json:"ChargeType"`
	CreateTime       string           `json:"CreateTime"`
	Description      string           `json:"Description"`
	FileSystemID     string           `json:"FileSystemId"`
	FileSystemName   string           `json:"FileSystemName"`
	FileSystemType   string           `json:"FileSystemType"`
	ProjectName      string           `json:"ProjectName"`
	ProtocolType     string           `json:"ProtocolType"`
	RegionID         string           `json:"RegionId"`
	Status           string           `json:"Status"`
	StorageType      string           `json:"StorageType"`
	UpdateTime       string           `json:"UpdateTime"`
	ZoneID           string           `json:"ZoneId"`
	ZoneName         string           `json:"ZoneName"`
}

type CachePerformance struct {
	BaseBandwidth     int64 `json:"BaseBandwidth"`
	CacheBandwidth    int64 `json:"CacheBandwidth"`
	DataFlowBandwidth int64 `json:"DataFlowBandwidth"`
}

type PermissionRule struct {
	CIDRIP           string `json:"CidrIp"`
	PermissionRuleID string `json:"PermissionRuleId"`
	RwMode           string `json:"RwMode"`
	UserMode         string `json:"UserMode"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NASGraph, error) {
	original := &NAS{}
	resource := &model.NASGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NAS.FileSystemID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nas", original.NAS.FileSystemID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nas"
	resource.Name = original.NAS.Description
	resource.Description = original.NAS.Description

	resource.Status = lo.Ternary(strings.EqualFold(original.NAS.Status, "running"), "on", "off")
	resource.Class = strings.ToLower(original.NAS.ProtocolType)
	resource.IPs = lo.Map(original.MountPoints, func(mountPoint MountPoint, _ int) string { return mountPoint.IP })
	resource.Domains = lo.Map(original.MountPoints, func(mountPoint MountPoint, _ int) string { return mountPoint.Domain })
	resource.VPC = lo.Map(original.MountPoints, func(mountPoint MountPoint, _ int) *model.VPCGraph {
		return &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", mountPoint.VpcID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.Subnet = lo.Map(original.MountPoints, func(mountPoint MountPoint, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", mountPoint.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.ACLRule = lo.Map(original.PermissionRules, func(permissionRule PermissionRule, _ int) *model.NasACLRuleGraph {
		return &model.NasACLRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "nas-acl-rule", permissionRule.PermissionRuleID),
				TargetUID: resource.UID,
			},
			AclID:      permissionRule.PermissionRuleID,
			SourceCidr: provider_utils.FormatCIDR(permissionRule.CIDRIP),
			RWAccess:   lo.Ternary(strings.EqualFold(permissionRule.RwMode, "RW"), "RDWR", "RDONLY"),
			UserAccess: lo.Ternary(strings.EqualFold(permissionRule.UserMode, "no_root_squash"), "no_squash", strings.ToLower(permissionRule.UserMode)),
		}
	})

	return resource, nil
}
