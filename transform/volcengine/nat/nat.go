package nat

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strconv"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nat"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "nat_gateway_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nat_list"] = append(resourceData["nat_list"], utils.GenParamsFromStruct(resource))

		resourceData["snat_rule_list"] = append(resourceData["snat_rule_list"],
			utils.GenParamsFromStructSlice(resource.SNATRules)...,
		)
		resourceData["ip_range_list"] = append(resourceData["ip_range_list"],
			lo.FlatMap(resource.SNATRules, func(snatRule *model.SNATRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(snatRule.IPRange)
			})...,
		)

		resourceData["dnat_rule_list"] = append(resourceData["dnat_rule_list"],
			utils.GenParamsFromStructSlice(resource.DNATRules)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, natSchema, resourceData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, snatRuleSchema, resourceData["snat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dnatRuleSchema, resourceData["dnat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipRangeSchema, resourceData["ip_range_list"], map[string]any{"last_updated": "test"})
}

type NAT struct {
	DnatTable  []DnatTable `json:"dnatTable"`
	NatGateway NatGateway  `json:"natGateway"`
	SnatTable  []SnatTable `json:"snatTable"`
}

type DnatTable struct {
	NatGatewayID  string `json:"NatGatewayId"`
	DnatEntryID   string `json:"DnatEntryId"`
	DnatEntryName string `json:"DnatEntryName"`
	Protocol      string `json:"Protocol"`
	InternalIP    string `json:"InternalIp"`
	InternalPort  string `json:"InternalPort"`
	ExternalIP    string `json:"ExternalIp"`
	ExternalPort  string `json:"ExternalPort"`
	Status        string `json:"Status"`
}

type NatGateway struct {
	BillingType        int64           `json:"BillingType"`
	BusinessStatus     string          `json:"BusinessStatus"`
	CreationTime       string          `json:"CreationTime"`
	DeletedTime        string          `json:"DeletedTime"`
	Description        string          `json:"Description"`
	DnatEntryIDS       []interface{}   `json:"DnatEntryIds"`
	EipAddresses       []NatEipAddress `json:"EipAddresses"`
	ExpiredTime        string          `json:"ExpiredTime"`
	LockReason         string          `json:"LockReason"`
	NatGatewayID       string          `json:"NatGatewayId"`
	NatGatewayName     string          `json:"NatGatewayName"`
	NetworkInterfaceID string          `json:"NetworkInterfaceId"`
	NetworkType        string          `json:"NetworkType"`
	OverdueTime        string          `json:"OverdueTime"`
	ProjectName        string          `json:"ProjectName"`
	SnatEntryIDS       []string        `json:"SnatEntryIds"`
	Spec               string          `json:"Spec"`
	Status             string          `json:"Status"`
	SubnetID           string          `json:"SubnetId"`
	Tags               []Tag           `json:"Tags"`
	UpdatedAt          string          `json:"UpdatedAt"`
	VpcID              string          `json:"VpcId"`
	ZoneID             string          `json:"ZoneId"`
}

type NatEipAddress struct {
	AllocationID string `json:"AllocationId"`
	EipAddress   string `json:"EipAddress"`
	UsingStatus  string `json:"UsingStatus"`
}

type SnatTable struct {
	EipAddress    string `json:"EipAddress"`
	EipID         string `json:"EipId"`
	NatGatewayID  string `json:"NatGatewayId"`
	NatIPID       string `json:"NatIpId"`
	SnatEntryID   string `json:"SnatEntryId"`
	SnatEntryName string `json:"SnatEntryName"`
	SourceCIDR    string `json:"SourceCidr"`
	Status        string `json:"Status"`
	SubnetID      string `json:"SubnetId"`
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NATGraph, error) {
	original := &NAT{}
	resource := &model.NATGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NatGateway.NatGatewayID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nat", original.NatGateway.NatGatewayID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nat"
	resource.Name = original.NatGateway.NatGatewayName
	resource.Description = original.NatGateway.Description
	resource.OriginalLabels = lo.Map(original.NatGateway.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Status = strings.ToLower(original.NatGateway.Status)
	resource.Spec = original.NatGateway.Spec

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.NatGateway.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.NatGateway.SubnetID),
			TargetUID: resource.UID,
		},
	})
	resource.EIP = lo.Map(original.NatGateway.EipAddresses, func(eip NatEipAddress, _ int) *model.EipGraph {
		return &model.EipGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eip", eip.AllocationID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.SNATRules = lo.FilterMap(original.SnatTable, func(snatTable SnatTable, _ int) (*model.SNATRuleGraph, bool) {
		natUID := utils.GenerateUID(resource.Provider, "snat_rule", snatTable.SnatEntryID)
		ipRange, err := provider_utils.ParseIPRange(snatTable.SourceCIDR, natUID)
		if err != nil {
			logger.Errorf("parse snat table ip range error, snatTable: %v, error: %v", snatTable, err)
			return nil, false
		}

		return &model.SNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       natUID,
				TargetUID: resource.UID,
			},
			RuleID:  snatTable.SnatEntryID,
			Name:    snatTable.SnatEntryName,
			Status:  strings.ToLower(snatTable.Status),
			IPRange: []*model.IPRangeGraph{ipRange},
		}, true
	})

	resource.DNATRules = lo.Map(original.DnatTable, func(dnatTable DnatTable, _ int) *model.DNATRuleGraph {
		internalPort, _ := strconv.Atoi(dnatTable.InternalPort)
		externalPort, _ := strconv.Atoi(dnatTable.ExternalPort)
		return &model.DNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "dnat_rule", dnatTable.DnatEntryID),
				TargetUID: resource.UID,
			},
			RuleID:       dnatTable.DnatEntryID,
			Name:         dnatTable.DnatEntryName,
			Status:       strings.ToLower(dnatTable.Status),
			Protocol:     strings.ToLower(dnatTable.Protocol),
			InternalIP:   dnatTable.InternalIP,
			InternalPort: internalPort,
			ExternalIP:   dnatTable.ExternalIP,
			ExternalPort: externalPort,
		}
	})

	return resource, nil
}
