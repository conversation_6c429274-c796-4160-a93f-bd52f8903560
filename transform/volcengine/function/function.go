package function

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "function"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "fc_function_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["function_list"] = append(resourceData["function_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, functionSchema, resourceData["function_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Function struct {
	Function FunctionClass `json:"function"`
}

type FunctionClass struct {
	CodeSizeLimit       int64          `json:"CodeSizeLimit"`
	Command             *string        `json:"Command,omitempty"`
	CreationTime        string         `json:"CreationTime"`
	Description         string         `json:"Description"`
	ExclusiveMode       bool           `json:"ExclusiveMode"`
	ID                  string         `json:"Id"`
	InitializerSEC      int64          `json:"InitializerSec"`
	InstanceType        string         `json:"InstanceType"`
	LastUpdateTime      string         `json:"LastUpdateTime"`
	MaxConcurrency      int64          `json:"MaxConcurrency"`
	MemoryMB            int64          `json:"MemoryMB"`
	Name                string         `json:"Name"`
	Owner               string         `json:"Owner"`
	RequestTimeout      int64          `json:"RequestTimeout"`
	Runtime             string         `json:"Runtime"`
	SourceType          string         `json:"SourceType"`
	TLSConfig           TLSConfig      `json:"TlsConfig"`
	VpcConfig           VpcConfig      `json:"VpcConfig"`
	CodeSize            int64          `json:"CodeSize"`
	SourceLocation      string         `json:"SourceLocation"`
	Envs                []Env          `json:"Envs"`
	ResourcePoolID      string         `json:"ResourcePoolId"`
	TriggersCount       int64          `json:"TriggersCount"`
	UseStatus           string         `json:"UseStatus"`
	DebugInstanceEnable bool           `json:"DebugInstanceEnable"`
	NASStorage          NASStorage     `json:"NasStorage"`
	TosMountConfig      TosMountConfig `json:"TosMountConfig"`
}

type Env struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

type NASStorage struct {
	EnableNAS  bool        `json:"EnableNas"`
	NASConfigs []NASConfig `json:"NasConfigs"`
}

type NASConfig struct {
	FileSystemID   string `json:"FileSystemId"`
	MountPointID   string `json:"MountPointId"`
	RemotePath     string `json:"RemotePath"`
	LocalMountPath string `json:"LocalMountPath"`
	Uid            int64  `json:"Uid"`
	Gid            int64  `json:"Gid"`
}

type TLSConfig struct {
	EnableLog    bool   `json:"EnableLog"`
	TLSProjectID string `json:"TlsProjectId"`
	TLSTopicID   string `json:"TlsTopicId"`
}

type TosMountConfig struct {
	EnableTos   bool            `json:"EnableTos"`
	MountPoints []TosMountPoint `json:"MountPoints"`
}

type TosMountPoint struct {
	Endpoint       string `json:"Endpoint"`
	BucketName     string `json:"BucketName"`
	BucketPath     string `json:"BucketPath"`
	LocalMountPath string `json:"LocalMountPath"`
	ReadOnly       bool   `json:"ReadOnly"`
}

type VpcConfig struct {
	EnableSharedInternetAccess bool     `json:"EnableSharedInternetAccess"`
	EnableVpc                  bool     `json:"EnableVpc"`
	SecurityGroupIDS           []string `json:"SecurityGroupIds"`
	SubnetIDS                  []string `json:"SubnetIds"`
	VpcID                      string   `json:"VpcId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.FunctionGraph, error) {
	original := &Function{}
	resource := &model.FunctionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Function.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "function", original.Function.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "function"
	resource.Name = original.Function.Name
	resource.Description = original.Function.Description

	resource.Runtime = strings.ToLower(original.Function.Runtime)
	resource.LogEnabled = original.Function.TLSConfig.EnableLog
	if resource.LogEnabled {
		resource.LogPath = fmt.Sprintf("/project/%s/topic/%s", original.Function.TLSConfig.TLSProjectID, original.Function.TLSConfig.TLSTopicID)
	}

	if original.Function.NASStorage.EnableNAS {
		resource.NasMountPoints = lo.Map(original.Function.NASStorage.NASConfigs, func(e NASConfig, _ int) string {
			return fmt.Sprintf("%s:%s", e.MountPointID, e.RemotePath)
		})
	}

	if original.Function.TosMountConfig.EnableTos {
		resource.BucketMountPoints = lo.Map(original.Function.TosMountConfig.MountPoints, func(e TosMountPoint, _ int) string {
			return fmt.Sprintf("%s:%s", e.BucketName, e.BucketPath)
		})
	}

	// 火山函数默认支持公网访问
	resource.DirectInternetAccessAllowed = true
	if original.Function.VpcConfig.EnableVpc {
		// 如果火山函数配置了VPC，则需要判断是否支持直接公网访问
		resource.DirectInternetAccessAllowed = original.Function.VpcConfig.EnableSharedInternetAccess
		resource.VPC = append(resource.VPC, &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", original.Function.VpcConfig.VpcID),
				TargetUID: resource.UID,
			},
		})
		resource.Subnet = append(resource.Subnet,
			lo.Map(original.Function.VpcConfig.SubnetIDS, func(subnetID string, _ int) *model.SubnetGraph {
				return &model.SubnetGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "subnet", subnetID),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
		resource.SG = append(resource.SG,
			lo.Map(original.Function.VpcConfig.SecurityGroupIDS, func(securityGroupID string, _ int) *model.SGGraph {
				return &model.SGGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "security-group", securityGroupID),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
	}

	return resource, nil
}
