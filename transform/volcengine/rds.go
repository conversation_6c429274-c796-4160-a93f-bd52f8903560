package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type RdsPostgres struct {
	Instance     PostgresqlInstance `json:"instance"`
	IPWhiteList  []IPWhiteList      `json:"ipWhiteList"`
	Accounts     []PGAccount        `json:"accounts"`
	AuditEnabled bool               `json:"auditEnabled"`
	BackupList   []PGBackupList     `json:"backupList"`
}

type PGBackupList struct {
	BackupEndTime   string `json:"BackupEndTime"`
	BackupFileName  string `json:"BackupFileName"`
	BackupFileSize  int64  `json:"BackupFileSize"`
	BackupID        string `json:"BackupId"`
	BackupProgress  int64  `json:"BackupProgress"`
	BackupStartTime string `json:"BackupStartTime"`
	BackupStatus    string `json:"BackupStatus"`
	BackupType      string `json:"BackupType"`
	CreateType      string `json:"CreateType"`
}

type PGAccount struct {
	AccountName       string `json:"AccountName"`
	AccountPrivileges string `json:"AccountPrivileges"`
	AccountStatus     string `json:"AccountStatus"`
	AccountType       string `json:"AccountType"`
}

type PostgresqlInstance struct {
	AddressObject    []AddressObject `json:"AddressObject"`
	AllowListVersion string          `json:"AllowListVersion"`
	ChargeDetail     ChargeDetail    `json:"ChargeDetail"`
	CreateTime       string          `json:"CreateTime"`
	DBEngineVersion  string          `json:"DBEngineVersion"`
	InstanceID       string          `json:"InstanceId"`
	InstanceName     string          `json:"InstanceName"`
	InstanceStatus   string          `json:"InstanceStatus"`
	InstanceType     string          `json:"InstanceType"`
	NodeNumber       int64           `json:"NodeNumber"`
	NodeSpec         string          `json:"NodeSpec"`
	ProjectName      string          `json:"ProjectName"`
	RegionID         string          `json:"RegionId"`
	StorageSpace     int64           `json:"StorageSpace"`
	StorageType      string          `json:"StorageType"`
	SubnetID         string          `json:"SubnetId"`
	Tags             []Tags          `json:"Tags"`
	VpcID            string          `json:"VpcId"`
	ZoneID           string          `json:"ZoneId"`
	ZoneIDS          []string        `json:"ZoneIds"`
}

type ChargeDetail struct {
	AutoRenew           bool        `json:"AutoRenew"`
	ChargeEndTime       string      `json:"ChargeEndTime"`
	ChargeStartTime     string      `json:"ChargeStartTime"`
	ChargeStatus        string      `json:"ChargeStatus"`
	ChargeType          string      `json:"ChargeType"`
	Number              int64       `json:"Number"`
	OverdueReclaimTime  string      `json:"OverdueReclaimTime"`
	OverdueTime         string      `json:"OverdueTime"`
	Period              int64       `json:"Period"`
	PeriodUnit          string      `json:"PeriodUnit"`
	TempModifyEndTime   interface{} `json:"TempModifyEndTime"`
	TempModifyStartTime interface{} `json:"TempModifyStartTime"`
}

type IPWhiteList struct {
	AllowList string `json:"AllowList"`
}

func parseRdsPostgresql(raw map[string]any) model.Asset {
	original := &RdsPostgres{}
	meta := &model.ObjectMeta{}
	rds := &model.RDS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Instance.InstanceID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "rds", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "rds"
	meta.Name = original.Instance.InstanceName

	if len(original.Instance.AddressObject) > 0 {
		rds.ConnectionAddress = original.Instance.AddressObject[0].Domain
	}
	rds.Engine = "PostgreSQL"
	rds.EngineVersion = strings.TrimPrefix(original.Instance.DBEngineVersion, "PostgreSQL_")
	_, found := utils.Find(original.Instance.AddressObject, func(item AddressObject) bool {
		return item.EipID != ""
	})
	rds.PublicAllowed = found
	rds.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Instance.VpcID)
	rds.IpWhiteList = utils.Flatten(
		utils.Map(original.IPWhiteList, func(ipList IPWhiteList) []string {
			return strings.Split(ipList.AllowList, ",")
		}),
	)

	rds.TDEEnabled = nil
	rds.Accounts = utils.Map(original.Accounts, func(account PGAccount) model.RDSAccount {
		return model.RDSAccount{
			Name:    account.AccountName,
			Enabled: strings.EqualFold(account.AccountStatus, "available"),
			Class:   lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
		}
	})

	rds.BackupAvailable = len(original.BackupList) > 0
	_, foundAuto := lo.Find(original.BackupList, func(backup PGBackupList) bool {
		return strings.EqualFold(backup.CreateType, "system")
	})
	rds.BackupMethod = lo.Ternary(foundAuto, "auto", "manual")

	lastBackupTime := lo.MaxBy(original.BackupList, func(a, b PGBackupList) bool {
		return a.BackupEndTime > b.BackupEndTime
	})
	backuptime, err := time.Parse(time.RFC3339, lastBackupTime.BackupEndTime)
	if err != nil {
		rds.LastBackupTime = 0
	} else {
		rds.LastBackupTime = backuptime.UnixMilli()
	}

	rds.LogFileExists = original.AuditEnabled

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, rds)
	return asset
}

type RdsMysql struct {
	Instance     Instance           `json:"instance"`
	IPWhiteList  []MysqlIPWhiteList `json:"ipWhiteList"`
	Accounts     []MysqlAccount     `json:"accounts"`
	AuditEnabled bool               `json:"auditEnabled"`
	BackupList   []MysqlBackupList  `json:"backupList"`
}

type MysqlBackupList struct {
	BackupEndTime   string `json:"BackupEndTime"`
	BackupFileName  string `json:"BackupFileName"`
	BackupFileSize  int64  `json:"BackupFileSize"`
	BackupID        string `json:"BackupId"`
	BackupMethod    string `json:"BackupMethod"`
	BackupRegion    string `json:"BackupRegion"`
	BackupStartTime string `json:"BackupStartTime"`
	BackupStatus    string `json:"BackupStatus"`
	BackupType      string `json:"BackupType"`
	ConsistentTime  string `json:"ConsistentTime"`
	CreateType      string `json:"CreateType"`
	DownloadStatus  string `json:"DownloadStatus"`
	ExpiredTime     string `json:"ExpiredTime"`
	IsEncrypted     bool   `json:"IsEncrypted"`
	IsExpired       bool   `json:"IsExpired"`
}

type MysqlAccount struct {
	AccountDesc       string             `json:"AccountDesc"`
	AccountName       string             `json:"AccountName"`
	AccountPrivileges []AccountPrivilege `json:"AccountPrivileges"`
	AccountStatus     string             `json:"AccountStatus"`
	AccountType       string             `json:"AccountType"`
	Host              string             `json:"Host"`
}

type AccountPrivilege struct {
	AccountPrivilege       string `json:"AccountPrivilege"`
	AccountPrivilegeDetail string `json:"AccountPrivilegeDetail"`
	DBName                 string `json:"DBName"`
}

type MysqlIPWhiteList struct {
	Metadata               *ResponseMetadata       `json:"Metadata,omitempty"`
	AllowList              string                  `json:"AllowList,omitempty"`
	AllowListCategory      string                  `json:"AllowListCategory"`
	AllowListDesc          string                  `json:"AllowListDesc"`
	AllowListID            string                  `json:"AllowListId"`
	AllowListName          string                  `json:"AllowListName"`
	AllowListType          string                  `json:"AllowListType"`
	AssociatedInstances    []AssociatedInstance    `json:"AssociatedInstances,omitempty"`
	SecurityGroupBindInfos []SecurityGroupBindInfo `json:"SecurityGroupBindInfos"`
	UserAllowList          string                  `json:"UserAllowList,omitempty"`
	AllowListIPNum         int64                   `json:"AllowListIPNum,omitempty"`
	AssociatedInstanceNum  int64                   `json:"AssociatedInstanceNum,omitempty"`
}

type AssociatedInstance struct {
	InstanceID   string `json:"InstanceId"`
	InstanceName string `json:"InstanceName"`
	Vpc          string `json:"VPC"`
}

type ResponseMetadata struct {
	RequestID string      `json:"RequestId"`
	Action    string      `json:"Action"`
	Version   string      `json:"Version"`
	Service   string      `json:"Service"`
	Region    string      `json:"Region"`
	HTTPCode  int64       `json:"HTTPCode"`
	Error     interface{} `json:"Error"`
}

type Instance struct {
	AddressObject       []AddressObject   `json:"AddressObject"`
	AllowListVersion    string            `json:"AllowListVersion"`
	ChargeDetail        ChargeDetail      `json:"ChargeDetail"`
	CreateTime          string            `json:"CreateTime"`
	DBEngineVersion     string            `json:"DBEngineVersion"`
	InstanceID          string            `json:"InstanceId"`
	InstanceName        string            `json:"InstanceName"`
	InstanceStatus      string            `json:"InstanceStatus"`
	InstanceType        string            `json:"InstanceType"`
	LowerCaseTableNames string            `json:"LowerCaseTableNames"`
	MaintenanceWindow   MaintenanceWindow `json:"MaintenanceWindow"`
	NodeNumber          int64             `json:"NodeNumber"`
	NodeSpec            string            `json:"NodeSpec"`
	ProjectName         string            `json:"ProjectName"`
	RegionID            string            `json:"RegionId"`
	StorageSpace        int64             `json:"StorageSpace"`
	StorageType         string            `json:"StorageType"`
	SubnetID            string            `json:"SubnetId"`
	TimeZone            string            `json:"TimeZone"`
	VpcID               string            `json:"VpcId"`
	ZoneID              string            `json:"ZoneId"`
	Tags                []Tags            `json:"Tags"`
}

type AddressObject struct {
	DNSVisibility    bool   `json:"DNSVisibility"`
	Domain           string `json:"Domain"`
	EipID            string `json:"EipId"`
	IPAddress        string `json:"IPAddress"`
	InternetProtocol string `json:"InternetProtocol"`
	NetworkType      string `json:"NetworkType"`
	Port             string `json:"Port"`
	SubnetID         string `json:"SubnetId"`
}

type MaintenanceWindow struct {
	DayKind         string        `json:"DayKind"`
	DayOfMonth      []interface{} `json:"DayOfMonth"`
	DayOfWeek       []string      `json:"DayOfWeek"`
	MaintenanceTime string        `json:"MaintenanceTime"`
}

type SecurityGroupBindInfo struct {
	SecurityGroupID string `json:"SecurityGroupId"`
}

func parseRdsMysql(raw map[string]any) model.Asset {
	original := &RdsMysql{}
	meta := &model.ObjectMeta{}
	rds := &model.RDS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Instance.InstanceID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Instance.Tags, func(e Tags) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "redis", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "rds"
	meta.Name = original.Instance.InstanceName

	if len(original.Instance.AddressObject) > 0 {
		rds.ConnectionAddress = original.Instance.AddressObject[0].Domain
	}
	rds.Engine = "MySQL"
	rds.EngineVersion = strings.TrimPrefix(original.Instance.DBEngineVersion, "MySQL_")
	_, found := utils.Find(original.Instance.AddressObject, func(item AddressObject) bool {
		return item.EipID != ""
	})
	rds.PublicAllowed = found
	rds.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Instance.VpcID)
	rds.IpWhiteList = utils.Flatten(
		utils.Map(original.IPWhiteList, func(ipList MysqlIPWhiteList) []string {
			return strings.Split(ipList.AllowList, ",")
		}),
	)
	rds.SGIDList = utils.Flatten(
		utils.Map(original.IPWhiteList, func(ipList MysqlIPWhiteList) []string {
			results := []string{}
			for _, item := range ipList.SecurityGroupBindInfos {
				results = append(results, item.SecurityGroupID)
			}
			return results

		}),
	)

	rds.TDEEnabled = nil
	rds.Accounts = utils.Map(original.Accounts, func(account MysqlAccount) model.RDSAccount {
		return model.RDSAccount{
			Name:        account.AccountName,
			Enabled:     strings.EqualFold(account.AccountStatus, "available"),
			Class:       lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
			Description: account.AccountDesc,
			Host:        account.Host,
		}
	})

	rds.BackupAvailable = len(original.BackupList) > 0
	_, foundAuto := lo.Find(original.BackupList, func(backup MysqlBackupList) bool {
		return strings.EqualFold(backup.CreateType, "system")
	})
	rds.BackupMethod = lo.Ternary(foundAuto, "auto", "manual")

	lastBackupTime := lo.MaxBy(original.BackupList, func(a, b MysqlBackupList) bool {
		return a.BackupEndTime > b.BackupEndTime
	})
	backuptime, err := time.Parse(time.RFC3339, lastBackupTime.BackupEndTime)
	if err != nil {
		rds.LastBackupTime = 0
	} else {
		rds.LastBackupTime = backuptime.UnixMilli()
	}

	rds.LogFileExists = original.AuditEnabled

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, rds)
	return asset
}
