package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var volumeLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "volume"})

func NewVolumeService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_disk_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformVolume,
		UpdateResources: updateVolumes,
	}
}

func transformVolume(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseVolume(assetMsg)
		if err != nil {
			volumeLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["volume_list"] = append(resourceData["volume_list"], utils.GenParamsFromStruct(resource))

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["image_list"] = append(resourceData["image_list"],
			utils.GenParamsFromStructSlice(resource.Image)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateVolumes(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, volumeSchema, resourceData["volume_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsVolumeSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
}

type Disk struct {
	Disk DiskClass `json:"disk"`
}

type DiskClass struct {
	AutoSnapshotPolicyID   string `json:"AutoSnapshotPolicyId"`
	AutoSnapshotPolicyName string `json:"AutoSnapshotPolicyName"`
	BillingType            int64  `json:"BillingType"`
	CreatedAt              string `json:"CreatedAt"`
	DeleteWithInstance     bool   `json:"DeleteWithInstance"`
	Description            string `json:"Description"`
	DeviceName             string `json:"DeviceName"`
	ErrorDetail            string `json:"ErrorDetail"`
	ExpiredTime            string `json:"ExpiredTime"`
	ImageID                string `json:"ImageId"`
	InstanceID             string `json:"InstanceId"`
	Kind                   string `json:"Kind"`
	OverdueReclaimTime     string `json:"OverdueReclaimTime"`
	OverdueTime            string `json:"OverdueTime"`
	PayType                string `json:"PayType"`
	ProjectName            string `json:"ProjectName"`
	RenewType              int64  `json:"RenewType"`
	Size                   int64  `json:"Size"`
	SnapshotCount          int64  `json:"SnapshotCount"`
	SourceSnapshotID       string `json:"SourceSnapshotId"`
	Status                 string `json:"Status"`
	Tags                   []Tag  `json:"Tags"`
	TradeStatus            int64  `json:"TradeStatus"`
	UpdatedAt              string `json:"UpdatedAt"`
	VolumeID               string `json:"VolumeId"`
	VolumeName             string `json:"VolumeName"`
	VolumeType             string `json:"VolumeType"`
	ZoneID                 string `json:"ZoneId"`
}

type Attachment struct {
	AttachedTime string `json:"AttachedTime"`
	Device       string `json:"Device"`
	InstanceID   string `json:"InstanceId"`
}

func parseVolume(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Disk{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Disk.VolumeID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Disk.VolumeID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Disk.VolumeName
	resource.Description = original.Disk.Description
	resource.OriginalLabels = lo.Map(original.Disk.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.Class = "volume"
	resource.Status = lo.Ternary(strings.EqualFold(original.Disk.Status, "in_use"), "inuse", "available")
	resource.AutoSnapshotEnabled = lo.ToPtr(original.Disk.AutoSnapshotPolicyID != "")
	resource.Image = append(resource.Image, &model.EBSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ebs", original.Disk.ImageID),
			TargetUID: resource.UID,
		},
	})
	if original.Disk.InstanceID != "" {
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.Disk.InstanceID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
