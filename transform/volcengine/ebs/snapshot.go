package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var snapshotLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "snapshot"})

func NewSnapshotService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_snapshot_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSnapshot,
		UpdateResources: updateSnapshots,
	}
}

func transformSnapshot(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSnapshot(assetMsg)
		if err != nil {
			snapshotLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["snapshot_list"] = append(resourceData["snapshot_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateSnapshots(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, snapshotSchema, resourceData["snapshot_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Snapshot struct {
	Snapshot SnapshotClass `json:"snapshot"`
}

type SnapshotClass struct {
	CreationTime    string      `json:"CreationTime"`
	Description     string      `json:"Description"`
	ImageID         string      `json:"ImageId"`
	Progress        int64       `json:"Progress"`
	ProjectName     string      `json:"ProjectName"`
	RetentionDays   interface{} `json:"RetentionDays"`
	SnapshotGroupID string      `json:"SnapshotGroupId"`
	SnapshotID      string      `json:"SnapshotId"`
	SnapshotName    string      `json:"SnapshotName"`
	SnapshotType    string      `json:"SnapshotType"`
	Status          string      `json:"Status"`
	Tags            []Tag       `json:"Tags"`
	VolumeID        string      `json:"VolumeId"`
	VolumeKind      string      `json:"VolumeKind"`
	VolumeName      string      `json:"VolumeName"`
	VolumeSize      int64       `json:"VolumeSize"`
	VolumeStatus    string      `json:"VolumeStatus"`
	VolumeType      string      `json:"VolumeType"`
	ZoneID          string      `json:"ZoneId"`
}

func parseSnapshot(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Snapshot{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Snapshot.SnapshotID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.SnapshotID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Snapshot.SnapshotName
	resource.Description = original.Snapshot.Description
	resource.OriginalLabels = lo.Map(original.Snapshot.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.Class = "snapshot"
	resource.Status = lo.Ternary(strings.EqualFold(original.Snapshot.Status, "available"), "available", "unavailable")
	resource.Volume = append(resource.Volume, &model.EBSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.VolumeID),
			TargetUID: resource.UID,
		},
	})

	return resource, nil
}
