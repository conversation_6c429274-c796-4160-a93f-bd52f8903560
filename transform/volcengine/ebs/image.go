package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var imageLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "image"})

func NewImageService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_image_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformImage,
		UpdateResources: updateImages,
	}
}

func transformImage(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseImage(assetMsg)
		if err != nil {
			imageLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["image_list"] = append(resourceData["image_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateImages(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Image struct {
	Image ImageClass `json:"image"`
}

type ImageClass struct {
	Architecture       string        `json:"Architecture"`
	BootMode           string        `json:"BootMode"`
	CreatedAt          string        `json:"CreatedAt"`
	Description        string        `json:"Description"`
	DetectionResults   interface{}   `json:"DetectionResults"`
	ImageID            string        `json:"ImageId"`
	ImageName          string        `json:"ImageName"`
	ImageOwnerID       string        `json:"ImageOwnerId"`
	IsLTS              bool          `json:"IsLTS"`
	IsSupportCloudInit bool          `json:"IsSupportCloudInit"`
	OSName             string        `json:"OsName"`
	OSType             string        `json:"OsType"`
	Platform           string        `json:"Platform"`
	PlatformVersion    string        `json:"PlatformVersion"`
	ProjectName        string        `json:"ProjectName"`
	ShareStatus        string        `json:"ShareStatus"`
	Size               int64         `json:"Size"`
	Snapshots          []interface{} `json:"Snapshots"`
	Status             string        `json:"Status"`
	Tags               []Tag         `json:"Tags"`
	UpdatedAt          string        `json:"UpdatedAt"`
	VirtualSize        int64         `json:"VirtualSize"`
	Visibility         string        `json:"Visibility"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}



func parseImage(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Image{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Image.ImageID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Image.ImageID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Image.ImageName
	resource.Description = original.Image.Description
	resource.OriginalLabels = lo.Map(original.Image.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.Class = "image"
	resource.Status = lo.Ternary(strings.EqualFold(original.Image.Status, "Available"), "available", "unavailable")

	return resource, nil
}
