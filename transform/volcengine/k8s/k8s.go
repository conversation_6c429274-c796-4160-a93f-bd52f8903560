package k8s

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "k8s"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "k8s_cluster_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["k8s_list"] = append(resourceData["k8s_list"], utils.GenParamsFromStruct(resource))

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, k8sSchema, resourceData["k8s_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
}

type K8S struct {
	Cluster Cluster     `json:"cluster"`
	Subnets []K8SSubnet `json:"subnets"`
}

type Cluster struct {
	ClusterConfig           ClusterConfig  `json:"ClusterConfig"`
	CreateClientToken       string         `json:"CreateClientToken"`
	CreateTime              string         `json:"CreateTime"`
	DeleteProtectionEnabled bool           `json:"DeleteProtectionEnabled"`
	Description             string         `json:"Description"`
	ID                      string         `json:"Id"`
	KubernetesVersion       string         `json:"KubernetesVersion"`
	LoggingConfig           LoggingConfig  `json:"LoggingConfig"`
	Name                    string         `json:"Name"`
	NodeStatistics          NodeStatistics `json:"NodeStatistics"`
	PodsConfig              PodsConfig     `json:"PodsConfig"`
	ServicesConfig          ServicesConfig `json:"ServicesConfig"`
	Status                  Status         `json:"Status"`
	Tags                    []Tag          `json:"Tags"`
	UpdateClientToken       string         `json:"UpdateClientToken"`
	UpdateTime              string         `json:"UpdateTime"`
}

type ClusterConfig struct {
	APIServerEndpoints                 APIServerEndpoints          `json:"ApiServerEndpoints"`
	APIServerPublicAccessConfig        APIServerPublicAccessConfig `json:"ApiServerPublicAccessConfig"`
	APIServerPublicAccessEnabled       bool                        `json:"ApiServerPublicAccessEnabled"`
	ResourcePublicAccessDefaultEnabled bool                        `json:"ResourcePublicAccessDefaultEnabled"`
	SecurityGroupIDS                   []string                    `json:"SecurityGroupIds"`
	SubnetIDS                          []string                    `json:"SubnetIds"`
	VpcID                              string                      `json:"VpcId"`
}

type APIServerEndpoints struct {
	PrivateIP IP `json:"PrivateIp"`
	PublicIP  IP `json:"PublicIp"`
}

type IP struct {
	Ipv4 string `json:"Ipv4"`
}

type APIServerPublicAccessConfig struct {
	AccessSourceIpsv4         interface{}               `json:"AccessSourceIpsv4"`
	PublicAccessNetworkConfig PublicAccessNetworkConfig `json:"PublicAccessNetworkConfig"`
}

type PublicAccessNetworkConfig struct {
	Bandwidth   int64  `json:"Bandwidth"`
	BillingType int64  `json:"BillingType"`
	ISP         string `json:"Isp"`
}

type LoggingConfig struct {
	LogProjectID string     `json:"LogProjectId"`
	LogSetups    []LogSetup `json:"LogSetups"`
}

type LogSetup struct {
	Enabled    bool   `json:"Enabled"`
	LogTopicID string `json:"LogTopicId"`
	LogTTL     int64  `json:"LogTtl"`
	LogType    string `json:"LogType"`
}

type NodeStatistics struct {
	CreatingCount int64 `json:"CreatingCount"`
	DeletingCount int64 `json:"DeletingCount"`
	FailedCount   int64 `json:"FailedCount"`
	RunningCount  int64 `json:"RunningCount"`
	TotalCount    int64 `json:"TotalCount"`
	UpdatingCount int64 `json:"UpdatingCount"`
}

type PodsConfig struct {
	FlannelConfig  FlannelConfig `json:"FlannelConfig"`
	PodNetworkMode string        `json:"PodNetworkMode"`
	VpcCniConfig   VpcCniConfig  `json:"VpcCniConfig"`
}

type FlannelConfig struct {
	MaxPodsPerNode int64    `json:"MaxPodsPerNode"`
	PodCidrs       []string `json:"PodCidrs"`
}

type VpcCniConfig struct {
	SubnetIDS []string `json:"SubnetIds"`
}

type ServicesConfig struct {
	ServiceCidrsv4 []string `json:"ServiceCidrsv4"`
}

type Status struct {
	Conditions []Condition `json:"Conditions"`
	Phase      string      `json:"Phase"`
}

type Condition struct {
	Type string `json:"Type"`
}

type Tag struct {
	Key   string `json:"Key"`
	Type  string `json:"Type,omitempty"`
	Value string `json:"Value"`
}

type K8SSubnet struct {
	CIDRBlock string `json:"CidrBlock"`
	SubnetID  string `json:"SubnetId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.K8SGraph, error) {
	original := &K8S{}
	resource := &model.K8SGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cluster.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "kubernetes", original.Cluster.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "kubernetes"
	resource.Name = original.Cluster.Name
	resource.Description = original.Cluster.Description
	resource.OriginalLabels = lo.Map(original.Cluster.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Status = lo.Ternary(strings.EqualFold(original.Cluster.Status.Phase, "Running"), "running", "stopped")
	resource.EngineVersion = original.Cluster.KubernetesVersion

	if len(original.Cluster.ClusterConfig.APIServerEndpoints.PublicIP.Ipv4) > 0 {
		resource.PublicEndpoint = fmt.Sprintf("https://%s:%d", original.Cluster.ClusterConfig.APIServerEndpoints.PublicIP.Ipv4, 6443)
	}
	if len(original.Cluster.ClusterConfig.APIServerEndpoints.PrivateIP.Ipv4) > 0 {
		resource.PrivateEndpoint = fmt.Sprintf("https://%s:%d", original.Cluster.ClusterConfig.APIServerEndpoints.PrivateIP.Ipv4, 6443)
	}

	if len(original.Cluster.PodsConfig.FlannelConfig.PodCidrs) > 0 {
		resource.PodCIDRs = append(resource.PodCIDRs, original.Cluster.PodsConfig.FlannelConfig.PodCidrs...)
	}
	if len(original.Subnets) > 0 {
		resource.PodCIDRs = append(resource.PodCIDRs, lo.Map(original.Subnets, func(e K8SSubnet, _ int) string { return e.CIDRBlock })...)
	}
	resource.ServiceCIDRs = original.Cluster.ServicesConfig.ServiceCidrsv4
	_, found := lo.Find(original.Cluster.LoggingConfig.LogSetups,
		func(logSetup LogSetup) bool { return logSetup.LogType == "Audit" && logSetup.Enabled },
	)
	resource.AuditLogEnabled = lo.ToPtr(found)

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Cluster.ClusterConfig.VpcID),
			TargetUID: resource.UID,
		},
	})
	if len(original.Cluster.ClusterConfig.SubnetIDS) > 0 {
		resource.Subnet = append(resource.Subnet, lo.Map(original.Cluster.ClusterConfig.SubnetIDS, func(subnetID string, _ int) *model.SubnetGraph {
			return &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", subnetID),
				TargetUID: resource.UID,
				},
			}
		})...)
	}
	resource.SG = append(resource.SG, lo.Map(original.Cluster.ClusterConfig.SecurityGroupIDS, func(securityGroupID string, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", securityGroupID),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
