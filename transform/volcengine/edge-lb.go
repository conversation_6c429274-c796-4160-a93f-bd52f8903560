package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"slices"
	"strings"
	"time"
)

type EdgeAlb struct {
	Alb         EdgeAlbClass      `json:"alb"`
	AlbRulePath []EdgeAlbRulePath `json:"alb_rulePath"`
}

type EdgeAlbClass struct {
	Identity            string            `json:"identity"`
	Name                string            `json:"name"`
	Country             string            `json:"country"`
	Region              string            `json:"region"`
	Province            string            `json:"province"`
	City                string            `json:"city"`
	ISP                 string            `json:"isp"`
	Alias               string            `json:"alias"`
	RsType              int64             `json:"rs_type"`
	Status              int64             `json:"status"`
	VpcIdentity         string            `json:"vpc_identity"`
	VpcName             string            `json:"vpc_name"`
	EipType             int64             `json:"eip_type"`
	BillingMode         int64             `json:"billing_mode"`
	PaymentMethod       int64             `json:"payment_method"`
	Desc                string            `json:"desc"`
	NetworkType         int64             `json:"network_type"`
	Listeners           []EdgeAlbListener `json:"listeners"`
	Eips                []EdgeAlbEip      `json:"eips"`
	Qps                 int64             `json:"qps"`
	IsEmpty             bool              `json:"is_empty"`
	ApplicationIdentity string            `json:"application_identity"`
	ApplicationName     string            `json:"application_name"`
	CreateTime          int64             `json:"create_time"`
	UpdateTime          int64             `json:"update_time"`
	Clusters            []EdgeCluster     `json:"clusters"`
	BandwidthPeak       int64             `json:"bandwidth_peak"`
}

type EdgeAlbEip struct {
	Identity      string `json:"identity"`
	EipAddr       string `json:"eip_addr"`
	BandwidthPeak int64  `json:"bandwidth_peak"`
	ClusterName   string `json:"cluster_name"`
	Name          string `json:"name"`
	EipType       int64  `json:"eip_type"`
	Region        string `json:"region"`
	City          string `json:"city"`
	ISP           string `json:"isp"`
	ClusterAlias  string `json:"cluster_alias"`
	Status        any    `json:"status"`
}

type EdgeAlbListener struct {
	Identity             string                 `json:"identity"`
	Name                 string                 `json:"name"`
	ListenProtocol       int                    `json:"listen_protocol"`
	ListenPort           int                    `json:"listen_port"`
	XForwardedFor        bool                   `json:"x_forwarded_for"`
	RedirectTo           string                 `json:"redirect_to"`
	TLSStrategy          int64                  `json:"tls_strategy"`
	IdleTimeout          int64                  `json:"idle_timeout"`
	RequestTimeout       int64                  `json:"request_timeout"`
	ResponseTimeout      int64                  `json:"response_timeout"`
	Desc                 string                 `json:"desc"`
	CreateTime           int64                  `json:"create_time"`
	UpdateTime           int64                  `json:"update_time"`
	RedirectToName       string                 `json:"redirect_to_name"`
	TLSStrategyMultiList []TLSStrategyMultiList `json:"tls_strategy_multi_list"`
	Http2Enabled         bool                   `json:"http2_enabled"`
	QuicEnabled          bool                   `json:"quic_enabled"`
	ProxyBufferSize      int64                  `json:"proxy_buffer_size"`
}

type TLSStrategyMultiList struct {
	Version         string   `json:"version"`
	EncryptAlgoList []string `json:"encrypt_algo_list"`
}

type EdgeAlbRulePath struct {
	Identity        string         `json:"identity"`
	Path            string         `json:"path"`
	CustomHeader    interface{}    `json:"custom_header"`
	CustomParam     interface{}    `json:"custom_param"`
	CustomRetryList interface{}    `json:"custom_retry_list"`
	RsGroup         EdgeAlbRsGroup `json:"rs_group"`
}

type EdgeAlbRsGroup struct {
	Identity            string               `json:"identity"`
	Name                string               `json:"name"`
	LoadbalanceStrategy string               `json:"loadbalance_strategy"`
	HealthCheck         EdgeAlbHealthCheck   `json:"health_check"`
	StickySession       EdgeAlbStickySession `json:"sticky_session"`
	RsList              []EdgeAlbRsList      `json:"rs_list"`
}

type EdgeAlbHealthCheck struct {
	Protocol          string `json:"protocol"`
	Port              int64  `json:"port"`
	Interval          int64  `json:"interval"`
	ResponseTimeout   int64  `json:"response_timeout"`
	UnhealthThreshold int64  `json:"unhealth_threshold"`
	HealthThreshold   int64  `json:"health_threshold"`
	HTTPHost          string `json:"http_host"`
	HTTPPath          string `json:"http_path"`
	HTTPSuccessCodes  string `json:"http_success_codes"`
	HTTPMethodType    string `json:"http_method_type"`
	HTTPVersion       string `json:"http_version"`
}

type EdgeAlbRsList struct {
	Identity      string `json:"identity"`
	Name          string `json:"name"`
	InternalIP    string `json:"internal_ip"`
	Port          int    `json:"port"`
	Weight        int    `json:"weight"`
	HealthyStatus bool   `json:"healthy_status"`
	Enable        bool   `json:"enable"`
	InternalIpv4  string `json:"internal_ipv4"`
	InternalIpv6  string `json:"internal_ipv6"`
}

type EdgeAlbStickySession struct {
	CookieDealType int64  `json:"cookie_deal_type"`
	CookieTimeout  int64  `json:"cookie_timeout"`
	CookieName     string `json:"cookie_name"`
}

func parseEdgeAlb(raw map[string]any) model.Asset {
	original := &EdgeAlb{}
	meta := &model.ObjectMeta{}
	lb := &model.LB{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Alb.Identity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "edge-lb", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Alb.Desc
	meta.Kind = "edge-lb"
	meta.Name = original.Alb.Name

	lb.Class = "application"
	lb.Status = utils.UnwrapOr("active", original.Alb.Status == 2, "inactive")
	lb.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Alb.VpcIdentity)

	lb.PublicIPList = utils.Map(original.Alb.Eips, func(e EdgeAlbEip) string {
		return e.EipAddr
	})

	lb.ListenerIDList = utils.Map(original.Alb.Listeners, func(listener EdgeAlbListener) string {
		return utils.GenerateUID(ProviderID, "edge-lb-listener", listener.Identity)
	})

	lb.Servers = utils.Flatten(utils.Map(original.AlbRulePath, func(e EdgeAlbRulePath) []model.LbServer {
		return utils.Map(e.RsGroup.RsList, func(e EdgeAlbRsList) model.LbServer {
			return model.LbServer{
				OriginalID: e.Identity,
				Uid:        utils.GenerateUID(meta.Provider, "edge-ecs", e.Identity),
				Class:      "edge-ecs",
				IP:         e.InternalIP,
				Port:       e.Port,
				Weight:     e.Weight,
			}
		})
	}))

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}

type EdgeClb struct {
	Clb EdgeClbClass `json:"clb"`
}

type EdgeClbClass struct {
	Identity        string            `json:"identity"`
	Name            string            `json:"name"`
	Region          string            `json:"region"`
	City            string            `json:"city"`
	ClusterName     string            `json:"cluster_name"`
	ClusterAlias    string            `json:"cluster_alias"`
	VpcName         string            `json:"vpc_name"`
	VpcIdentity     string            `json:"vpc_identity"`
	VpcSubnets      []string          `json:"vpc_subnets"`
	Status          string            `json:"status"`
	IPS             []EdgeClbIP       `json:"ips"`
	Listeners       []EdgeClbListener `json:"listeners"`
	Desc            string            `json:"desc"`
	BillingMethod   string            `json:"billing_method"`
	CreateTime      int64             `json:"create_time"`
	UpdateTime      int64             `json:"update_time"`
	LBType          string            `json:"lb_type"`
	AccountIdentity int64             `json:"account_identity"`
	RsType          string            `json:"rs_type"`
	Clusters        []EdgeCluster     `json:"clusters"`
	ISP             string            `json:"isp"`
	EipNum          int64             `json:"eip_num"`
	IsBareMetal     bool              `json:"is_bare_metal"`
	ForwardMode     string            `json:"forward_mode"`
}

type EdgeClbIP struct {
	IPIdentity    string `json:"ipIdentity"`
	IP            string `json:"ip"`
	IPName        string `json:"ip_name"`
	IPType        string `json:"ip_type"`
	BandwidthPeak string `json:"bandwidth_peak"`
	ACK           bool   `json:"ack"`
	Color         string `json:"color"`
}

type EdgeClbListener struct {
	Name                string             `json:"name"`
	ListenPortProtocol  string             `json:"listen_port_protocol"`
	ListenPort          int                `json:"listen_port"`
	LoadbalanceStrategy string             `json:"loadbalance_strategy"`
	Desc                string             `json:"desc"`
	Endpoints           []EdgeClbEndpoint  `json:"endpoints"`
	ListenerUniq        string             `json:"listener_uniq"`
	DefaultEndpointPort int64              `json:"default_endpoint_port"`
	HealthCheck         EdgeClbHealthCheck `json:"health_check"`
}

type EdgeClbEndpoint struct {
	EndpointIdentity string              `json:"endpoint_identity"`
	EndpointName     string              `json:"endpoint_name"`
	EndpointType     string              `json:"endpoint_type"`
	Port             int                 `json:"port"`
	Weight           int                 `json:"weight"`
	InternalIPS      []EdgeClbEndpointIP `json:"internal_ips"`
	ExternalIPS      []EdgeClbEndpointIP `json:"external_ips"`
	MACAddr          string              `json:"mac_addr"`
	HealthyStatus    bool                `json:"healthy_status"`
	EndpointUniq     string              `json:"endpoint_uniq"`
}

type EdgeClbHealthCheck struct {
	Protocol         string `json:"protocol"`
	Port             int64  `json:"port"`
	Timeout          int64  `json:"timeout"`
	Period           int64  `json:"period"`
	FailureThreshold int64  `json:"failure_threshold"`
	SuccessThreshold int64  `json:"success_threshold"`
	HTTPHost         string `json:"http_host"`
	HTTPPath         string `json:"http_path"`
	HTTPSuccessCodes string `json:"http_success_codes"`
	UDPCheckString   string `json:"udp_check_string"`
	UseEndpointPort  bool   `json:"use_endpoint_port"`
}

type EdgeClbEndpointIP struct {
	IPAddr    string `json:"ip_addr"`
	IPVersion string `json:"ip_version"`
	ISP       string `json:"isp"`
	Primary   bool   `json:"primary"`
}

func parseEdgeClb(raw map[string]any) model.Asset {
	original := &EdgeClb{}
	meta := &model.ObjectMeta{}
	lb := &model.LB{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Clb.Identity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "edge-lb", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "edge-lb"
	meta.Description = original.Clb.Desc
	meta.Name = original.Clb.Name

	lb.Class = "mixed"
	lb.Status = utils.UnwrapOr("active", slices.Contains([]string{"bound", "unbound"}, strings.ToLower(original.Clb.Status)), "inactive")
	lb.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Clb.VpcIdentity)

	lb.PublicIPList = utils.Map(original.Clb.IPS, func(e EdgeClbIP) string {
		return e.IP
	})

	lb.ListenerIDList = utils.Map(original.Clb.Listeners, func(e EdgeClbListener) string {
		return utils.GenerateUID(ProviderID, "edge-lb-listener", e.ListenerUniq)
	})

	lb.Servers = utils.Flatten(utils.Map(original.Clb.Listeners, func(e EdgeClbListener) []model.LbServer {
		return utils.FilterMap(e.Endpoints, func(e EdgeClbEndpoint) (model.LbServer, bool) {
			if e.EndpointType != "veen" || len(e.InternalIPS) == 0 {
				return model.LbServer{}, false
			}

			ip, found := utils.FindMap(e.InternalIPS, func(e EdgeClbEndpointIP) (string, bool) {
				return e.IPAddr, e.Primary
			})
			if !found {
				ip = e.InternalIPS[0].IPAddr
			}

			return model.LbServer{
				OriginalID: e.EndpointIdentity,
				Uid:        utils.GenerateUID(meta.Provider, "edge-ecs", e.EndpointIdentity),
				Class:      "edge-ecs",
				IP:         ip,
				Port:       e.Port,
				Weight:     e.Weight,
			}, true
		})
	}))

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}
