package rds

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var mysqlLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewMysqlService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "rds_mysql_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformMysql,
		UpdateResources: updateResourcesMysql,
	}
}

func transformMysql(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseMysql(assetMsg)
		if err != nil {
			mysqlLogger.Errorf("parseMysql failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["rds_list"] = append(resourceData["rds_list"], utils.GenParamsFromStruct(resource))

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResourcesMysql(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, rdsSchema, resourceData["rds_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, rdsAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type RdsMysql struct {
	Instance     Instance           `json:"instance"`
	IPWhiteList  []MysqlIPWhiteList `json:"ipWhiteList"`
	Accounts     []MysqlAccount     `json:"accounts"`
	AuditEnabled bool               `json:"auditEnabled"`
	BackupList   []MysqlBackupList  `json:"backupList"`
}

type MysqlBackupList struct {
	BackupEndTime   string `json:"BackupEndTime"`
	BackupFileName  string `json:"BackupFileName"`
	BackupFileSize  int64  `json:"BackupFileSize"`
	BackupID        string `json:"BackupId"`
	BackupMethod    string `json:"BackupMethod"`
	BackupRegion    string `json:"BackupRegion"`
	BackupStartTime string `json:"BackupStartTime"`
	BackupStatus    string `json:"BackupStatus"`
	BackupType      string `json:"BackupType"`
	ConsistentTime  string `json:"ConsistentTime"`
	CreateType      string `json:"CreateType"`
	DownloadStatus  string `json:"DownloadStatus"`
	ExpiredTime     string `json:"ExpiredTime"`
	IsEncrypted     bool   `json:"IsEncrypted"`
	IsExpired       bool   `json:"IsExpired"`
}

type MysqlAccount struct {
	AccountDesc       string             `json:"AccountDesc"`
	AccountName       string             `json:"AccountName"`
	AccountPrivileges []AccountPrivilege `json:"AccountPrivileges"`
	AccountStatus     string             `json:"AccountStatus"`
	AccountType       string             `json:"AccountType"`
	Host              string             `json:"Host"`
}

type AccountPrivilege struct {
	AccountPrivilege       string `json:"AccountPrivilege"`
	AccountPrivilegeDetail string `json:"AccountPrivilegeDetail"`
	DBName                 string `json:"DBName"`
}

type MysqlIPWhiteList struct {
	Metadata               *ResponseMetadata       `json:"Metadata,omitempty"`
	AllowList              string                  `json:"AllowList,omitempty"`
	AllowListCategory      string                  `json:"AllowListCategory"`
	AllowListDesc          string                  `json:"AllowListDesc"`
	AllowListID            string                  `json:"AllowListId"`
	AllowListName          string                  `json:"AllowListName"`
	AllowListType          string                  `json:"AllowListType"`
	AssociatedInstances    []AssociatedInstance    `json:"AssociatedInstances,omitempty"`
	SecurityGroupBindInfos []SecurityGroupBindInfo `json:"SecurityGroupBindInfos"`
	UserAllowList          string                  `json:"UserAllowList,omitempty"`
	AllowListIPNum         int64                   `json:"AllowListIPNum,omitempty"`
	AssociatedInstanceNum  int64                   `json:"AssociatedInstanceNum,omitempty"`
}

type AssociatedInstance struct {
	InstanceID   string `json:"InstanceId"`
	InstanceName string `json:"InstanceName"`
	Vpc          string `json:"VPC"`
}

type ResponseMetadata struct {
	RequestID string      `json:"RequestId"`
	Action    string      `json:"Action"`
	Version   string      `json:"Version"`
	Service   string      `json:"Service"`
	Region    string      `json:"Region"`
	HTTPCode  int64       `json:"HTTPCode"`
	Error     interface{} `json:"Error"`
}

type Instance struct {
	AddressObject       []AddressObject   `json:"AddressObject"`
	AllowListVersion    string            `json:"AllowListVersion"`
	ChargeDetail        ChargeDetail      `json:"ChargeDetail"`
	CreateTime          string            `json:"CreateTime"`
	DBEngineVersion     string            `json:"DBEngineVersion"`
	InstanceID          string            `json:"InstanceId"`
	InstanceName        string            `json:"InstanceName"`
	InstanceStatus      string            `json:"InstanceStatus"`
	InstanceType        string            `json:"InstanceType"`
	LowerCaseTableNames string            `json:"LowerCaseTableNames"`
	MaintenanceWindow   MaintenanceWindow `json:"MaintenanceWindow"`
	NodeNumber          int64             `json:"NodeNumber"`
	NodeSpec            string            `json:"NodeSpec"`
	ProjectName         string            `json:"ProjectName"`
	RegionID            string            `json:"RegionId"`
	StorageSpace        int64             `json:"StorageSpace"`
	StorageType         string            `json:"StorageType"`
	SubnetID            string            `json:"SubnetId"`
	TimeZone            string            `json:"TimeZone"`
	VpcID               string            `json:"VpcId"`
	ZoneID              string            `json:"ZoneId"`
	Tags                []Tag             `json:"Tags"`
}

type AddressObject struct {
	DNSVisibility    bool   `json:"DNSVisibility"`
	Domain           string `json:"Domain"`
	EipID            string `json:"EipId"`
	IPAddress        string `json:"IPAddress"`
	InternetProtocol string `json:"InternetProtocol"`
	NetworkType      string `json:"NetworkType"`
	Port             string `json:"Port"`
	SubnetID         string `json:"SubnetId"`
}

type MaintenanceWindow struct {
	DayKind         string        `json:"DayKind"`
	DayOfMonth      []interface{} `json:"DayOfMonth"`
	DayOfWeek       []string      `json:"DayOfWeek"`
	MaintenanceTime string        `json:"MaintenanceTime"`
}

type SecurityGroupBindInfo struct {
	SecurityGroupID string `json:"SecurityGroupId"`
}

func parseMysql(assetMsg *model.AssetMessage) (*model.RDSGraph, error) {
	original := &RdsMysql{}
	resource := &model.RDSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "rds", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "rds"
	resource.Name = original.Instance.InstanceName
	resource.OriginalLabels = lo.Map(original.Instance.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.Key, tag.Value, resource.UID)
	})

	if len(original.Instance.AddressObject) > 0 {
		resource.ConnectionAddress = original.Instance.AddressObject[0].Domain
	}
	resource.Engine = "MySQL"
	resource.EngineVersion = strings.TrimPrefix(original.Instance.DBEngineVersion, "MySQL_")
	_, found := lo.Find(original.Instance.AddressObject, func(item AddressObject) bool {
		return item.EipID != ""
	})
	resource.PublicAllowed = found
	resource.TDEEnabled = nil
	resource.IpWhiteList = lo.Flatten(
		lo.Map(original.IPWhiteList, func(ip MysqlIPWhiteList, _ int) []string {
			return lo.Map(strings.Split(ip.AllowList, ","), func(ip string, _ int) string {
				return provider_utils.FormatCIDR(ip)
			})
		}),
	)
	resource.BackupAvailable = len(original.BackupList) > 0
	_, foundAuto := lo.Find(original.BackupList, func(backup MysqlBackupList) bool {
		return strings.EqualFold(backup.CreateType, "system")
	})
	resource.BackupMethod = lo.Ternary(foundAuto, "auto", "manual")

	lastBackupTime := lo.MaxBy(original.BackupList, func(a, b MysqlBackupList) bool {
		return a.BackupEndTime > b.BackupEndTime
	})
	backuptime, err := time.Parse(time.RFC3339, lastBackupTime.BackupEndTime)
	if err != nil {
		resource.LastBackupTime = 0
	} else {
		resource.LastBackupTime = backuptime.UnixMilli()
	}
	resource.LogFileExists = original.AuditEnabled

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.SubnetID),
			TargetUID: resource.UID,
		},
	})

	resource.SG = append(resource.SG, lo.Flatten(
		lo.Map(original.IPWhiteList, func(sg MysqlIPWhiteList, _ int) []*model.SGGraph {
			results := []*model.SGGraph{}
			for _, item := range sg.SecurityGroupBindInfos {
				results = append(results, &model.SGGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "security-group", item.SecurityGroupID),
						TargetUID: resource.UID,
					},
				})
			}
			return results
		}),
	)...)

	resource.Accounts = lo.Map(original.Accounts, func(account MysqlAccount, _ int) *model.RDSAccountGraph {
		return &model.RDSAccountGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "rds-account", account.AccountName),
				TargetUID: resource.UID,
			},
			Name:        account.AccountName,
			Enabled:     strings.EqualFold(account.AccountStatus, "available"),
			Class:       lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
			Description: account.AccountDesc,
		}
	})

	return resource, nil
}
