package rds

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var pgLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "rds_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformPg,
		UpdateResources: updatePg,
	}
}

func transformPg(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parsePg(assetMsg)
		if err != nil {
			pgLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["rds_list"] = append(resourceData["rds_list"], utils.GenParamsFromStruct(resource))

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updatePg(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, rdsSchema, resourceData["rds_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, rdsAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type RdsPostgres struct {
	Instance     PostgresqlInstance `json:"instance"`
	IPWhiteList  []IPWhiteList      `json:"ipWhiteList"`
	Accounts     []PGAccount        `json:"accounts"`
	AuditEnabled bool               `json:"auditEnabled"`
	BackupList   []PGBackupList     `json:"backupList"`
}

type PGBackupList struct {
	BackupEndTime   string `json:"BackupEndTime"`
	BackupFileName  string `json:"BackupFileName"`
	BackupFileSize  int64  `json:"BackupFileSize"`
	BackupID        string `json:"BackupId"`
	BackupProgress  int64  `json:"BackupProgress"`
	BackupStartTime string `json:"BackupStartTime"`
	BackupStatus    string `json:"BackupStatus"`
	BackupType      string `json:"BackupType"`
	CreateType      string `json:"CreateType"`
}

type PGAccount struct {
	AccountName       string `json:"AccountName"`
	AccountPrivileges string `json:"AccountPrivileges"`
	AccountStatus     string `json:"AccountStatus"`
	AccountType       string `json:"AccountType"`
}

type PostgresqlInstance struct {
	AddressObject    []AddressObject `json:"AddressObject"`
	AllowListVersion string          `json:"AllowListVersion"`
	ChargeDetail     ChargeDetail    `json:"ChargeDetail"`
	CreateTime       string          `json:"CreateTime"`
	DBEngineVersion  string          `json:"DBEngineVersion"`
	InstanceID       string          `json:"InstanceId"`
	InstanceName     string          `json:"InstanceName"`
	InstanceStatus   string          `json:"InstanceStatus"`
	InstanceType     string          `json:"InstanceType"`
	NodeNumber       int64           `json:"NodeNumber"`
	NodeSpec         string          `json:"NodeSpec"`
	ProjectName      string          `json:"ProjectName"`
	RegionID         string          `json:"RegionId"`
	StorageSpace     int64           `json:"StorageSpace"`
	StorageType      string          `json:"StorageType"`
	SubnetID         string          `json:"SubnetId"`
	Tags             []Tag           `json:"Tags"`
	VpcID            string          `json:"VpcId"`
	ZoneID           string          `json:"ZoneId"`
	ZoneIDS          []string        `json:"ZoneIds"`
}

type ChargeDetail struct {
	AutoRenew           bool        `json:"AutoRenew"`
	ChargeEndTime       string      `json:"ChargeEndTime"`
	ChargeStartTime     string      `json:"ChargeStartTime"`
	ChargeStatus        string      `json:"ChargeStatus"`
	ChargeType          string      `json:"ChargeType"`
	Number              int64       `json:"Number"`
	OverdueReclaimTime  string      `json:"OverdueReclaimTime"`
	OverdueTime         string      `json:"OverdueTime"`
	Period              int64       `json:"Period"`
	PeriodUnit          string      `json:"PeriodUnit"`
	TempModifyEndTime   interface{} `json:"TempModifyEndTime"`
	TempModifyStartTime interface{} `json:"TempModifyStartTime"`
}

type IPWhiteList struct {
	AllowList string `json:"AllowList"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parsePg(assetMsg *model.AssetMessage) (*model.RDSGraph, error) {
	original := &RdsPostgres{}
	resource := &model.RDSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "rds", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "rds"
	resource.Name = original.Instance.InstanceName
	resource.OriginalLabels = lo.Map(original.Instance.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.Key, tag.Value, resource.UID)
	})

	resource.ConnectionAddress = original.Instance.AddressObject[0].Domain
	resource.Engine = "PostgreSQL"
	resource.EngineVersion = strings.TrimPrefix(original.Instance.DBEngineVersion, "PostgreSQL_")
	_, found := lo.Find(original.Instance.AddressObject, func(item AddressObject) bool {
		return item.EipID != ""
	})
	resource.PublicAllowed = found
	resource.TDEEnabled = nil
	resource.IpWhiteList = lo.Flatten(
		lo.Map(original.IPWhiteList, func(ip IPWhiteList, _ int) []string {
			return lo.Map(strings.Split(ip.AllowList, ","), func(ip string, _ int) string {
				return provider_utils.FormatCIDR(ip)
			})
		}),
	)

	resource.BackupAvailable = len(original.BackupList) > 0
	_, foundAuto := lo.Find(original.BackupList, func(backup PGBackupList) bool {
		return strings.EqualFold(backup.CreateType, "system")
	})
	resource.BackupMethod = lo.Ternary(foundAuto, "auto", "manual")
	lastBackupTime := lo.MaxBy(original.BackupList, func(a, b PGBackupList) bool {
		return a.BackupEndTime > b.BackupEndTime
	})
	backuptime, err := time.Parse(time.RFC3339, lastBackupTime.BackupEndTime)
	if err != nil {
		resource.LastBackupTime = 0
	} else {
		resource.LastBackupTime = backuptime.UnixMilli()
	}
	resource.LogFileExists = original.AuditEnabled

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.SubnetID),
			TargetUID: resource.UID,
		},
	})

	resource.Accounts = lo.Map(original.Accounts, func(account PGAccount, _ int) *model.RDSAccountGraph {
		return &model.RDSAccountGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "rds-account", account.AccountName),
				TargetUID: resource.UID,
			},
			Name:    account.AccountName,
			Enabled: strings.EqualFold(account.AccountStatus, "available"),
			Class:   lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
		}
	})

	return resource, nil
}
