package elasticsearch

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "elasticsearch"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "es_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["es_list"] = append(resourceData["es_list"], utils.GenParamsFromStruct(resource))

		resourceData["es_node_list"] = append(resourceData["es_node_list"],
			utils.GenParamsFromStructSlice(resource.Node)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, esSchema, resourceData["es_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, esNodeSchema, resourceData["es_node_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
}

type Es struct {
	Instance EsInstance `json:"instance"`
	Nodes    []Node     `json:"nodes"`
}

type EsInstance struct {
	ChargeEnabled                   bool                  `json:"ChargeEnabled"`
	CreateTime                      string                `json:"CreateTime"`
	DeletionProtection              bool                  `json:"DeletionProtection"`
	KibanaPrivateIPWhitelist        string                `json:"KibanaPrivateIpWhitelist"`
	KibanaPublicIPWhitelist         string                `json:"KibanaPublicIpWhitelist"`
	EnableESPrivateNetwork          bool                  `json:"EnableESPrivateNetwork"`
	EnableESPublicNetwork           bool                  `json:"EnableESPublicNetwork"`
	EnableKibanaPrivateNetwork      bool                  `json:"EnableKibanaPrivateNetwork"`
	EnableKibanaPublicNetwork       bool                  `json:"EnableKibanaPublicNetwork"`
	EnableESPrivateDomainPublic     bool                  `json:"EnableESPrivateDomainPublic"`
	EnableKibanaPrivateDomainPublic bool                  `json:"EnableKibanaPrivateDomainPublic"`
	KibanaEipID                     string                `json:"KibanaEipId"`
	KibanaEip                       string                `json:"KibanaEip"`
	ExpireDate                      string                `json:"ExpireDate"`
	InstanceConfiguration           InstanceConfiguration `json:"InstanceConfiguration"`
	MetricsVersion                  string                `json:"MetricsVersion"`
	InstanceID                      string                `json:"InstanceId"`
	KibanaPrivateDomain             string                `json:"KibanaPrivateDomain"`
	KibanaPublicDomain              string                `json:"KibanaPublicDomain"`
	KibanaConfig                    KibanaConfig          `json:"KibanaConfig"`
	MaintenanceDay                  []string              `json:"MaintenanceDay"`
	MaintenanceTime                 string                `json:"MaintenanceTime"`
	Status                          string                `json:"Status"`
	TransferInfo                    TransferInfo          `json:"TransferInfo"`
	ResourceTags                    []ResourceTag         `json:"ResourceTags"`
	TotalNodes                      int64                 `json:"TotalNodes"`
	UserID                          string                `json:"UserId"`
	SubInstances                    []interface{}         `json:"SubInstances"`
	Encrypted                       bool                  `json:"Encrypted"`
	CerebroEnabled                  bool                  `json:"CerebroEnabled"`
	CerebroPrivateDomain            string                `json:"CerebroPrivateDomain"`
	CerebroPublicDomain             string                `json:"CerebroPublicDomain"`
	ESInnerEndpoint                 string                `json:"ESInnerEndpoint"`
	ESPrivateDomain                 string                `json:"ESPrivateDomain"`
	ESPrivateEndpoint               string                `json:"ESPrivateEndpoint"`
	ESPublicDomain                  string                `json:"ESPublicDomain"`
	ESPublicEndpoint                string                `json:"ESPublicEndpoint"`
	ESPrivateIPWhitelist            string                `json:"ESPrivateIpWhitelist"`
	ESPublicIPWhitelist             string                `json:"ESPublicIpWhitelist"`
	ESEipID                         string                `json:"ESEipId"`
	ESEip                           string                `json:"ESEip"`
}

type InstanceConfiguration struct {
	AdminUserName          string           `json:"AdminUserName"`
	ChargeType             string           `json:"ChargeType"`
	EnableHTTPS            bool             `json:"EnableHttps"`
	EnablePureMaster       bool             `json:"EnablePureMaster"`
	ProjectName            string           `json:"ProjectName"`
	RegionID               string           `json:"RegionId"`
	ZoneID                 string           `json:"ZoneId"`
	ZoneNumber             int64            `json:"ZoneNumber"`
	Subnet                 EsSubnet         `json:"Subnet"`
	Version                string           `json:"Version"`
	ColdNodeNumber         int64            `json:"ColdNodeNumber"`
	HotNodeNumber          int64            `json:"HotNodeNumber"`
	HotNodeResourceSpec    NodeResourceSpec `json:"HotNodeResourceSpec"`
	HotNodeStorageSpec     NodeStorageSpec  `json:"HotNodeStorageSpec"`
	CoordinatorNodeNumber  int64            `json:"CoordinatorNodeNumber"`
	InstanceName           string           `json:"InstanceName"`
	KibanaNodeNumber       int64            `json:"KibanaNodeNumber"`
	KibanaNodeResourceSpec NodeResourceSpec `json:"KibanaNodeResourceSpec"`
	MasterNodeNumber       int64            `json:"MasterNodeNumber"`
	MasterNodeResourceSpec NodeResourceSpec `json:"MasterNodeResourceSpec"`
	MasterNodeStorageSpec  NodeStorageSpec  `json:"MasterNodeStorageSpec"`
	WarmNodeNumber         int64            `json:"WarmNodeNumber"`
	Vpc                    EsVpc            `json:"VPC"`
}

type NodeResourceSpec struct {
	Name        string `json:"Name"`
	DisplayName string `json:"DisplayName"`
	Description string `json:"Description"`
	Memory      int64  `json:"Memory"`
	CPU         int64  `json:"CPU"`
}

type NodeStorageSpec struct {
	Name        string `json:"Name"`
	DisplayName string `json:"DisplayName"`
	Description string `json:"Description"`
	MaxSize     int64  `json:"MaxSize"`
	MinSize     int64  `json:"MinSize"`
	Size        int64  `json:"Size"`
	Type        string `json:"Type"`
}

type EsSubnet struct {
	SubnetID   string `json:"SubnetId"`
	SubnetName string `json:"SubnetName"`
}

type EsVpc struct {
	VpcID   string `json:"VpcId"`
	VpcName string `json:"VpcName"`
}

type KibanaConfig struct {
	CookieTTL        int64 `json:"CookieTTL"`
	SessionTTL       int64 `json:"SessionTTL"`
	SessionKeepAlive bool  `json:"SessionKeepAlive"`
	RequestTimeout   int64 `json:"RequestTimeout"`
}

type ResourceTag struct {
	Type   string            `json:"Type"`
	TagKvs map[string]string `json:"TagKvs"`
}

type TransferInfo struct {
}

type Node struct {
	InstanceID      string       `json:"InstanceId"`
	NodeName        string       `json:"NodeName"`
	NodeDisplayName string       `json:"NodeDisplayName"`
	Status          string       `json:"Status"`
	StartTime       string       `json:"StartTime"`
	RestartNumber   int64        `json:"RestartNumber"`
	IsMaster        bool         `json:"IsMaster"`
	IsHot           bool         `json:"IsHot"`
	IsCoordinator   bool         `json:"IsCoordinator"`
	IsWarm          bool         `json:"IsWarm"`
	IsCold          bool         `json:"IsCold"`
	IsKibana        bool         `json:"IsKibana"`
	HaveLog         bool         `json:"HaveLog"`
	ResourceSpec    ResourceSpec `json:"ResourceSpec"`
	StorageSpec     StorageSpec  `json:"StorageSpec"`
}

type ResourceSpec struct {
	Description string `json:"Description"`
	DisplayName string `json:"DisplayName"`
	Memory      int64  `json:"Memory"`
	CPU         int64  `json:"CPU"`
}

type StorageSpec struct {
	Description string `json:"Description"`
	DisplayName string `json:"DisplayName"`
	MinSize     int64  `json:"MinSize"`
	MaxSize     int64  `json:"MaxSize"`
	Type        string `json:"Type"`
	Size        int64  `json:"Size"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ESGraph, error) {
	original := &Es{}
	resource := &model.ESGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "es", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "es"
	resource.Name = original.Instance.InstanceConfiguration.InstanceName
	resource.OriginalLabels = lo.Flatten(lo.Map(original.Instance.ResourceTags, func(e ResourceTag, _ int) []*model.KVGraph {
		kvs := make([]*model.KVGraph, 0, len(e.TagKvs))
		for k, v := range e.TagKvs {
			kvs = append(kvs, utils.NewLabel(k, v, resource.UID))
		}
		return kvs
	}))

	resource.Status = lo.Ternary(strings.EqualFold(original.Instance.Status, "running"), "running", "stopped")
	resource.PublicEndpoint = original.Instance.ESPublicEndpoint
	resource.PrivateEndpoint = original.Instance.ESPrivateEndpoint
	resource.IPWhiteList = append(resource.IPWhiteList, lo.Map(strings.Split(original.Instance.ESPrivateIPWhitelist, ","), func(e string, _ int) string { return provider_utils.FormatCIDR(e) })...)
	resource.IPWhiteList = append(resource.IPWhiteList, lo.Map(strings.Split(original.Instance.ESPublicIPWhitelist, ","), func(e string, _ int) string { return provider_utils.FormatCIDR(e) })...)
	resource.EngineVersion = original.Instance.InstanceConfiguration.Version
	resource.PublicAllowed = original.Instance.EnableESPublicNetwork

	resource.KibanaIPWhiteList = append(resource.KibanaIPWhiteList, lo.Map(strings.Split(original.Instance.KibanaPrivateIPWhitelist, ","), func(e string, _ int) string { return provider_utils.FormatCIDR(e) })...)
	resource.KibanaIPWhiteList = append(resource.KibanaIPWhiteList, lo.Map(strings.Split(original.Instance.KibanaPublicIPWhitelist, ","), func(e string, _ int) string { return provider_utils.FormatCIDR(e) })...)
	resource.KibanaProtocol = lo.Ternary(original.Instance.InstanceConfiguration.EnableHTTPS, "https", "http")
	resource.KibanaPublicAllowed = original.Instance.EnableKibanaPublicNetwork
	if resource.KibanaPublicAllowed {
		resource.KibanaPublicEndpoint = original.Instance.KibanaPublicDomain
	} else {
		resource.KibanaPrivateEndpoint = original.Instance.KibanaPrivateDomain
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.InstanceConfiguration.Vpc.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.InstanceConfiguration.Subnet.SubnetID),
			TargetUID: resource.UID,
		},
	})
	resource.Node = lo.Map(original.Nodes, func(node Node, _ int) *model.ESNodeGraph {
		return &model.ESNodeGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "es-node", node.InstanceID),
				TargetUID: resource.UID,
			},
			IP:    node.InstanceID,
			Class: lo.Ternary(node.IsKibana, "kibana", "es"),
		}
	})

	return resource, nil
}
