package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type Vpc struct {
	Vpc     VpcClass `json:"vpc"`
}

type VpcClass struct {
	AccountID           string        `json:"AccountId"`
	Associate<PERSON><PERSON>       interface{}   `json:"AssociateCens"`
	CIDRBlock           string        `json:"CidrBlock"`
	CreationTime        string        `json:"CreationTime"`
	Description         string        `json:"Description"`
	DNSServers          interface{}   `json:"DnsServers"`
	IsDefault           bool          `json:"IsDefault"`
	NatGatewayIDS       []string      `json:"NatGatewayIds"`
	NetworkACLNum       string        `json:"NetworkAclNum"`
	ProjectName         string        `json:"ProjectName"`
	RouteTableIDS       []string      `json:"RouteTableIds"`
	SecondaryCIDRBlocks []string      `json:"SecondaryCidrBlocks"`
	SecurityGroupIDS    []string      `json:"SecurityGroupIds"`
	Status              string        `json:"Status"`
	SubnetIDS           []string      `json:"SubnetIds"`
	Tags                []Tags `json:"Tags"`
	UpdateTime          string        `json:"UpdateTime"`
	UserCIDRBlocks      interface{}   `json:"UserCidrBlocks"`
	VpcID               string        `json:"VpcId"`
	VpcName             string        `json:"VpcName"`
}


func parseVPC(raw map[string]any) model.Asset {
	original := &Vpc{}
	meta := &model.ObjectMeta{}
	vpc := &model.VPC{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Vpc.VpcID
	meta.OriginalLabels = utils.Map(original.Vpc.Tags, func(e Tags) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "vpc", original.Vpc.VpcID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "vpc"
	meta.Name = original.Vpc.VpcName
	meta.Description = original.Vpc.Description

	vpc.CIDR = original.Vpc.CIDRBlock
	vpc.IsDefault = original.Vpc.IsDefault
	vpc.SecondaryCIDRs = original.Vpc.SecondaryCIDRBlocks
	vpc.SubnetIDList = utils.Map(original.Vpc.SubnetIDS, func(e string) string { return utils.GenerateUID(meta.Provider, "subnet", e) })

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, vpc)
	return asset
}
