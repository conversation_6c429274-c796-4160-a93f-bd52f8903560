package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

type SG struct {
	Permissions []Permission `json:"permissions"`
	Sg          SgClass      `json:"sg"`
}

type Permission struct {
	CIDRIP          string      `json:"CidrIp"`
	CreationTime    string      `json:"CreationTime"`
	Description     string      `json:"Description"`
	Direction       string      `json:"Direction"`
	Policy          string      `json:"Policy"`
	PortEnd         int         `json:"PortEnd"`
	PortStart       int         `json:"PortStart"`
	PrefixListCidrs interface{} `json:"PrefixListCidrs"`
	PrefixListID    string      `json:"PrefixListId"`
	Priority        int         `json:"Priority"`
	Protocol        string      `json:"Protocol"`
	SourceGroupID   string      `json:"SourceGroupId"`
	UpdateTime      string      `json:"UpdateTime"`
}

type SgClass struct {
	CreationTime      string `json:"CreationTime"`
	Description       string `json:"Description"`
	ProjectName       string `json:"ProjectName"`
	SecurityGroupID   string `json:"SecurityGroupId"`
	SecurityGroupName string `json:"SecurityGroupName"`
	ServiceManaged    bool   `json:"ServiceManaged"`
	Status            string `json:"Status"`
	Tags              []Tag  `json:"Tags"`
	Type              string `json:"Type"`
	VpcID             string `json:"VpcId"`
}

func parseSG(raw map[string]any) model.Asset {
	original := &SG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Sg.SecurityGroupID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Sg.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.Sg.SecurityGroupID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.Sg.SecurityGroupName
	meta.Description = original.Sg.Description

	sg.IsDefault = strings.EqualFold(original.Sg.SecurityGroupName, "default")
	sg.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Sg.VpcID)
	sg.Rules = utils.Map(original.Permissions, func(perm Permission) model.SGRule {
		rule := model.SGRule{}
		rule.Description = perm.Description
		rule.Protocol = perm.Protocol
		rule.Policy = perm.Policy
		rule.Priority = perm.Priority
		rule.Direction = perm.Direction
		rule.PeerSGID = utils.UnwrapOr(perm.SourceGroupID, perm.SourceGroupID == "", utils.GenerateUID(meta.Provider, "security-group", perm.SourceGroupID))
		rule.PeerCIDR = perm.CIDRIP
		rule.PortStart = utils.UnwrapOr(perm.PortStart, perm.PortStart >= 0, 0)
		rule.PortEnd = utils.UnwrapOr(perm.PortEnd, perm.PortEnd >= 0, 65535)

		return rule
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}
