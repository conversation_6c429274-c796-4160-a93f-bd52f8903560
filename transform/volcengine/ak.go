package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type AK struct {
	Ak       AkClass  `json:"ak"`
	LastUsed LastUsed `json:"last_used"`
}

type AkClass struct {
	AccessKeyID string `json:"AccessKeyId"`
	CreateDate  string `json:"CreateDate"`
	Status      string `json:"Status"`
	UpdateDate  string `json:"UpdateDate"`
	UserName    string `json:"UserName"`
}

type LastUsed struct {
	Region      string `json:"Region"`
	RequestTime string `json:"RequestTime"`
	Service     string `json:"Service"`
}

func parseAk(raw map[string]any) model.Asset {
	original := &AK{}
	meta := &model.ObjectMeta{}
	ak := &model.AK{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Ak.AccessKeyID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "ak", original.Ak.AccessKeyID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ak"
	meta.Name = original.Ak.AccessKeyID

	ak.Enabled = utils.DataPointer(original.Ak.Status == "active")
	ak.UserID = utils.GenerateUID(meta.Provider, "user", original.Ak.UserName)
	ak.UserName = original.Ak.UserName
	t, _ := time.Parse("20060102T150405Z0700", original.Ak.CreateDate)
	ak.CreatedAt = utils.DataPointer(t.UnixMilli())
	t, _ = time.Parse("20060102T150405Z0700", original.LastUsed.RequestTime)
	ak.LastUsedAt = utils.DataPointer(t.UnixMilli())

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ak)
	return asset
}
