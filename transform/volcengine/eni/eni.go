package eni

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eni"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_eni_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eni_list"] = append(resourceData["eni_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

	}

	return resourceData, nil
}

type Eni struct {
	AccountID            string              `json:"AccountId"`
	AssociatedElasticIP  AssociatedElasticIP `json:"AssociatedElasticIp"`
	CreatedAt            string              `json:"CreatedAt"`
	Description          string              `json:"Description"`
	DeviceID             string              `json:"DeviceId"`
	IPv6Sets             []string            `json:"IPv6Sets"`
	MACAddress           string              `json:"MacAddress"`
	NetworkInterfaceID   string              `json:"NetworkInterfaceId"`
	NetworkInterfaceName string              `json:"NetworkInterfaceName"`
	PortSecurityEnabled  bool                `json:"PortSecurityEnabled"`
	PrimaryIPAddress     string              `json:"PrimaryIpAddress"`
	PrivateIPSets        PrivateIPSets       `json:"PrivateIpSets"`
	ProjectName          string              `json:"ProjectName"`
	SecurityGroupIDS     []string            `json:"SecurityGroupIds"`
	ServiceManaged       bool                `json:"ServiceManaged"`
	Status               string              `json:"Status"`
	SubnetID             string              `json:"SubnetId"`
	Tags                 []Tag               `json:"Tags"`
	Type                 string              `json:"Type"`
	UpdatedAt            string              `json:"UpdatedAt"`
	VpcID                string              `json:"VpcId"`
	VpcName              string              `json:"VpcName"`
	ZoneID               string              `json:"ZoneId"`
}

type AssociatedElasticIP struct {
	AllocationID        string `json:"AllocationId"`
	EipAddress          string `json:"EipAddress"`
	ReleaseWithInstance bool   `json:"ReleaseWithInstance"`
}

type PrivateIPSets struct {
	PrivateIPSet []PrivateIPSet `json:"PrivateIpSet"`
}

type PrivateIPSet struct {
	AssociatedElasticIP AssociatedElasticIP `json:"AssociatedElasticIp"`
	Primary             bool                `json:"Primary"`
	PrivateIPAddress    string              `json:"PrivateIpAddress"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcEniSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetEniSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgEniSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEniSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipEniSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})

}

func parseOne(assetMsg *model.AssetMessage) (*model.ENIGraph, error) {
	original := &Eni{}
	resource := &model.ENIGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NetworkInterfaceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eni", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Description
	resource.Kind = "eni"
	resource.Name = original.NetworkInterfaceName
	resource.OriginalLabels = lo.Map(original.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.PrimaryIP = original.PrimaryIPAddress
	resource.MacAddress = original.MACAddress
	resource.Status = lo.Ternary(strings.EqualFold(original.Status, "InUse"), "inuse", "available")
	resource.Class = lo.Ternary(strings.EqualFold(original.Type, "primary"), "primary", "secondary")
	resource.PrivateIPList = lo.FilterMap(original.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet, _ int) (string, bool) {
		return e.PrivateIPAddress, e.PrivateIPAddress != ""
	})
	resource.PublicIPList = lo.FilterMap(original.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet, _ int) (string, bool) {
		return e.AssociatedElasticIP.EipAddress, e.AssociatedElasticIP.EipAddress != ""
	})

	if original.DeviceID != "" {
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.DeviceID),
				TargetUID: resource.UID,
			},
		})
	}
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.SubnetID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG, lo.Map(original.SecurityGroupIDS, func(e string, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e),
				TargetUID: resource.UID,
			},
		}
	})...)
	return resource, nil
}
