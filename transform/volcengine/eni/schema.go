package eni

import graph "AssetStandardizer/graph"

var (
	eniSchema = graph.NodeSchema{
		Label: "ENI",
		Properties: graph.NodeProperties{
			"uid":                {Name: "uid"},
			"provider":           {Name: "provider"},
			"original_id":        {Name: "original_id"},
			"transformed_object": {Name: "transformed_object"},
			"region":             {Name: "region"},
			"last_seen":          {Name: "last_seen"},
			"description":        {Name: "description"},
			"kind":               {Name: "kind"},
			"name":               {Name: "name"},
			"mac_address":        {Name: "mac_address"},
			"primary_ip":         {Name: "primary_ip"},
			"status":             {Name: "status"},
			"class":              {Name: "class"},
			"private_ip_list":    {Name: "private_ip_list"},
			"public_ip_list":     {Name: "public_ip_list"},
		},
	}

	vpcEniSchema = graph.NodeSchema{
		Label: "VPC",
		Properties: graph.NodeProperties{
			"uid": {Name: "uid"},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "ENI",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "eni_id"},
				},
				RelLabel:  "MEMBER_OF",
				Direction: graph.OUTWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}

	subnetEniSchema = graph.NodeSchema{
		Label: "Subnet",
		Properties: graph.NodeProperties{
			"uid": {Name: "uid"},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "ENI",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "eni_id"},
				},
				RelLabel:  "MEMBER_OF",
				Direction: graph.OUTWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}

	sgEniSchema = graph.NodeSchema{
		Label: "SecurityGroup",
		Properties: graph.NodeProperties{
			"uid": {Name: "uid"},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "ENI",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "eni_id"},
				},
				RelLabel:  "ATTACHED_TO",
				Direction: graph.OUTWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}

	ecsEniSchema = graph.NodeSchema{
		Label: "ECS",
		Properties: graph.NodeProperties{
			"uid": {Name: "uid"},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "ENI",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "eni_id"},
				},
				RelLabel:  "ATTACHED_TO",
				Direction: graph.INWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}

	eipEniSchema = graph.NodeSchema{
		Label: "EIP",
		Properties: graph.NodeProperties{
			"uid": {Name: "uid"},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "ENI",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "eni_id"},
				},
				RelLabel:  "ATTACHED_TO",
				Direction: graph.OUTWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}
)
