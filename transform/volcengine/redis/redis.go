package redis

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "redis"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "redis_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["redis_list"] = append(resourceData["redis_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, redisSchema, resourceData["redis_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, redisAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Redis struct {
	Accounts       []RedisAccount       `json:"accounts"`
	Instance       RedisInstance        `json:"instance"`
	IPWhiteList    []RedisIPWhiteList   `json:"ipWhiteList"`
	SecurityGroups []RedisSecurityGroup `json:"securityGroups"`
	Tde            RedisTde             `json:"tde"`
}

type RedisAccount struct {
	AccountDescription string                  `json:"AccountDescription"`
	AccountName        string                  `json:"AccountName"`
	AccountStatus      string                  `json:"AccountStatus"`
	AccountType        string                  `json:"AccountType"`
	DatabasePrivileges RedisDatabasePrivileges `json:"DatabasePrivileges"`
	InstanceID         string                  `json:"InstanceId"`
}

type RedisDatabasePrivileges struct {
	DatabasePrivilege []RedisDatabasePrivilege `json:"DatabasePrivilege"`
}

type RedisDatabasePrivilege struct {
	AccountPrivilege string `json:"AccountPrivilege"`
}

type RedisIPWhiteList struct {
	SecurityIPGroupAttribute string `json:"SecurityIpGroupAttribute"`
	SecurityIPGroupName      string `json:"SecurityIpGroupName"`
	SecurityIPList           string `json:"SecurityIpList"`
}

type RedisInstance struct {
	ArchitectureType    string `json:"ArchitectureType"`
	Bandwidth           int64  `json:"Bandwidth"`
	Capacity            int64  `json:"Capacity"`
	ChargeType          string `json:"ChargeType"`
	ComputingType       string `json:"ComputingType"`
	Config              string `json:"Config"`
	ConnectionDomain    string `json:"ConnectionDomain"`
	Connections         int64  `json:"Connections"`
	CreateTime          string `json:"CreateTime"`
	DestroyTime         string `json:"DestroyTime"`
	EditionType         string `json:"EditionType"`
	EndTime             string `json:"EndTime"`
	EngineVersion       string `json:"EngineVersion"`
	GlobalInstanceID    string `json:"GlobalInstanceId"`
	HasRenewChangeOrder bool   `json:"HasRenewChangeOrder"`
	InstanceClass       string `json:"InstanceClass"`
	InstanceID          string `json:"InstanceId"`
	InstanceName        string `json:"InstanceName"`
	InstanceStatus      string `json:"InstanceStatus"`
	InstanceType        string `json:"InstanceType"`
	IsRDS               bool   `json:"IsRds"`
	NetworkType         string `json:"NetworkType"`
	NodeType            string `json:"NodeType"`
	PackageType         string `json:"PackageType"`
	Port                int64  `json:"Port"`
	Qps                 int64  `json:"QPS"`
	RegionID            string `json:"RegionId"`
	ResourceGroupID     string `json:"ResourceGroupId"`
	ShardClass          string `json:"ShardClass"`
	ShardCount          int64  `json:"ShardCount"`
	Tags                Tags   `json:"Tags"`
	UserName            string `json:"UserName"`
	VSwitchID           string `json:"VSwitchId"`
	VpcID               string `json:"VpcId"`
	ZoneID              string `json:"ZoneId"`
}

type RedisSecurityGroup struct {
	NetType         string `json:"NetType"`
	SecurityGroupID string `json:"SecurityGroupId"`
}

type RedisTde struct {
	TDEStatus string `json:"TDEStatus"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.RedisGraph, error) {
	original := &Redis{}
	resource := &model.RedisGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "redis", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "redis"
	resource.Name = original.Instance.InstanceName
	resource.OriginalLabels = lo.Map(original.Instance.Tags.Tag,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.ConnectionAddress = original.Instance.ConnectionDomain
	resource.Engine = "Redis"
	resource.EngineVersion = original.Instance.EngineVersion
	resource.PublicAllowed = !strings.EqualFold(original.Instance.NetworkType, "VPC") && !strings.EqualFold(original.Instance.NetworkType, "CLASSIC")
	resource.TDEEnabled = lo.ToPtr(strings.EqualFold(original.Tde.TDEStatus, "Enabled"))
	resource.IpWhiteList = lo.Flatten(
		lo.Map(original.IPWhiteList, func(ipList RedisIPWhiteList, _ int) []string {
			return lo.Map(strings.Split(ipList.SecurityIPList, ","), func(ip string, _ int) string {
				return provider_utils.FormatCIDR(ip)
			})
		}),
	)

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.VSwitchID),
			TargetUID: resource.UID,
		},
	})

	resource.Accounts = lo.Map(original.Accounts, func(account RedisAccount, _ int) *model.RedisAccountGraph {
		return &model.RedisAccountGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "redis-account", account.AccountName),
				TargetUID: resource.UID,
			},
			Name:        account.AccountName,
			Enabled:     strings.EqualFold(account.AccountStatus, "Available"),
			Class:       lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
			Description: account.AccountDescription,
		}
	})

	resource.SG = append(resource.SG, lo.Map(original.SecurityGroups, func(securityGroup RedisSecurityGroup, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", securityGroup.SecurityGroupID),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
