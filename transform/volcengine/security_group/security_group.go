package security_group

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "security_group"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_sg_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["sg_list"] = append(resourceData["sg_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_rule_list"] = append(resourceData["sg_rule_list"],
			utils.GenParamsFromStructSlice(resource.Rules)...,
		)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstPortRange)
			})...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgRuleSchema, resourceData["sg_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIPRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIPRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type SG struct {
	Permissions   []Permission  `json:"permissions"`
	SecurityGroup SecurityGroup `json:"security_group"`
}

type Permission struct {
	CreateTime              string `json:"CreateTime"`
	Description             string `json:"Description"`
	DestCIDRIP              string `json:"DestCidrIp"`
	DestGroupID             string `json:"DestGroupId"`
	DestGroupName           string `json:"DestGroupName"`
	DestGroupOwnerAccount   string `json:"DestGroupOwnerAccount"`
	DestPrefixListID        string `json:"DestPrefixListId"`
	DestPrefixListName      string `json:"DestPrefixListName"`
	Direction               string `json:"Direction"`
	IPProtocol              string `json:"IpProtocol"`
	Ipv6DestCIDRIP          string `json:"Ipv6DestCidrIp"`
	Ipv6SourceCIDRIP        string `json:"Ipv6SourceCidrIp"`
	NICType                 string `json:"NicType"`
	Policy                  string `json:"Policy"`
	PortRange               string `json:"PortRange"`
	Priority                string `json:"Priority"`
	SecurityGroupRuleID     string `json:"SecurityGroupRuleId"`
	SourceCIDRIP            string `json:"SourceCidrIp"`
	SourceGroupID           string `json:"SourceGroupId"`
	SourceGroupName         string `json:"SourceGroupName"`
	SourceGroupOwnerAccount string `json:"SourceGroupOwnerAccount"`
	SourcePortRange         string `json:"SourcePortRange"`
	SourcePrefixListID      string `json:"SourcePrefixListId"`
	SourcePrefixListName    string `json:"SourcePrefixListName"`
}

type SecurityGroup struct {
	CreationTime      string `json:"CreationTime"`
	Description       string `json:"Description"`
	ResourceGroupID   string `json:"ResourceGroupId"`
	RuleCount         int64  `json:"RuleCount"`
	SecurityGroupID   string `json:"SecurityGroupId"`
	SecurityGroupName string `json:"SecurityGroupName"`
	SecurityGroupType string `json:"SecurityGroupType"`
	ServiceID         int64  `json:"ServiceID"`
	ServiceManaged    bool   `json:"ServiceManaged"`
	Tags              Tags   `json:"Tags"`
	VpcID             string `json:"VpcId"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.SGGraph, error) {
	original := &SG{}
	resource := &model.SGGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.SecurityGroup.SecurityGroupID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "security-group", original.SecurityGroup.SecurityGroupID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "security-group"
	resource.Name = original.SecurityGroup.SecurityGroupName
	resource.Description = original.SecurityGroup.Description
	resource.OriginalLabels = lo.Map(original.SecurityGroup.Tags.Tag, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.IsDefault = strings.EqualFold(original.SecurityGroup.SecurityGroupName, "default")
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.SecurityGroup.VpcID),
			TargetUID: resource.UID,
		},
	})

	for _, perm := range original.Permissions {
		rule, err := parseSGRule(perm, resource.UID)
		if err != nil {
			return nil, fmt.Errorf("parseSGRule failed, perm: %v, err: %v", perm, err)
		}
		resource.Rules = append(resource.Rules, rule)
	}
	return resource, nil
}

func parseSGRule(perm Permission, targetUID string) (*model.SGRuleGraph, error) {
	rule := &model.SGRuleGraph{}
	rule.UID = utils.GenerateUID(provider_utils.ProviderID, "sg_rule", perm.SecurityGroupRuleID)
	rule.TargetUID = targetUID
	rule.RuleID = perm.SecurityGroupRuleID
	rule.Description = perm.Description
	rule.Protocol = strings.ToLower(perm.IPProtocol)
	rule.Policy = strings.ToLower(perm.Policy)
	rule.Priority, _ = strconv.Atoi(perm.Priority)
	rule.Direction = perm.Direction

	defaultCIDR := "0.0.0.0/0"
	if perm.Ipv6SourceCIDRIP != "" || perm.Ipv6DestCIDRIP != "" {
		defaultCIDR = "::/0"
	}

	if ipRange, err := provider_utils.ParseIPRange(lo.CoalesceOrEmpty(perm.SourceCIDRIP, perm.Ipv6SourceCIDRIP, defaultCIDR), rule.UID); err != nil {
		return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, lo.CoalesceOrEmpty(perm.SourceCIDRIP, perm.Ipv6SourceCIDRIP, defaultCIDR))
	} else {
		rule.SrcIPRange = []*model.IPRangeGraph{ipRange}
	}
	if portRange, err := provider_utils.ParsePortRange(perm.SourcePortRange, rule.UID); err != nil {
		return nil, fmt.Errorf("parse port range error: %v, port: %s", err, perm.SourcePortRange)
	} else {
		rule.SrcPortRange = []*model.PortRangeGraph{portRange}
	}

	if ipRange, err := provider_utils.ParseIPRange(lo.CoalesceOrEmpty(perm.DestCIDRIP, perm.Ipv6DestCIDRIP, defaultCIDR), rule.UID); err != nil {
		return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, lo.CoalesceOrEmpty(perm.DestCIDRIP, perm.Ipv6DestCIDRIP, defaultCIDR))
	} else {
		rule.DstIPRange = []*model.IPRangeGraph{ipRange}
	}
	if portRange, err := provider_utils.ParsePortRange(perm.PortRange, rule.UID); err != nil {
		return nil, fmt.Errorf("parse port range error: %v, port: %s", err, perm.PortRange)
	} else {
		rule.DstPortRange = []*model.PortRangeGraph{portRange}
	}

	return rule, nil
}
