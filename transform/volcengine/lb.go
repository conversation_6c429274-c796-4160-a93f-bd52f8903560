package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type Alb struct {
	Instance  AlbInstance   `json:"lb"`
	Listeners []AlbListener `json:"listeners"`
	Servers   []AlbServer   `json:"servers"`
}

type AlbInstance struct {
	AddressIPVersion        string        `json:"AddressIpVersion"`
	BusinessStatus          string        `json:"BusinessStatus"`
	CreateTime              string        `json:"CreateTime"`
	DNSName                 string        `json:"DNSName"`
	DeleteProtection        string        `json:"DeleteProtection"`
	DeletedTime             string        `json:"DeletedTime"`
	Description             string        `json:"Description"`
	EipAddress              string        `json:"EipAddress"`
	EipID                   string        `json:"EipId"`
	EniAddress              string        `json:"EniAddress"`
	EniID                   string        `json:"EniId"`
	LoadBalancerBillingType int64         `json:"LoadBalancerBillingType"`
	LoadBalancerID          string        `json:"LoadBalancerId"`
	LoadBalancerName        string        `json:"LoadBalancerName"`
	LocalAddresses          []interface{} `json:"LocalAddresses"`
	LockReason              string        `json:"LockReason"`
	OverdueTime             string        `json:"OverdueTime"`
	ProjectName             string        `json:"ProjectName"`
	Status                  string        `json:"Status"`
	SubnetID                string        `json:"SubnetId"`
	Tags                    []Tags        `json:"Tags"`
	Type                    string        `json:"Type"`
	UpdateTime              string        `json:"UpdateTime"`
	VpcID                   string        `json:"VpcId"`
	ZoneMappings            []ZoneMapping `json:"ZoneMappings"`
}

type ZoneMapping struct {
	LoadBalancerAddresses []LoadBalancerAddress `json:"LoadBalancerAddresses"`
	SubnetID              string                `json:"SubnetId"`
	ZoneID                string                `json:"ZoneId"`
}

type LoadBalancerAddress struct {
	Eip            interface{} `json:"Eip"`
	EipAddress     string      `json:"EipAddress"`
	EipID          string      `json:"EipId"`
	EniAddress     string      `json:"EniAddress"`
	EniID          string      `json:"EniId"`
	EniIpv6Address string      `json:"EniIpv6Address"`
	Ipv6Eip        interface{} `json:"Ipv6Eip"`
	Ipv6EipID      string      `json:"Ipv6EipId"`
}

type AlbListener struct {
	ACLIDS                  []interface{}     `json:"AclIds"`
	ACLStatus               string            `json:"AclStatus"`
	ACLType                 string            `json:"AclType"`
	CACertificateID         string            `json:"CACertificateId"`
	CERTCenterCertificateID string            `json:"CertCenterCertificateId"`
	CertificateID           string            `json:"CertificateId"`
	CertificateSource       string            `json:"CertificateSource"`
	CreateTime              string            `json:"CreateTime"`
	CustomizedCFGID         string            `json:"CustomizedCfgId"`
	Description             string            `json:"Description"`
	DomainExtensions        []DomainExtension `json:"DomainExtensions"`
	EnableHttp2             string            `json:"EnableHttp2"`
	EnableQuic              string            `json:"EnableQuic"`
	Enabled                 string            `json:"Enabled"`
	ListenerID              string            `json:"ListenerId"`
	ListenerName            string            `json:"ListenerName"`
	LoadBalancerID          string            `json:"LoadBalancerId"`
	Port                    int64             `json:"Port"`
	ProjectName             string            `json:"ProjectName"`
	Protocol                string            `json:"Protocol"`
	ProxyProtocolDisabled   interface{}       `json:"ProxyProtocolDisabled"`
	ServerGroupID           string            `json:"ServerGroupId"`
	ServerGroups            []ServerGroup     `json:"ServerGroups"`
	Status                  string            `json:"Status"`
	UpdateTime              string            `json:"UpdateTime"`
}

type DomainExtension struct {
	CERTCenterCertificateID string `json:"CertCenterCertificateId"`
	CertificateID           string `json:"CertificateId"`
	CertificateSource       string `json:"CertificateSource"`
	Domain                  string `json:"Domain"`
	DomainExtensionID       string `json:"DomainExtensionId"`
	ListenerID              string `json:"ListenerId"`
}

type ServerGroup struct {
	ServerGroupID   string `json:"ServerGroupId"`
	ServerGroupName string `json:"ServerGroupName"`
}

type AlbServer struct {
	Description   string `json:"Description"`
	InstanceID    string `json:"InstanceId"`
	IP            string `json:"Ip"`
	Port          int    `json:"Port"`
	RemoteEnabled string `json:"RemoteEnabled"`
	ServerID      string `json:"ServerId"`
	Type          string `json:"Type"`
	Weight        int    `json:"Weight"`
}

func parseAlb(raw map[string]any) model.Asset {
	original := &Alb{}
	meta := &model.ObjectMeta{}
	lb := &model.LB{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.Instance.Tags, func(e Tags) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalID = original.Instance.LoadBalancerID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "lb", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Instance.Description
	meta.Kind = "lb"
	meta.Name = original.Instance.LoadBalancerName

	lb.Class = "application"
	lb.DeleteProtection = lo.ToPtr(strings.EqualFold(original.Instance.DeleteProtection, "on"))
	lb.Status = utils.UnwrapOr("active", original.Instance.Status == "Active", "inactive")
	lb.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Instance.VpcID)
	for _, zone := range original.Instance.ZoneMappings {
		for _, address := range zone.LoadBalancerAddresses {
			if len(address.EipAddress) > 0 {
				lb.PublicIPList = append(lb.PublicIPList, address.EipAddress)
			}
			if len(address.EniAddress) > 0 {
				lb.PrivateIPList = append(lb.PrivateIPList, address.EniAddress)
			}
			if len(address.EniIpv6Address) > 0 {
				lb.PublicIPList = append(lb.PublicIPList, address.EipAddress)
			}
		}
	}
	for _, listener := range original.Listeners {
		if len(listener.ListenerID) > 0 {
			id := utils.GenerateUID(ProviderID, "lb-listener", listener.ListenerID)
			lb.ListenerIDList = append(lb.ListenerIDList, id)
		}
	}
	lb.Servers = append(lb.Servers, utils.Map(original.Servers, func(e AlbServer) model.LbServer {
		var class string
		switch e.Type {
		case "instance":
			class = "ecs"
		case "ip":
			class = "ip"
		}
		return model.LbServer{
			OriginalID: e.InstanceID,
			Uid:        lo.Ternary(class != "ip", utils.GenerateUID(meta.Provider, class, e.InstanceID), ""),
			Class:      class,
			IP:         e.IP,
			Port:       e.Port,
			Weight:     e.Weight,
		}
	})...)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}

type Clb struct {
	Instance  ClbInstance   `json:"lb"`
	Listeners []ClbListener `json:"listeners"`
	Servers   []ClbServer   `json:"servers"`
}

type ClbInstance struct {
	AccountID                    string      `json:"AccountId"`
	AddressIPVersion             string      `json:"AddressIpVersion"`
	BusinessStatus               string      `json:"BusinessStatus"`
	CreateTime                   string      `json:"CreateTime"`
	DeletedTime                  string      `json:"DeletedTime"`
	Description                  string      `json:"Description"`
	EipAddress                   string      `json:"EipAddress"`
	EipID                        string      `json:"EipID"`
	EniAddress                   string      `json:"EniAddress"`
	EniAddressNum                interface{} `json:"EniAddressNum"`
	EniAddresses                 interface{} `json:"EniAddresses"`
	EniID                        string      `json:"EniID"`
	EniIpv6Address               string      `json:"EniIpv6Address"`
	ExclusiveClusterID           string      `json:"ExclusiveClusterId"`
	ExpiredTime                  string      `json:"ExpiredTime"`
	Ipv6EipID                    string      `json:"Ipv6EipId"`
	LoadBalancerBillingType      int64       `json:"LoadBalancerBillingType"`
	LoadBalancerID               string      `json:"LoadBalancerId"`
	LoadBalancerName             string      `json:"LoadBalancerName"`
	LoadBalancerSpec             string      `json:"LoadBalancerSpec"`
	LockReason                   string      `json:"LockReason"`
	MasterZoneID                 string      `json:"MasterZoneId"`
	ModificationProtectionReason string      `json:"ModificationProtectionReason"`
	ModificationProtectionStatus string      `json:"ModificationProtectionStatus"`
	NewArch                      interface{} `json:"NewArch"`
	OverdueTime                  string      `json:"OverdueTime"`
	ProjectName                  string      `json:"ProjectName"`
	ServiceManaged               bool        `json:"ServiceManaged"`
	SlaveZoneID                  string      `json:"SlaveZoneId"`
	Status                       string      `json:"Status"`
	SubnetID                     string      `json:"SubnetId"`
	Tags                         []Tags      `json:"Tags"`
	Type                         string      `json:"Type"`
	UpdateTime                   string      `json:"UpdateTime"`
	VpcID                        string      `json:"VpcId"`
}

type ClbListener struct {
	ACLIDS                 []interface{} `json:"AclIds"`
	ACLStatus              string        `json:"AclStatus"`
	ACLType                string        `json:"AclType"`
	Bandwidth              int64         `json:"Bandwidth"`
	CertificateID          string        `json:"CertificateId"`
	ClientBodyTimeout      interface{}   `json:"ClientBodyTimeout"`
	ClientHeaderTimeout    interface{}   `json:"ClientHeaderTimeout"`
	ConnectionDrainEnabled string        `json:"ConnectionDrainEnabled"`
	ConnectionDrainTimeout int64         `json:"ConnectionDrainTimeout"`
	Cookie                 string        `json:"Cookie"`
	CreateTime             string        `json:"CreateTime"`
	Description            string        `json:"Description"`
	Enabled                string        `json:"Enabled"`
	EndPort                interface{}   `json:"EndPort"`
	HealthCheck            HealthCheck   `json:"HealthCheck"`
	Http2Enabled           interface{}   `json:"Http2Enabled"`
	KeepaliveTimeout       interface{}   `json:"KeepaliveTimeout"`
	ListenerID             string        `json:"ListenerId"`
	ListenerName           string        `json:"ListenerName"`
	PersistenceTimeout     int64         `json:"PersistenceTimeout"`
	PersistenceType        string        `json:"PersistenceType"`
	Port                   int64         `json:"Port"`
	Protocol               string        `json:"Protocol"`
	ProxyConnectTimeout    interface{}   `json:"ProxyConnectTimeout"`
	ProxyProtocolType      string        `json:"ProxyProtocolType"`
	ProxyReadTimeout       interface{}   `json:"ProxyReadTimeout"`
	ProxySendTimeout       interface{}   `json:"ProxySendTimeout"`
	Scheduler              string        `json:"Scheduler"`
	SecurityPolicyID       interface{}   `json:"SecurityPolicyId"`
	SendTimeout            interface{}   `json:"SendTimeout"`
	ServerGroupID          string        `json:"ServerGroupId"`
	StartPort              interface{}   `json:"StartPort"`
	Status                 string        `json:"Status"`
	Tags                   []interface{} `json:"Tags"`
	UpdateTime             string        `json:"UpdateTime"`
}

type ClbServer struct {
	Description string `json:"Description"`
	InstanceID  string `json:"InstanceId"`
	IP          string `json:"Ip"`
	Port        int    `json:"Port"`
	ServerID    string `json:"ServerId"`
	Type        string `json:"Type"`
	Weight      int    `json:"Weight"`
}

func parseClb(raw map[string]any) model.Asset {
	original := &Clb{}
	meta := &model.ObjectMeta{}
	lb := &model.LB{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.Instance.Tags, func(e Tags) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalID = original.Instance.LoadBalancerID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "lb", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "lb"
	meta.Description = original.Instance.Description
	meta.Name = original.Instance.LoadBalancerName

	lb.Class = "mixed"
	lb.DeleteProtection = lo.ToPtr(strings.EqualFold(original.Instance.ModificationProtectionStatus, "ConsoleProtection"))
	lb.Status = utils.UnwrapOr("active", original.Instance.Status == "Active", "inactive")
	lb.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Instance.VpcID)
	if len(original.Instance.EipAddress) > 0 {
		lb.PublicIPList = append(lb.PublicIPList, original.Instance.EipAddress)
	}
	if len(original.Instance.EniAddress) > 0 {
		lb.PrivateIPList = append(lb.PrivateIPList, original.Instance.EniAddress)
	}
	for _, listener := range original.Listeners {
		if len(listener.ListenerID) > 0 {
			id := utils.GenerateUID(ProviderID, "lb-listener", listener.ListenerID)
			lb.ListenerIDList = append(lb.ListenerIDList, id)
		}
	}
	lb.Servers = append(lb.Servers, utils.Map(original.Servers, func(e ClbServer) model.LbServer {
		class := strings.ToLower(e.Type)
		return model.LbServer{
			OriginalID: e.InstanceID,
			Uid:        lo.Ternary(class != "ip", utils.GenerateUID(meta.Provider, class, e.InstanceID), ""),
			Class:      class,
			IP:         e.IP,
			Port:       e.Port,
			Weight:     e.Weight,
		}
	})...)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}
