package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
)

const ProviderID = "volcengine"

func Parse(raw map[string]any) (model.Asset, error) {
	switch utils.ReadFieldString(raw, "type") {
	case "vpc_eip_aggregated":
		return parseEip(raw), nil
	case "vpc_sg_aggregated":
		return parseSG(raw), nil
	case "ecs_instance_aggregated":
		return parseEcs(raw), nil
	case "lb_alb_aggregated":
		return parseAlb(raw), nil
	case "lb_clb_aggregated":
		return parseClb(raw), nil
	case "lb_albListener_aggregated":
		return parseAlbListener(raw), nil
	case "lb_clbListener_aggregated":
		return parseClbListener(raw), nil
	case "iam_user_aggregated":
		return parseUser(raw), nil
	case "iam_ak_aggregated":
		return parseAk(raw), nil
	case "iam_role_aggregated":
		return parseRole(raw), nil
	case "iam_policy_data":
		return parsePolicyDetail(raw), nil
	case "rds_mysql_aggregated":
		return parseRdsMysql(raw), nil
	case "rds_postgresql_aggregated":
		return parseRdsPostgresql(raw), nil
	case "redis_instance_aggregated":
		return parseRedis(raw), nil
	case "nat_gateway_aggregated":
		return parseNat(raw), nil
	case "tos_bucket_aggregated":
		return parseBucket(raw), nil
	case "vpc_vpc_aggregated":
		return parseVPC(raw), nil
	case "vpc_subnet_aggregated":
		return parseSubnet(raw), nil
	case "es_instance_aggregated":
		return parseES(raw), nil
	case "k8s_cluster_aggregated":
		return parseK8S(raw)
	case "ecs_disk_aggregated":
		return parseVolume(raw), nil
	case "ecs_image_aggregated":
		return parseImage(raw), nil
	case "ecs_snapshot_aggregated":
		return parseSnapshot(raw), nil
	case "cdn_domain_aggregated":
		return parseCDN(raw), nil
	case "cen_cen_aggregated":
		return parseCen(raw), nil
	case "ecs_eni_raw":
		return parseEni(raw), nil
	case "veen_instance_aggregated":
		return parseEdgeServer(raw), nil
	case "veen_eip_aggregated":
		return parseEdgeEip(raw), nil
	case "veen_volume_aggregated":
		return parseEdgeVolume(raw), nil
	case "veen_image_aggregated":
		return parseEdgeImage(raw), nil
	case "veen_vpc_aggregated":
		return parseEdgeVPC(raw), nil
	case "veen_subnet_aggregated":
		return parseEdgeSubnet(raw), nil
	case "veen_nat_aggregated":
		return parseEdgeNat(raw), nil
	case "veen_securityGroup_aggregated":
		return parseEdgeSG(raw), nil
	case "veen_alb_aggregated":
		return parseEdgeAlb(raw), nil
	case "veen_albListener_aggregated":
		return parseEdgeAlbListener(raw), nil
	case "veen_clb_aggregated":
		return parseEdgeClb(raw), nil
	case "veen_clbListener_aggregated":
		return parseEdgeClbListener(raw), nil
	case "vefaas_function_aggregated":
		return parseFunction(raw), nil
	case "filenas_nas_aggregated":
		return parseNas(raw), nil
	case "tls_topic_aggregated":
		return parseTLS(raw), nil
	case "auditlog-entries-aggregated":
		return parseAuditLog(raw), nil
	default:
		return nil, fmt.Errorf("unknown type")
	}
}

var regionIdToName = map[string]string{
	"cn-beijing":     "北京",
	"cn-shanghai":    "上海",
	"cn-guangzhou":   "广州",
	"cn-chengdu":     "成都",
	"cn-hongkong":    "香港",
	"cn-hangzhou":    "杭州",
	"cn-qingdao":     "青岛",
	"ap-southeast-1": "亚太东南（柔佛）",
}
