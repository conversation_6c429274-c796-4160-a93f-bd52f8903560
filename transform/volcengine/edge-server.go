package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type EdgeServer struct {
	Veen Veen `json:"veen"`
}

type Veen struct {
	InstanceIdentity    string      `json:"instance_identity"`
	InstanceUUID        string      `json:"instance_uuid"`
	InstanceName        string      `json:"instance_name"`
	LoadType            string      `json:"load_type"`
	CloudServerIdentity string      `json:"cloud_server_identity"`
	CloudServerName     string      `json:"cloud_server_name"`
	VpcIdentity         string      `json:"vpc_identity"`
	Namespace           string      `json:"namespace"`
	SubnetCIDR          string      `json:"subnet_cidr"`
	Cluster             VeenCluster `json:"cluster"`
	Spec                string      `json:"spec"`
	SpecDisplay         string      `json:"spec_display"`
	CPU                 string      `json:"cpu"`
	Mem                 string      `json:"mem"`
	Status              string      `json:"status"`
	Creator             string      `json:"creator"`
	Image               VeenImage   `json:"image"`
	Storage             Storage     `json:"storage"`
	Network             Network     `json:"network"`
	GPU                 GPU         `json:"gpu"`
	Secret              Secret      `json:"secret"`
	Account             Account     `json:"account"`
	InstanceDesc        string      `json:"instance_desc"`
	HostName            string      `json:"host_name"`
	Arch                string      `json:"arch"`
	StartTime           int64       `json:"start_time"`
	EndTime             int64       `json:"end_time"`
	CreateTime          int64       `json:"create_time"`
	UpdateTime          int64       `json:"update_time"`
}

type Account struct {
	AccountIdentity int64 `json:"account_identity"`
}

type VeenCluster struct {
	ClusterName string `json:"cluster_name"`
	Country     string `json:"country"`
	Region      string `json:"region"`
	Province    string `json:"province"`
	City        string `json:"city"`
	ISP         string `json:"isp"`
	Level       string `json:"level"`
	Alias       string `json:"alias"`
}

type GPU struct {
	Gpus []interface{} `json:"gpus"`
}

type VeenImage struct {
	ImageIdentity string `json:"image_identity"`
	ImageName     string `json:"image_name"`
	SystemArch    string `json:"system_arch"`
	SystemType    string `json:"system_type"`
	SystemBit     string `json:"system_bit"`
	SystemVersion string `json:"system_version"`
	Property      string `json:"property"`
	DisableVGA    bool   `json:"disable_vga"`
	ImageBootMode string `json:"image_boot_mode"`
}

type Network struct {
	InternalInterface  InternalInterface   `json:"internal_interface"`
	ExternalInterface  ExternalInterface   `json:"external_interface"`
	ExternalInterfaces []ExternalInterface `json:"external_interfaces"`
	DefaultISP         string              `json:"default_isp"`
}

type ExternalInterface struct {
	IPAddr             string                `json:"ip_addr"`
	Mask               string                `json:"mask"`
	Ip6Addr            string                `json:"ip6_addr"`
	Mask6              string                `json:"mask6"`
	IPS                []ExternalInterfaceIP `json:"ips"`
	MACAddr            string                `json:"mac_addr"`
	BandwidthPeak      string                `json:"bandwidth_peak"`
	BandwidthPackageID int64                 `json:"bandwidth_package_id"`
}

type ExternalInterfaceIP struct {
	Addr      string `json:"addr"`
	Mask      string `json:"mask"`
	ISP       string `json:"isp"`
	IPVersion string `json:"ip_version"`
}

type InternalInterface struct {
	IPAddr        string                `json:"ip_addr"`
	Mask          string                `json:"mask"`
	Ip6Addr       string                `json:"ip6_addr"`
	Mask6         string                `json:"mask6"`
	IPS           []InternalInterfaceIP `json:"ips"`
	MACAddr       string                `json:"mac_addr"`
	BandwidthPeak string                `json:"bandwidth_peak"`
}

type InternalInterfaceIP struct {
	Addr      string `json:"addr"`
	Mask      string `json:"mask"`
	ISP       string `json:"isp"`
	IPVersion string `json:"ip_version"`
	Primary   bool   `json:"primary"`
}

type Secret struct {
	SecretType int64  `json:"secret_type"`
	SecretData string `json:"secret_data"`
}

type Storage struct {
	SystemDisk   DataDisk   `json:"system_disk"`
	DataDisk     DataDisk   `json:"data_disk"`
	DataDiskList []DataDisk `json:"data_disk_list"`
}

type DataDisk struct {
	StorageType string `json:"storage_type"`
	Capacity    string `json:"capacity"`
}

func parseEdgeServer(raw map[string]any) model.Asset {
	original := &EdgeServer{}
	meta := &model.ObjectMeta{}
	ecs := &model.ECS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Veen.InstanceIdentity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "edge-ecs", original.Veen.InstanceIdentity)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Veen.InstanceDesc
	meta.Kind = "edge-ecs"
	meta.Name = original.Veen.InstanceName

	ecs.Hostname = original.Veen.HostName
	ecs.Class = "edge"
	ecs.OSName = original.Veen.Image.SystemType
	osType := strings.ToLower(original.Veen.Image.SystemArch)
	ecs.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")

	ecs.PrimaryPrivateIP = original.Veen.Network.InternalInterface.IPAddr
	ecs.PrimaryPublicIP = original.Veen.Network.ExternalInterface.IPAddr

	ecs.PrivateIPList = lo.Filter(
		lo.Uniq(lo.Map(original.Veen.Network.InternalInterface.IPS, func(e InternalInterfaceIP, _ int) string { return e.Addr })),
		func(e string, _ int) bool { return e != "" },
	)
	ecs.PublicIPList = lo.Filter(
		lo.Uniq(
			lo.FlatMap(original.Veen.Network.ExternalInterfaces,
				func(e ExternalInterface, _ int) []string {
					return lo.Map(e.IPS, func(e ExternalInterfaceIP, _ int) string { return e.Addr })
				},
			),
		),
		func(e string, _ int) bool { return e != "" },
	)

	ecs.Spec = original.Veen.Spec
	ecs.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Veen.VpcIdentity)
	ecs.Status = lo.Ternary(strings.EqualFold(original.Veen.Status, "running"), "running", "stopped")
	// not supported by volcengine
	ecs.DeleteProtection = utils.DataPointer(false)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ecs)
	return asset
}
