package cen

import (
	"AssetStandardizer/graph"
	"strings"
)

var cenSchema = graph.NodeSchema{
	Label: "CEN",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"status":             {Name: "status"},
	},
}

var cenRouteEntrySchema = graph.NodeSchema{
	Label: "CENRouteEntry",
	Properties: map[string]graph.PropertyRef{
		"uid":               {Name: "uid"},
		"src_instance_id":   {Name: "src_instance_id"},
		"src_instance_uid":  {Name: "src_instance_uid"},
		"next_instance_id":  {Name: "next_instance_id"},
		"next_instance_uid": {Name: "next_instance_uid"},
		"dst_cidr_block":    {Name: "dst_cidr_block"},
		"status":            {Name: "status"},
		"class":             {Name: "class"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CEN",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_CEN_ROUTE_ENTRY",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var labelSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"key":          {Name: "key"},
		"value":        {Name: "value"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CEN",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_LABEL",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
var cenAttachedInstanceSchema = graph.NodeSchema{
	Label: "CENAttachedInstance",
	Properties: map[string]graph.PropertyRef{
		"uid":           {Name: "uid"},
		"instance_id":   {Name: "instance_id"},
		"instance_type": {Name: "instance_type"},
		"instance_name": {Name: "instance_name"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CEN",
			TargetNodeMatcher: map[string]graph.PropertyRef{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "ATTACHED_TO_CEN",
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated"},
			},
		},
	},
}

var knowInstanceRelSchema = graph.RelSchema{
	TargetNodeMatcher: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	RelLabel:  "IS",
	Direction: graph.OUTWARD,
	Properties: map[string]graph.PropertyRef{
		"last_updated": {Name: "last_updated"},
	},
}

func relToInstance(instanceType string) []graph.RelSchema {
	rel := knowInstanceRelSchema
	switch strings.ToLower(instanceType) {
	case "vpc":
		rel.TargetNodeLabel = "VPC"
	default:
		return []graph.RelSchema{}
	}
	return []graph.RelSchema{rel}
}
