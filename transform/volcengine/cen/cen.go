package cen

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	"AssetStandardizer/transform/utils"
	provider_utils "AssetStandardizer/transform/volcengine/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cen"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cen_aggregated", // FIXME: Verify the correct message type for volcengine CEN
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cen_list"] = append(resourceData["cen_list"], utils.GenParamsFromStruct(resource))

		resourceData["cen_route_entry_list"] = append(resourceData["cen_route_entry_list"],
			utils.GenParamsFromStructSlice(resource.CenRouteEntry)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		for _, instance := range resource.AttachedInstance {
			instanceType := instance.InstanceType
			resourceData[instanceType] = append(resourceData[instanceType], utils.GenParamsFromStruct(instance))
		}
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cenSchema, resourceData["cen_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, cenRouteEntrySchema, resourceData["cen_route_entry_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})

	for key, instances := range resourceData {
		if strings.HasSuffix(key, "_list") {
			continue
		}
		instanceSchema := cenAttachedInstanceSchema
		instanceSchema.OtherRelationships = append(instanceSchema.OtherRelationships, relToInstance(key)...)
		graph.Run(n4jSession, instanceSchema, instances, map[string]any{"last_updated": "test"})
	}
}

type Cen struct {
	AttachedInstances []AttachedInstance `json:"attachedInstances"`
	Cen               CenClass           `json:"cen"`
	CenRouteEntries   []CenRouteEntry    `json:"cenRouteEntries"`
}

type AttachedInstance struct {
	CenID            string `json:"CenId"`
	CreationTime     string `json:"CreationTime"`
	InstanceID       string `json:"InstanceId"`
	InstanceOwnerID  string `json:"InstanceOwnerId"`
	InstanceRegionID string `json:"InstanceRegionId"`
	InstanceType     string `json:"InstanceType"`
	Status           string `json:"Status"`
}

type CenClass struct {
	AccountID              string        `json:"AccountId"`
	CenBandwidthPackageIDS []interface{} `json:"CenBandwidthPackageIds"`
	CenID                  string        `json:"CenId"`
	CenName                string        `json:"CenName"`
	CreationTime           string        `json:"CreationTime"`
	Description            string        `json:"Description"`
	ProjectName            string        `json:"ProjectName"`
	Status                 string        `json:"Status"`
	Tags                   []Tag         `json:"Tags"`
	UpdateTime             string        `json:"UpdateTime"`
}

type CenRouteEntry struct {
	AsPath               []string `json:"AsPath"`
	CenID                string   `json:"CenId"`
	DestinationCIDRBlock string   `json:"DestinationCidrBlock"`
	InstanceID           string   `json:"InstanceId"`
	InstanceRegionID     string   `json:"InstanceRegionId"`
	InstanceType         string   `json:"InstanceType"`
	PublishStatus        string   `json:"PublishStatus"`
	Status               string   `json:"Status"`
	Type                 string   `json:"Type"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CenGraph, error) {
	original := &Cen{}
	resource := &model.CenGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cen.CenID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "cen", original.Cen.CenID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cen"
	resource.Name = original.Cen.CenName
	resource.OriginalLabels = lo.Map(original.Cen.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.Key, tag.Value, resource.UID)
	})

	resource.Status = lo.Ternary(strings.ToLower(original.Cen.Status) == "available", "running", "stopped")

	resource.AttachedInstance = lo.Map(original.AttachedInstances, func(e AttachedInstance, _ int) *model.CenAttachedInstanceGraph {
		instanceUID := utils.GenerateUID(provider_utils.ProviderID, strings.ToLower(e.InstanceType), e.InstanceID)
		return &model.CenAttachedInstanceGraph{
			BaseNode: model.BaseNode{
				UID:       instanceUID,
				TargetUID: resource.UID,
			},
			InstanceID:   e.InstanceID,
			InstanceType: strings.ToLower(e.InstanceType),
		}
	})

	resource.CenRouteEntry = lo.Map(original.CenRouteEntries, func(e CenRouteEntry, _ int) *model.CenRouteEntryGraph {
		return &model.CenRouteEntryGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "cen-route-entry", resource.UID+":"+e.DestinationCIDRBlock+":"+e.InstanceID),
				TargetUID: resource.UID,
			},
			NextInstanceID:  e.InstanceID,
			NextInstanceUID: utils.GenerateUID(provider_utils.ProviderID, strings.ToLower(e.InstanceType), e.InstanceID),
			DstCidrBlock:    provider_utils.FormatCIDR(e.DestinationCIDRBlock),
		}
	})

	return resource, nil
}
