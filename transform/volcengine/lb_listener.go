package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

type ClbListenerAgg struct {
	Acls     []interface{}       `json:"acls"`
	Listener ClbListenerInstance `json:"listener"`
	LBID     string              `json:"lbId"`
}

type ClbListenerInstance struct {
	ACLIDS                 []interface{} `json:"AclIds"`
	ACLStatus              string        `json:"AclStatus"`
	ACLType                string        `json:"AclType"`
	Bandwidth              int64         `json:"Bandwidth"`
	CertificateID          string        `json:"CertificateId"`
	ClientBodyTimeout      interface{}   `json:"ClientBodyTimeout"`
	ClientHeaderTimeout    interface{}   `json:"ClientHeaderTimeout"`
	ConnectionDrainEnabled string        `json:"ConnectionDrainEnabled"`
	ConnectionDrainTimeout int64         `json:"ConnectionDrainTimeout"`
	Cookie                 string        `json:"Cookie"`
	CreateTime             string        `json:"CreateTime"`
	Description            string        `json:"Description"`
	Enabled                string        `json:"Enabled"`
	EndPort                interface{}   `json:"EndPort"`
	HealthCheck            HealthCheck   `json:"HealthCheck"`
	Http2Enabled           interface{}   `json:"Http2Enabled"`
	KeepaliveTimeout       interface{}   `json:"KeepaliveTimeout"`
	ListenerID             string        `json:"ListenerId"`
	ListenerName           string        `json:"ListenerName"`
	PersistenceTimeout     int64         `json:"PersistenceTimeout"`
	PersistenceType        string        `json:"PersistenceType"`
	Port                   int           `json:"Port"`
	Protocol               string        `json:"Protocol"`
	ProxyConnectTimeout    interface{}   `json:"ProxyConnectTimeout"`
	ProxyProtocolType      string        `json:"ProxyProtocolType"`
	ProxyReadTimeout       interface{}   `json:"ProxyReadTimeout"`
	ProxySendTimeout       interface{}   `json:"ProxySendTimeout"`
	Scheduler              string        `json:"Scheduler"`
	SecurityPolicyID       interface{}   `json:"SecurityPolicyId"`
	SendTimeout            interface{}   `json:"SendTimeout"`
	ServerGroupID          string        `json:"ServerGroupId"`
	StartPort              interface{}   `json:"StartPort"`
	Status                 string        `json:"Status"`
	Tags                   []interface{} `json:"Tags"`
	UpdateTime             string        `json:"UpdateTime"`
}

type HealthCheck struct {
	Domain             interface{} `json:"Domain"`
	Enabled            string      `json:"Enabled"`
	HealthyThreshold   int64       `json:"HealthyThreshold"`
	HTTPCode           interface{} `json:"HttpCode"`
	Interval           int64       `json:"Interval"`
	Method             interface{} `json:"Method"`
	Port               interface{} `json:"Port"`
	Timeout            int64       `json:"Timeout"`
	UDPExpect          interface{} `json:"UdpExpect"`
	UDPRequest         interface{} `json:"UdpRequest"`
	UnHealthyThreshold int64       `json:"UnHealthyThreshold"`
	URI                interface{} `json:"Uri"`
}

func parseClbListener(raw map[string]any) model.Asset {
	original := &ClbListenerAgg{}
	meta := &model.ObjectMeta{}
	listener := &model.LbListener{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Listener.ListenerID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "lb-listener", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "lb-listener"
	meta.Name = original.Listener.ListenerName

	listener.LbID = utils.GenerateUID(ProviderID, "lb", original.LBID)
	listener.LbOriginalID = original.LBID
	listener.Status = strings.ToLower(original.Listener.Status)
	listener.Port = original.Listener.Port
	listener.Protocol = strings.ToLower(original.Listener.Protocol)
	listener.AclStatus = original.Listener.ACLStatus
	listener.AclType = original.Listener.ACLType
	listener.CertExists = utils.DataPointer(original.Listener.CertificateID != "")
	listener.HealthCheckEnabled = utils.DataPointer(original.Listener.HealthCheck.Enabled == "on")

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, listener, utils.WithOmitempty())
	return asset
}

type AlbListenerAgg struct {
	Acls     []interface{}       `json:"acls"`
	LBID     string              `json:"lbId"`
	Listener AlbListenerInstance `json:"listener"`
}

type AlbListenerInstance struct {
	ACLIDS                  []interface{}     `json:"AclIds"`
	ACLStatus               string            `json:"AclStatus"`
	ACLType                 string            `json:"AclType"`
	CACertificateID         string            `json:"CACertificateId"`
	CERTCenterCertificateID string            `json:"CertCenterCertificateId"`
	CertificateID           string            `json:"CertificateId"`
	CertificateSource       string            `json:"CertificateSource"`
	CreateTime              string            `json:"CreateTime"`
	CustomizedCFGID         string            `json:"CustomizedCfgId"`
	Description             string            `json:"Description"`
	DomainExtensions        []DomainExtension `json:"DomainExtensions"`
	EnableHttp2             string            `json:"EnableHttp2"`
	EnableQuic              string            `json:"EnableQuic"`
	Enabled                 string            `json:"Enabled"`
	ListenerID              string            `json:"ListenerId"`
	ListenerName            string            `json:"ListenerName"`
	LoadBalancerID          string            `json:"LoadBalancerId"`
	Port                    int               `json:"Port"`
	ProjectName             string            `json:"ProjectName"`
	Protocol                string            `json:"Protocol"`
	ProxyProtocolDisabled   interface{}       `json:"ProxyProtocolDisabled"`
	ServerGroupID           string            `json:"ServerGroupId"`
	ServerGroups            []ServerGroup     `json:"ServerGroups"`
	Status                  string            `json:"Status"`
	UpdateTime              string            `json:"UpdateTime"`
}

func parseAlbListener(raw map[string]any) model.Asset {
	original := &AlbListenerAgg{}
	meta := &model.ObjectMeta{}
	listener := &model.LbListener{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Listener.ListenerID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "lb-listener", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "lb-listener"
	meta.Name = original.Listener.ListenerName

	listener.LbID = utils.GenerateUID(ProviderID, "lb", original.LBID)
	listener.LbOriginalID = original.LBID
	listener.Status = strings.ToLower(original.Listener.Status)
	listener.Port = original.Listener.Port
	listener.Protocol = strings.ToLower(original.Listener.Protocol)
	listener.AclStatus = original.Listener.ACLStatus
	listener.AclType = original.Listener.ACLType
	listener.CertExists = utils.DataPointer(original.Listener.CertificateID != "")
	listener.HealthCheckEnabled = nil

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, listener, utils.WithOmitempty())
	return asset
}
