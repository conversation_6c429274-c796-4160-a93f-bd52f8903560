package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type Cen struct {
	AttachedInstances []AttachedInstance `json:"attachedInstances"`
	Cen               CenClass           `json:"cen"`
	CenRouteEntries   []CenRouteEntry    `json:"cenRouteEntries"`
}

type AttachedInstance struct {
	CenID            string `json:"CenId"`
	CreationTime     string `json:"CreationTime"`
	InstanceID       string `json:"InstanceId"`
	InstanceOwnerID  string `json:"InstanceOwnerId"`
	InstanceRegionID string `json:"InstanceRegionId"`
	InstanceType     string `json:"InstanceType"`
	Status           string `json:"Status"`
}

type CenClass struct {
	AccountID              string        `json:"AccountId"`
	CenBandwidthPackageIDS []interface{} `json:"CenBandwidthPackageIds"`
	CenID                  string        `json:"CenId"`
	CenName                string        `json:"CenName"`
	CreationTime           string        `json:"CreationTime"`
	Description            string        `json:"Description"`
	ProjectName            string        `json:"ProjectName"`
	Status                 string        `json:"Status"`
	Tags                   []Tag         `json:"Tags"`
	UpdateTime             string        `json:"UpdateTime"`
}

type CenRouteEntry struct {
	AsPath               []string `json:"AsPath"`
	CenID                string   `json:"CenId"`
	DestinationCIDRBlock string   `json:"DestinationCidrBlock"`
	InstanceID           string   `json:"InstanceId"`
	InstanceRegionID     string   `json:"InstanceRegionId"`
	InstanceType         string   `json:"InstanceType"`
	PublishStatus        string   `json:"PublishStatus"`
	Status               string   `json:"Status"`
	Type                 string   `json:"Type"`
}

func parseCen(raw map[string]any) model.Asset {
	original := &Cen{}
	meta := &model.ObjectMeta{}
	cen := &model.Cen{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Cen.CenID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "cen", original.Cen.CenID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "cen"
	meta.Name = original.Cen.CenName
	meta.OriginalLabels = utils.Map(original.Cen.Tags, func(tag Tag) model.KV { return model.KV{Key: tag.Key, Value: tag.Value} })

	cen.Status = lo.Ternary(strings.ToLower(original.Cen.Status) == "available", "running", "stopped")
	cen.AttachedInstances = lo.Map(original.AttachedInstances, func(e AttachedInstance, _ int) model.CenAttachedInstance {
		return model.CenAttachedInstance{
			InstanceID:   e.InstanceID,
			InstanceUID:  utils.GenerateUID(ProviderID, strings.ToLower(e.InstanceType), e.InstanceID),
			InstanceType: strings.ToLower(e.InstanceType),
		}
	})
	cen.CenRouteEntries = lo.Map(original.CenRouteEntries, func(e CenRouteEntry, _ int) model.CenRouteEntry {
		return model.CenRouteEntry{
			NextInstanceID:  e.InstanceID,
			NextInstanceUID: utils.GenerateUID(ProviderID, strings.ToLower(e.InstanceType), e.InstanceID),
			DstCidrBlock:    formatCIDR(e.DestinationCIDRBlock),
		}
	})
	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, cen)
	return asset
}
