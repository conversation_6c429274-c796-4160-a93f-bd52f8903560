package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"
)

type CDN struct {
	CDNDomain    CDNDomain    `json:"cdnDomain"`
	DomainConfig DomainConfig `json:"domainConfig"`
}

type CDNDomain struct {
	BackupCname           interface{} `json:"BackupCname"`
	BackupOrigin          interface{} `json:"BackupOrigin"`
	CacheShared           string      `json:"CacheShared"`
	CacheSharedTargetHost string      `json:"CacheSharedTargetHost"`
	Cname                 string      `json:"Cname"`
	ConfigStatus          string      `json:"ConfigStatus"`
	CreateTime            int64       `json:"CreateTime"`
	Domain                string      `json:"Domain"`
	DomainLock            DomainLock  `json:"DomainLock"`
	FeatureConfig         interface{} `json:"FeatureConfig"`
	HTTPS                 bool        `json:"HTTPS"`
	IPv6                  bool        `json:"IPv6"`
	IsConflictDomain      bool        `json:"IsConflictDomain"`
	OriginProtocol        string      `json:"OriginProtocol"`
	PrimaryOrigin         []string    `json:"PrimaryOrigin"`
	Project               string      `json:"Project"`
	ResourceTags          []string    `json:"ResourceTags"`
	ServiceRegion         string      `json:"ServiceRegion"`
	ServiceType           string      `json:"ServiceType"`
	Status                string      `json:"Status"`
	UpdateTime            int64       `json:"UpdateTime"`
}

type DomainLock struct {
	Remark string `json:"Remark"`
	Status string `json:"Status"`
}

type DomainConfig struct {
	Metadata         Metadata          `json:"Metadata"`
	DomainConfig     DomainConfigClass `json:"DomainConfig"`
	FeatureConfig    interface{}       `json:"FeatureConfig"`
	ModuleLockConfig interface{}       `json:"ModuleLockConfig"`
}

type DomainConfigClass struct {
	AreaAccessRule      AreaAccessRule     `json:"AreaAccessRule"`
	BackupCname         interface{}        `json:"BackupCname"`
	BandwidthLimit      BandwidthLimit     `json:"BandwidthLimit"`
	BrowserCache        interface{}        `json:"BrowserCache"`
	Cache               []Cache            `json:"Cache"`
	CacheHost           interface{}        `json:"CacheHost"`
	CacheKey            []CacheKey         `json:"CacheKey"`
	Cname               string             `json:"Cname"`
	Compression         Compression        `json:"Compression"`
	ConditionalOrigin   interface{}        `json:"ConditionalOrigin"`
	CreateTime          int64              `json:"CreateTime"`
	CustomErrorPage     CustomErrorPage    `json:"CustomErrorPage"`
	CustomizeAccessRule interface{}        `json:"CustomizeAccessRule"`
	Domain              string             `json:"Domain"`
	DownloadSpeedLimit  DownloadSpeedLimit `json:"DownloadSpeedLimit"`
	FollowRedirect      bool               `json:"FollowRedirect"`
	HTTPS               HTTPS              `json:"HTTPS"`
	HTTPForcedRedirect  ForcedRedirect     `json:"HttpForcedRedirect"`
	IPv6                IPv6               `json:"IPv6"`
	IPAccessRule        IPAccessRule       `json:"IpAccessRule"`
	IPFreqLimit         interface{}        `json:"IpFreqLimit"`
	IPSpeedLimit        interface{}        `json:"IpSpeedLimit"`
	LockStatus          string             `json:"LockStatus"`
	MethodDeniedRule    MethodDeniedRule   `json:"MethodDeniedRule"`
	NegativeCache       interface{}        `json:"NegativeCache"`
	Origin              []Origin           `json:"Origin"`
	OriginAccessRule    interface{}        `json:"OriginAccessRule"`
	OriginArg           []OriginArg        `json:"OriginArg"`
	OriginCERTCheck     interface{}        `json:"OriginCertCheck"`
	OriginHost          string             `json:"OriginHost"`
	OriginIPv6          string             `json:"OriginIPv6"`
	OriginProtocol      string             `json:"OriginProtocol"`
	OriginRange         bool               `json:"OriginRange"`
	OriginRetry         interface{}        `json:"OriginRetry"`
	OriginRewrite       OriginRewrite      `json:"OriginRewrite"`
	OriginSni           OriginSni          `json:"OriginSni"`
	PageOptimization    interface{}        `json:"PageOptimization"`
	Project             string             `json:"Project"`
	Quic                interface{}        `json:"Quic"`
	RedirectionRewrite  RedirectionRewrite `json:"RedirectionRewrite"`
	RefererAccessRule   RefererAccessRule  `json:"RefererAccessRule"`
	RemoteAuth          RemoteAuth         `json:"RemoteAuth"`
	RequestBlockRule    interface{}        `json:"RequestBlockRule"`
	RequestHeader       interface{}        `json:"RequestHeader"`
	ResponseHeader      interface{}        `json:"ResponseHeader"`
	ServiceRegion       string             `json:"ServiceRegion"`
	ServiceType         string             `json:"ServiceType"`
	SignedURLAuth       interface{}        `json:"SignedUrlAuth"`
	Status              string             `json:"Status"`
	Timeout             Timeout            `json:"Timeout"`
	UaAccessRule        interface{}        `json:"UaAccessRule"`
	UpdateTime          int64              `json:"UpdateTime"`
	URLNormalize        URLNormalize       `json:"UrlNormalize"`
	VideoDrag           IPv6               `json:"VideoDrag"`
}

type AreaAccessRule struct {
	Area     interface{} `json:"Area"`
	RuleType string      `json:"RuleType"`
	Switch   bool        `json:"Switch"`
}

type BandwidthLimit struct {
	BandwidthLimitRule BandwidthLimitRule `json:"BandwidthLimitRule"`
	Switch             bool               `json:"Switch"`
}

type BandwidthLimitRule struct {
	BandwidthLimitAction BandwidthLimitAction `json:"BandwidthLimitAction"`
}

type BandwidthLimitAction struct {
	BandwidthThreshold int64  `json:"BandwidthThreshold"`
	LimitType          string `json:"LimitType"`
	SpeedLimitRate     int64  `json:"SpeedLimitRate"`
}

type Cache struct {
	CacheAction CacheAction  `json:"CacheAction"`
	Condition   CDNCondition `json:"Condition"`
}

type CacheAction struct {
	Action        string `json:"Action"`
	DefaultPolicy string `json:"DefaultPolicy"`
	IgnoreCase    bool   `json:"IgnoreCase"`
	TTL           int64  `json:"Ttl"`
}

type CDNCondition struct {
	ConditionRule []ConditionRule `json:"ConditionRule"`
	Connective    string          `json:"Connective"`
}

type ConditionRule struct {
	Name     string `json:"Name"`
	Object   string `json:"Object"`
	Operator string `json:"Operator"`
	Type     string `json:"Type"`
	Value    string `json:"Value"`
}

type CacheKey struct {
	CacheKeyAction CacheKeyAction `json:"CacheKeyAction"`
	Condition      Condition      `json:"Condition"`
}

type CacheKeyAction struct {
	CacheKeyComponents []CacheKeyComponent `json:"CacheKeyComponents"`
}

type CacheKeyComponent struct {
	Action     string `json:"Action"`
	IgnoreCase bool   `json:"IgnoreCase"`
	Object     string `json:"Object"`
	Subobject  string `json:"Subobject"`
}

type Compression struct {
	CompressionRules interface{} `json:"CompressionRules"`
	Switch           bool        `json:"Switch"`
}

type CustomErrorPage struct {
	ErrorPageRule interface{} `json:"ErrorPageRule"`
	Switch        bool        `json:"Switch"`
}

type DownloadSpeedLimit struct {
	DownloadSpeedLimitRules interface{} `json:"DownloadSpeedLimitRules"`
	Switch                  bool        `json:"Switch"`
}

type ForcedRedirect struct {
	EnableForcedRedirect bool   `json:"EnableForcedRedirect"`
	StatusCode           string `json:"StatusCode"`
}

type HTTPS struct {
	CERTCheck      interface{}    `json:"CertCheck"`
	CERTInfo       interface{}    `json:"CertInfo"`
	CERTInfoList   interface{}    `json:"CertInfoList"`
	DisableHTTP    bool           `json:"DisableHttp"`
	ForcedRedirect ForcedRedirect `json:"ForcedRedirect"`
	Http2          bool           `json:"HTTP2"`
	Hsts           Hsts           `json:"Hsts"`
	Ocsp           bool           `json:"OCSP"`
	Switch         bool           `json:"Switch"`
	TLSVersion     []interface{}  `json:"TlsVersion"`
}

type Hsts struct {
	Subdomain interface{} `json:"Subdomain"`
	Switch    bool        `json:"Switch"`
	TTL       int64       `json:"Ttl"`
}

type IPv6 struct {
	Switch bool `json:"Switch"`
}

type MethodDeniedRule struct {
	Methods string `json:"Methods"`
	Switch  bool   `json:"Switch"`
}

type Origin struct {
	Condition    interface{}  `json:"Condition"`
	OriginAction OriginAction `json:"OriginAction"`
}

type OriginAction struct {
	OriginLines []OriginLine `json:"OriginLines"`
}

type OriginLine struct {
	Address             string      `json:"Address"`
	BucketName          interface{} `json:"BucketName"`
	HTTPPort            string      `json:"HttpPort"`
	HTTPSPort           string      `json:"HttpsPort"`
	InstanceType        string      `json:"InstanceType"`
	OriginHost          string      `json:"OriginHost"`
	OriginType          string      `json:"OriginType"`
	PrivateBucketAccess bool        `json:"PrivateBucketAccess"`
	PrivateBucketAuth   interface{} `json:"PrivateBucketAuth"`
	Region              string      `json:"Region"`
	Weight              string      `json:"Weight"`
}

type OriginArg struct {
	Condition       Condition       `json:"Condition"`
	OriginArgAction OriginArgAction `json:"OriginArgAction"`
}

type OriginArgAction struct {
	OriginArgComponents []OriginArgComponent `json:"OriginArgComponents"`
}

type OriginArgComponent struct {
	Action    string `json:"Action"`
	Object    string `json:"Object"`
	Subobject string `json:"Subobject"`
}

type OriginRewrite struct {
	OriginRewriteRule interface{} `json:"OriginRewriteRule"`
	Switch            bool        `json:"Switch"`
}

type OriginSni struct {
	SniDomain string `json:"SniDomain"`
	Switch    bool   `json:"Switch"`
}
type RefererAccessRule struct {
	AllowEmpty   bool         `json:"AllowEmpty"`
	ReferersType ReferersType `json:"ReferersType"`
	RuleType     string       `json:"RuleType"`
	Switch       bool         `json:"Switch"`
}

type ReferersType struct {
	CommonType  CommonType  `json:"CommonType"`
	RegularType RegularType `json:"RegularType"`
}

type CommonType struct {
	IgnoreCase   bool     `json:"IgnoreCase"`
	IgnoreScheme bool     `json:"IgnoreScheme"`
	Referers     []string `json:"Referers"`
}

type RegularType struct {
	Referers []string `json:"Referers"`
}

type RedirectionRewrite struct {
	RedirectionRule interface{} `json:"RedirectionRule"`
	Switch          bool        `json:"Switch"`
}

type RemoteAuth struct {
	RemoteAuthRules interface{} `json:"RemoteAuthRules"`
	Switch          bool        `json:"Switch"`
}

type Timeout struct {
	Switch       bool        `json:"Switch"`
	TimeoutRules interface{} `json:"TimeoutRules"`
}

type URLNormalize struct {
	NormalizeObject interface{} `json:"NormalizeObject"`
	Switch          bool        `json:"Switch"`
}

type IPAccessRule struct {
	Switch   bool     `json:"Switch"`
	RuleType string   `json:"RuleType"`
	IP       []string `json:"Ip"`
}

type Metadata struct {
	RequestID string      `json:"RequestId"`
	Action    string      `json:"Action"`
	Version   string      `json:"Version"`
	Service   string      `json:"Service"`
	Region    string      `json:"Region"`
	HTTPCode  int64       `json:"HTTPCode"`
	Error     interface{} `json:"Error"`
}

func parseCDN(raw map[string]any) model.Asset {
	original := &CDN{}
	meta := &model.ObjectMeta{}
	cdn := &model.CDN{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalID = original.CDNDomain.Domain
	meta.UID = utils.GenerateUID(meta.Provider, "cdn", meta.OriginalID)
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "cdn"
	meta.Name = original.CDNDomain.Domain
	meta.OriginalLabels = utils.FilterMap(original.CDNDomain.ResourceTags, func(e string) (model.KV, bool) {
		kv := strings.SplitN(e, ":", 2)
		if len(kv) == 2 {
			return model.KV{Key: kv[0], Value: kv[1]}, true
		}
		return model.KV{}, false
	})

	cdn.Status = utils.UnwrapOr("running", strings.ToLower(original.CDNDomain.Status) == "online", "stopped")
	cdn.HTTPSEnabled = original.CDNDomain.HTTPS
	cdn.CNAME = original.CDNDomain.Cname
	cdn.Coverage = "domestic"
	cdn.Class = strings.ToLower(original.CDNDomain.ServiceType)

	for _, origin := range original.DomainConfig.DomainConfig.Origin {
		for _, originLine := range origin.OriginAction.OriginLines {
			weight, _ := strconv.Atoi(originLine.Weight)
			cdn.SourceSite = append(cdn.SourceSite, model.SourceSite{URL: originLine.Address, Weight: weight})

		}
	}
	cdn.ForceHTTPS = original.DomainConfig.DomainConfig.HTTPForcedRedirect.EnableForcedRedirect

	if original.DomainConfig.DomainConfig.RefererAccessRule.Switch {
		cdn.AllowEmptyReferer = original.DomainConfig.DomainConfig.RefererAccessRule.AllowEmpty
		if original.DomainConfig.DomainConfig.RefererAccessRule.RuleType == "allow" {
			cdn.RefererWhiteList = append(cdn.RefererWhiteList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.CommonType.Referers...)
			cdn.RefererWhiteList = append(cdn.RefererWhiteList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.RegularType.Referers...)
		} else if original.DomainConfig.DomainConfig.RefererAccessRule.RuleType == "deny" {
			cdn.RefererBlackList = append(cdn.RefererBlackList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.CommonType.Referers...)
			cdn.RefererBlackList = append(cdn.RefererBlackList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.RegularType.Referers...)
		}
	}

	if original.DomainConfig.DomainConfig.IPAccessRule.Switch {
		if original.DomainConfig.DomainConfig.IPAccessRule.RuleType == "allow" {
			cdn.IPWhiteList = utils.Map(original.DomainConfig.DomainConfig.IPAccessRule.IP, func(ip string) string { return formatCIDR(ip) })
		} else if original.DomainConfig.DomainConfig.IPAccessRule.RuleType == "deny" {
			cdn.IPBlackList = utils.Map(original.DomainConfig.DomainConfig.IPAccessRule.IP, func(ip string) string { return formatCIDR(ip) })
		}
	}

	cdn.RateLimitEnabled = original.DomainConfig.DomainConfig.DownloadSpeedLimit.Switch
	// not support by aliyun
	cdn.FreqLimitEnabled = false
	// FIXME: get data from cloud monitor, no data now.
	cdn.BandwidthLimitEnabled = original.DomainConfig.DomainConfig.BandwidthLimit.Switch

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, cdn)
	return asset
}
