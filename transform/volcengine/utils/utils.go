package utils

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/utils"
	"fmt"
	"net"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
)

const ProviderID = "volcengine"

func ParseIPRange(cidr string, targetUID string) (*model.IPRangeGraph, error) {
	if len(cidr) == 0 {
		return nil, fmt.Errorf("invalid cidr: %s", cidr)
	} else if !strings.Contains(cidr, "/") {
		cidr = cidr + "/32"
	}
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, fmt.Errorf("invalid cidr: %s", cidr)
	}
	prefixLen, _ := ipnet.Mask.Size()
	return &model.IPRangeGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(ProviderID, "ip-range", ipnet.IP.String()+"/"+strconv.Itoa(prefixLen)),
			TargetUID: targetUID,
		},
		Start: ipnet.IP.String(),
		End:   ipnet.IP.String(), // FIXME: This should calculate the actual end IP of the range
	}, nil
}

func ParseIPRanges(cidrs []string, targetUID string) []*model.IPRangeGraph {
	ranges := make([]*model.IPRangeGraph, 0, len(cidrs))
	for _, cidr := range cidrs {
		if ipRange, err := ParseIPRange(cidr, targetUID); err == nil {
			ranges = append(ranges, ipRange)
		}
	}
	return ranges
}

func GenRuleDescription(rule any) string {
	str, _ := sonic.MarshalString(rule)
	return str
}

func NewResource(provider, kind, originalID string) *model.ObjectMetaGraph {
	return &model.ObjectMetaGraph{
		Provider:   provider,
		OriginalID: originalID,
		Kind:       kind,
	}
}

// FIXME: This might need specific logic for volcengine tag structure
func ParseTags(tags []map[string]string, targetUID string) []*model.KVGraph {
	return lo.Map(tags, func(tag map[string]string, _ int) *model.KVGraph {
		key := ""
		value := ""
		if k, ok := tag["Key"]; ok {
			key = k
		}
		if v, ok := tag["Value"]; ok {
			value = v
		}
		return utils.NewLabel(key, value, targetUID)
	})
}

type PolicyDocument struct {
	Statement []Statement `json:"Statement"`
}

type Statement struct {
	Action    []string `json:"Action"`
	Resource  []string `json:"Resource"`
	Effect    string   `json:"Effect"`
	Condition any      `json:"Condition"`
	Principal any      `json:"Principal"`
}

func ParsePolicyDocument(policy string, targetUID string) []*model.PolicyStatementGraph {
	if policy == "" {
		return nil
	}

	parsedDocument := PolicyDocument{}
	sonic.UnmarshalString(policy, &parsedDocument)

	statements := lo.Map(parsedDocument.Statement, func(statement Statement, _ int) *model.PolicyStatementGraph {
		pd := model.PolicyStatementGraph{}
		pd.Effect = strings.ToLower(statement.Effect)

		pd.Action = statement.Action
		pd.Resource = statement.Resource
		pd.Principal = utils.ToJsonStr(statement.Principal)
		pd.Condition = utils.ToJsonStr(statement.Condition)
		return &pd
	})
	return statements
}

func FormatCIDR(cidr string) string {
	cidr = strings.TrimSpace(cidr)
	if strings.Contains(cidr, "/") {
		return cidr
	}
	return cidr + lo.Ternary(strings.Contains(cidr, ":"), "/128", "/32")
}
