package policy

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/volcengine/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ram_policy_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   10 * time.Second,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resources := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		policy, err := parseOne(assetMsg)
		if err != nil {
			return nil, err
		} else {
			policy.TransformedObject, _ = sonic.MarshalString(policy)
		}

		resources["policy_list"] = append(resources["policy_list"], utils.GenParamsFromStruct(policy))
		for _, statement := range policy.PolicyStatement {
			resources["policy_statement_list"] = append(resources["policy_statement_list"],
				utils.GenParamsFromStruct(statement),
			)
		}
	}
	return resources, nil
}

func updateResources(resources map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, policySchema, resources["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policyStatementSchema, resources["policy_statement_list"], map[string]any{"last_updated": "test"})
}

type PolicyDetail struct {
	CreateDate     string `json:"createDate"`
	Description    string `json:"description"`
	PolicyDocument string `json:"policyDocument"`
	PolicyName     string `json:"policyName"`
	PolicyTrn      string `json:"policyTrn"`
	PolicyType     string `json:"policyType"`
	UpdateDate     string `json:"updateDate"`
}

type PolicyDocument struct {
	Statement []Statement `json:"Statement"`
}

type Statement struct {
	Action    []string `json:"Action"`
	Resource  []string `json:"Resource"`
	Effect    string   `json:"Effect"`
	Condition any      `json:"Condition"`
	Principal any      `json:"Principal"`
}

var policyTypeMap = map[string]string{
	"System": "managed",
	"Custom": "custom",
}

func parseOne(assetMsg *model.AssetMessage) (*model.PolicyGraph, error) {
	original := &PolicyDetail{}
	policy := &model.PolicyGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	policy.Provider = provider_utils.ProviderID
	policy.Region = assetMsg.Region
	policy.OriginalObject = string(assetMsg.RawLog)
	policy.OriginalID = original.PolicyTrn
	policy.UID = utils.GenerateUID(policy.Provider, "policy", original.PolicyName)
	policy.LastSeen = time.Now().UnixMilli()
	policy.Description = original.Description
	policy.Kind = "policy"
	policy.Name = original.PolicyName

	v, ok := policyTypeMap[original.PolicyType]
	policy.Class = lo.Ternary(ok, v, strings.ToLower(original.PolicyType))

	policy.PolicyStatement = provider_utils.ParsePolicyDocument(original.PolicyDocument, policy.UID)

	return policy, nil
}
