package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"time"
)

type K8S struct {
	Cluster Cluster     `json:"cluster"`
	Subnets []K8SSubnet `json:"subnets"`
}

type Cluster struct {
	ClusterConfig           ClusterConfig  `json:"ClusterConfig"`
	CreateClientToken       string         `json:"CreateClientToken"`
	CreateTime              string         `json:"CreateTime"`
	DeleteProtectionEnabled bool           `json:"DeleteProtectionEnabled"`
	Description             string         `json:"Description"`
	ID                      string         `json:"Id"`
	KubernetesVersion       string         `json:"KubernetesVersion"`
	LoggingConfig           LoggingConfig  `json:"LoggingConfig"`
	Name                    string         `json:"Name"`
	NodeStatistics          NodeStatistics `json:"NodeStatistics"`
	PodsConfig              PodsConfig     `json:"PodsConfig"`
	ServicesConfig          ServicesConfig `json:"ServicesConfig"`
	Status                  Status         `json:"Status"`
	Tags                    []Tag          `json:"Tags"`
	UpdateClientToken       string         `json:"UpdateClientToken"`
	UpdateTime              string         `json:"UpdateTime"`
}

type ClusterConfig struct {
	APIServerEndpoints                 APIServerEndpoints          `json:"ApiServerEndpoints"`
	APIServerPublicAccessConfig        APIServerPublicAccessConfig `json:"ApiServerPublicAccessConfig"`
	APIServerPublicAccessEnabled       bool                        `json:"ApiServerPublicAccessEnabled"`
	ResourcePublicAccessDefaultEnabled bool                        `json:"ResourcePublicAccessDefaultEnabled"`
	SecurityGroupIDS                   []string                    `json:"SecurityGroupIds"`
	SubnetIDS                          []string                    `json:"SubnetIds"`
	VpcID                              string                      `json:"VpcId"`
}

type APIServerEndpoints struct {
	PrivateIP IP `json:"PrivateIp"`
	PublicIP  IP `json:"PublicIp"`
}

type IP struct {
	Ipv4 string `json:"Ipv4"`
}

type APIServerPublicAccessConfig struct {
	AccessSourceIpsv4         interface{}               `json:"AccessSourceIpsv4"`
	PublicAccessNetworkConfig PublicAccessNetworkConfig `json:"PublicAccessNetworkConfig"`
}

type PublicAccessNetworkConfig struct {
	Bandwidth   int64  `json:"Bandwidth"`
	BillingType int64  `json:"BillingType"`
	ISP         string `json:"Isp"`
}

type LoggingConfig struct {
	LogProjectID string     `json:"LogProjectId"`
	LogSetups    []LogSetup `json:"LogSetups"`
}

type LogSetup struct {
	Enabled    bool   `json:"Enabled"`
	LogTopicID string `json:"LogTopicId"`
	LogTTL     int64  `json:"LogTtl"`
	LogType    string `json:"LogType"`
}

type NodeStatistics struct {
	CreatingCount int64 `json:"CreatingCount"`
	DeletingCount int64 `json:"DeletingCount"`
	FailedCount   int64 `json:"FailedCount"`
	RunningCount  int64 `json:"RunningCount"`
	TotalCount    int64 `json:"TotalCount"`
	UpdatingCount int64 `json:"UpdatingCount"`
}

type PodsConfig struct {
	FlannelConfig  FlannelConfig `json:"FlannelConfig"`
	PodNetworkMode string        `json:"PodNetworkMode"`
	VpcCniConfig   VpcCniConfig  `json:"VpcCniConfig"`
}

type FlannelConfig struct {
	MaxPodsPerNode int64    `json:"MaxPodsPerNode"`
	PodCidrs       []string `json:"PodCidrs"`
}

type VpcCniConfig struct {
	SubnetIDS []string `json:"SubnetIds"`
}

type ServicesConfig struct {
	ServiceCidrsv4 []string `json:"ServiceCidrsv4"`
}

type Status struct {
	Conditions []Condition `json:"Conditions"`
	Phase      string      `json:"Phase"`
}

type Condition struct {
	Type string `json:"Type"`
}

type Tag struct {
	Key   string `json:"Key"`
	Type  string `json:"Type,omitempty"`
	Value string `json:"Value"`
}

type K8SSubnet struct {
	CIDRBlock string `json:"CidrBlock"`
	SubnetID  string `json:"SubnetId"`
}

func parseK8S(raw map[string]any) (model.Asset, error) {
	original := &K8S{}
	meta := &model.ObjectMeta{}
	k8s := &model.K8S{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Cluster.ID
	meta.OriginalLabels = utils.Map(original.Cluster.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "kubernetes", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "kubernetes"
	meta.Name = original.Cluster.Name

	k8s.Status = utils.UnwrapOr("running", original.Cluster.Status.Phase == "Running", "stopped")
	k8s.EngineVersion = original.Cluster.KubernetesVersion
	k8s.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Cluster.ClusterConfig.VpcID)
	k8s.SubnetIDList = utils.Map(original.Subnets, func(e K8SSubnet) string { return utils.GenerateUID(meta.Provider, "subnet", e.SubnetID) })
	k8s.SGIDList = utils.Map(original.Cluster.ClusterConfig.SecurityGroupIDS, func(e string) string { return utils.GenerateUID(meta.Provider, "security-group", e) })

	if len(original.Cluster.ClusterConfig.APIServerEndpoints.PublicIP.Ipv4) > 0 {
		k8s.PublicEndpoint = fmt.Sprintf("https://%s:%d", original.Cluster.ClusterConfig.APIServerEndpoints.PublicIP.Ipv4, 6443)
	}
	if len(original.Cluster.ClusterConfig.APIServerEndpoints.PrivateIP.Ipv4) > 0 {
		k8s.PrivateEndpoint = fmt.Sprintf("https://%s:%d", original.Cluster.ClusterConfig.APIServerEndpoints.PrivateIP.Ipv4, 6443)
	}

	if len(original.Cluster.PodsConfig.FlannelConfig.PodCidrs) > 0 {
		k8s.PodCIDRs = append(k8s.PodCIDRs, original.Cluster.PodsConfig.FlannelConfig.PodCidrs...)
	}
	if len(original.Subnets) > 0 {
		k8s.PodCIDRs = append(k8s.PodCIDRs, utils.Map(original.Subnets, func(e K8SSubnet) string { return e.CIDRBlock })...)
	}
	k8s.ServiceCIDRs = original.Cluster.ServicesConfig.ServiceCidrsv4
	_, found := utils.Find(original.Cluster.LoggingConfig.LogSetups,
		func(logSetup LogSetup) bool { return logSetup.LogType == "Audit" && logSetup.Enabled },
	)
	k8s.AuditLogEnabled = utils.DataPointer(found)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, k8s)
	return asset, nil
}
