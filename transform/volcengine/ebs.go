package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type Disk struct {
	Disk DiskClass `json:"disk"`
}

type DiskClass struct {
	AutoSnapshotPolicyID   string `json:"AutoSnapshotPolicyId"`
	AutoSnapshotPolicyName string `json:"AutoSnapshotPolicyName"`
	BillingType            int64  `json:"BillingType"`
	CreatedAt              string `json:"CreatedAt"`
	DeleteWithInstance     bool   `json:"DeleteWithInstance"`
	Description            string `json:"Description"`
	DeviceName             string `json:"DeviceName"`
	ErrorDetail            string `json:"ErrorDetail"`
	ExpiredTime            string `json:"ExpiredTime"`
	ImageID                string `json:"ImageId"`
	InstanceID             string `json:"InstanceId"`
	Kind                   string `json:"Kind"`
	OverdueReclaimTime     string `json:"OverdueReclaimTime"`
	OverdueTime            string `json:"OverdueTime"`
	PayType                string `json:"PayType"`
	ProjectName            string `json:"ProjectName"`
	RenewType              int64  `json:"RenewType"`
	Size                   int64  `json:"Size"`
	SnapshotCount          int64  `json:"SnapshotCount"`
	SourceSnapshotID       string `json:"SourceSnapshotId"`
	Status                 string `json:"Status"`
	Tags                   []Tag  `json:"Tags"`
	TradeStatus            int64  `json:"TradeStatus"`
	UpdatedAt              string `json:"UpdatedAt"`
	VolumeID               string `json:"VolumeId"`
	VolumeName             string `json:"VolumeName"`
	VolumeType             string `json:"VolumeType"`
	ZoneID                 string `json:"ZoneId"`
}

type Attachment struct {
	AttachedTime string `json:"AttachedTime"`
	Device       string `json:"Device"`
	InstanceID   string `json:"InstanceId"`
}

func parseVolume(raw map[string]any) model.Asset {
	original := &Disk{}
	meta := &model.ObjectMeta{}
	ebs := &model.EBS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Disk.VolumeID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Disk.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "ebs", original.Disk.VolumeID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ebs"
	meta.Name = original.Disk.VolumeName
	meta.Description = original.Disk.Description

	ebs.Class = "volume"
	ebs.Status = utils.UnwrapOr("inuse", original.Disk.Status == "attached", "available")
	ebs.AutoSnapshotEnabled = utils.DataPointer(original.Disk.AutoSnapshotPolicyID != "")
	ebs.ImageId = utils.GenerateUID(ProviderID, "ebs", original.Disk.ImageID)
	ebs.ECSIDs = []string{utils.GenerateUID(ProviderID, "ecs", original.Disk.InstanceID)}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ebs)
	return asset
}

type Image struct {
	Image ImageClass `json:"image"`
}

type ImageClass struct {
	Architecture       string        `json:"Architecture"`
	BootMode           string        `json:"BootMode"`
	CreatedAt          string        `json:"CreatedAt"`
	Description        string        `json:"Description"`
	DetectionResults   interface{}   `json:"DetectionResults"`
	ImageID            string        `json:"ImageId"`
	ImageName          string        `json:"ImageName"`
	ImageOwnerID       string        `json:"ImageOwnerId"`
	IsLTS              bool          `json:"IsLTS"`
	IsSupportCloudInit bool          `json:"IsSupportCloudInit"`
	OSName             string        `json:"OsName"`
	OSType             string        `json:"OsType"`
	Platform           string        `json:"Platform"`
	PlatformVersion    string        `json:"PlatformVersion"`
	ProjectName        string        `json:"ProjectName"`
	ShareStatus        string        `json:"ShareStatus"`
	Size               int64         `json:"Size"`
	Snapshots          []interface{} `json:"Snapshots"`
	Status             string        `json:"Status"`
	Tags               []Tag         `json:"Tags"`
	UpdatedAt          string        `json:"UpdatedAt"`
	VirtualSize        int64         `json:"VirtualSize"`
	Visibility         string        `json:"Visibility"`
}

func parseImage(raw map[string]any) model.Asset {
	original := &Image{}
	meta := &model.ObjectMeta{}
	ebs := &model.EBS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Image.ImageID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Image.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "ebs", original.Image.ImageID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ebs"
	meta.Name = original.Image.ImageName
	meta.Description = original.Image.Description

	ebs.Class = "image"
	ebs.Status = utils.UnwrapOr("available", original.Image.Status == "available", "unavailable")

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ebs)
	return asset
}

type Snapshot struct {
	Snapshot SnapshotClass `json:"snapshot"`
}

type SnapshotClass struct {
	CreationTime    string      `json:"CreationTime"`
	Description     string      `json:"Description"`
	ImageID         string      `json:"ImageId"`
	Progress        int64       `json:"Progress"`
	ProjectName     string      `json:"ProjectName"`
	RetentionDays   interface{} `json:"RetentionDays"`
	SnapshotGroupID string      `json:"SnapshotGroupId"`
	SnapshotID      string      `json:"SnapshotId"`
	SnapshotName    string      `json:"SnapshotName"`
	SnapshotType    string      `json:"SnapshotType"`
	Status          string      `json:"Status"`
	Tags            []Tag       `json:"Tags"`
	VolumeID        string      `json:"VolumeId"`
	VolumeKind      string      `json:"VolumeKind"`
	VolumeName      string      `json:"VolumeName"`
	VolumeSize      int64       `json:"VolumeSize"`
	VolumeStatus    string      `json:"VolumeStatus"`
	VolumeType      string      `json:"VolumeType"`
	ZoneID          string      `json:"ZoneId"`
}

func parseSnapshot(raw map[string]any) model.Asset {
	original := &Snapshot{}
	meta := &model.ObjectMeta{}
	ebs := &model.EBS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Snapshot.SnapshotID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Snapshot.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "ebs", original.Snapshot.SnapshotID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ebs"
	meta.Name = original.Snapshot.SnapshotName
	meta.Description = original.Snapshot.Description

	ebs.Class = "snapshot"
	ebs.Status = utils.UnwrapOr("available", original.Snapshot.Status == "available", "unavailable")
	ebs.VolumeId = utils.GenerateUID(ProviderID, "ebs", original.Snapshot.VolumeID)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ebs)
	return asset
}
