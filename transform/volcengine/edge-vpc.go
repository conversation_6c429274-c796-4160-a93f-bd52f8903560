package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type EdgeVpc struct {
	Vpc EdgeVpcClass `json:"vpc"`
}

type EdgeVpcClass struct {
	AccountIdentity    int64                 `json:"account_identity"`
	UserIdentity       int64                 `json:"user_identity"`
	VpcIdentity        string                `json:"vpc_identity"`
	VpcName            string                `json:"vpc_name"`
	VpcNS              string                `json:"vpc_ns"`
	ClusterVpcID       int64                 `json:"cluster_vpc_id"`
	Cluster            EdgeCluster           `json:"cluster"`
	Status             string                `json:"status"`
	IsDefault          bool                  `json:"is_default"`
	Desc               string                `json:"desc"`
	SubNets            []EdgeSubNet          `json:"sub_nets"`
	ResourceStatistic  EdgeResourceStatistic `json:"resource_statistic"`
	LocalAccountVgwNum int64                 `json:"local_account_vgw_num"`
	CrossAccountVgwNum int64                 `json:"cross_account_vgw_num"`
	Is<PERSON>ust<PERSON>           bool                  `json:"is_custom"`
	Segment            string                `json:"segment"`
	CreateTime         int64                 `json:"create_time"`
	UpdateTime         int64                 `json:"update_time"`
}

type EdgeResourceStatistic struct {
	VeenInstanceCount           int64 `json:"veen_instance_count"`
	VeewSgInstanceCount         int64 `json:"veew_sg_instance_count"`
	VeewLBInstanceCount         int64 `json:"veew_lb_instance_count"`
	VeewRouteTableInstanceCount int64 `json:"veew_route_table_instance_count"`
	VeewNatgwInstanceCount      int64 `json:"veew_natgw_instance_count"`
}

type EdgeSubNet struct {
	AccountIdentity int64  `json:"account_identity"`
	UserIdentity    int64  `json:"user_identity"`
	SubnetIdentity  string `json:"subnet_identity"`
	CIDRIP          string `json:"cidr_ip"`
	CIDRMask        int64  `json:"cidr_mask"`
	Name            string `json:"name"`
	Desc            string `json:"desc"`
	Status          string `json:"status"`
	CreateTime      int64  `json:"create_time"`
	UpdateTime      int64  `json:"update_time"`
}

func parseEdgeVPC(raw map[string]any) model.Asset {
	original := &EdgeVpc{}
	meta := &model.ObjectMeta{}
	vpc := &model.VPC{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Vpc.VpcIdentity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "vpc", original.Vpc.VpcIdentity)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "vpc"
	meta.Name = original.Vpc.VpcName
	meta.Description = original.Vpc.Desc

	vpc.CIDR = original.Vpc.Segment
	vpc.IsDefault = original.Vpc.IsDefault
	vpc.SubnetIDList = utils.Map(original.Vpc.SubNets, func(e EdgeSubNet) string { return utils.GenerateUID(meta.Provider, "subnet", e.SubnetIdentity) })

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, vpc)
	return asset
}
