package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"

	"github.com/samber/lo"
)

type TLS struct {
	Project Project          `json:"project"`
	Topic   Topic            `json:"topic"`
	Rules   []map[string]any `json:"rules"`
}

type Project struct {
	ProjectID       string `json:"ProjectId"`
	ProjectName     string `json:"ProjectName"`
	Description     string `json:"Description"`
	CreateTimestamp string `json:"CreateTime"`
	TopicCount      int64  `json:"TopicCount"`
	InnerNetDomain  string `json:"InnerNetDomain"`
	IamProjectName  string `json:"IamProjectName"`
	Tags            []Tag  `json:"Tags"`
}

type Topic struct {
	TopicName      string `json:"TopicName"`
	ProjectID      string `json:"ProjectId"`
	TopicID        string `json:"TopicId"`
	TTL            int64  `json:"Ttl"`
	CreateTime     string `json:"CreateTime"`
	ModifyTime     string `json:"ModifyTime"`
	ShardCount     int64  `json:"ShardCount"`
	Description    string `json:"Description"`
	MaxSplitShard  int64  `json:"MaxSplitShard"`
	AutoSplit      bool   `json:"AutoSplit"`
	EnableTracking bool   `json:"EnableTracking"`
	TimeKey        string `json:"TimeKey"`
	TimeFormat     string `json:"TimeFormat"`
	Tags           []Tag  `json:"Tags"`
	LogPublicIP    bool   `json:"LogPublicIP"`
	EnableHotTTL   bool   `json:"EnableHotTtl"`
	HotTTL         int64  `json:"HotTtl"`
	ColdTTL        int64  `json:"ColdTtl"`
	ArchiveTTL     int64  `json:"ArchiveTtl"`
}

func parseTLS(raw map[string]any) model.Asset {
	original := &TLS{}
	meta := &model.ObjectMeta{}
	tls := &model.LogService{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Topic.TopicID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = lo.Map(original.Topic.Tags, func(e Tag, _ int) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "log-service", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "log-service"
	meta.Name = original.Topic.TopicName

	tls.Project = original.Project.ProjectName
	tls.LogStoreName = original.Topic.TopicName
	tls.LogStoreConfigs = original.Rules

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, tls)
	return asset
}
