package role

import "AssetStandardizer/graph"

var roleSchema = graph.NodeSchema{
	Label: "IAMRole",
	Properties: map[string]graph.PropertyRef{
		"uid":                   {Name: "uid"},
		"last_updated":          {Name: "last_updated", SetInKwargs: true},
		"provider":              {Name: "provider"},
		"original_id":           {Name: "original_id"},
		"transformed_object":    {Name: "transformed_object"},
		"region":                {Name: "region"},
		"last_seen":             {Name: "last_seen"},
		"description":           {Name: "description"},
		"kind":                  {Name: "kind"},
		"name":                  {Name: "name"},
		"max_session_duration":  {Name: "max_session_duration"},
		"assume_role_principal": {Name: "assume_role_principal"},
	},
}

var policySchema = graph.NodeSchema{
	Label: "IAMPolicy",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "IAMRole",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
