package role

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "role"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ram_role_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["role_list"] = append(resourceData["role_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, roleSchema, resourceData["role_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, resourceData["policy_list"], map[string]any{"last_updated": "test"})
}

type Role struct {
	Policies []RolePolicy `json:"policies"`
	Role     RoleClass    `json:"role"`
}

type RolePolicy struct {
	AttachDate     string `json:"AttachDate"`
	DefaultVersion string `json:"DefaultVersion"`
	Description    string `json:"Description"`
	PolicyName     string `json:"PolicyName"`
	PolicyType     string `json:"PolicyType"`
}

type RoleClass struct {
	Arn                      string `json:"Arn"`
	CreateDate               string `json:"CreateDate"`
	Description              string `json:"Description"`
	MaxSessionDuration       int    `json:"MaxSessionDuration"`
	RoleID                   string `json:"RoleId"`
	RoleName                 string `json:"RoleName"`
	UpdateDate               string `json:"UpdateDate"`
	AssumeRolePolicyDocument string `json:"AssumeRolePolicyDocument"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.RoleGraph, error) {
	original := &Role{}
	resource := &model.RoleGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Role.RoleID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "role", original.Role.RoleName)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "role"
	resource.Name = original.Role.RoleName
	resource.Description = original.Role.Description

	resource.MaxSessionDuration = original.Role.MaxSessionDuration
	resource.Policy = lo.Map(original.Policies, func(policy RolePolicy, _ int) *model.PolicyGraph {
		return &model.PolicyGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "policy", policy.PolicyName),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
