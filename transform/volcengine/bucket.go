package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"encoding/json"
	"slices"
	"strings"
	"time"
)

type Bucket struct {
	Bucket     BucketClass `json:"bucket"`
	Cors       []Cor       `json:"cors"`
	Encryption Encryption  `json:"encryption"`
	Logging    interface{} `json:"logging"`
	ObjACL     []ObjACL    `json:"objAcl"`
	Policy     string      `json:"policy"`
	ACL        []ACL       `json:"acl,omitempty"`
}

type BucketClass struct {
	CreationDate     string `json:"CreationDate"`
	Name             string `json:"Name"`
	Location         string `json:"Location"`
	ExtranetEndpoint string `json:"ExtranetEndpoint"`
	IntranetEndpoint string `json:"IntranetEndpoint"`
	ProjectName      string `json:"ProjectName"`
}

type Cor struct {
	AllowedOrigins []string `json:"AllowedOrigins"`
	AllowedMethods []string `json:"AllowedMethods"`
	AllowedHeaders []string `json:"AllowedHeaders"`
	MaxAgeSeconds  int64    `json:"MaxAgeSeconds"`
}

type Encryption struct {
	ApplyServerSideEncryptionByDefault ApplyServerSideEncryptionByDefault `json:"ApplyServerSideEncryptionByDefault"`
}

type ApplyServerSideEncryptionByDefault struct {
	SSEAlgorithm string `json:"SSEAlgorithm"`
}

type ObjACL struct {
	ACL    []ACL  `json:"acl"`
	Object string `json:"object"`
}

type ACL struct {
	Grantee    Grantee `json:"Grantee"`
	Permission string  `json:"Permission"`
}

type Grantee struct {
	ID     string `json:"ID"`
	Type   string `json:"Type"`
	Canned string `json:"Canned"`
}

func parseBucket(raw map[string]any) model.Asset {
	original := &Bucket{}
	meta := &model.ObjectMeta{}
	bucket := &model.Bucket{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.Region = regionIdToName[original.Bucket.Location]
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalID = original.Bucket.Name
	meta.UID = utils.GenerateUID(meta.Provider, "bucket", meta.OriginalID)
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "bucket"
	meta.Name = original.Bucket.Name

	bucket.PrivateEndpoint = original.Bucket.IntranetEndpoint
	bucket.PublicEndpoint = original.Bucket.ExtranetEndpoint

	parsedDocument := PolicyDocument{}
	json.Unmarshal([]byte(original.Policy), &parsedDocument)
	bucket.Policy = utils.Map(parsedDocument.Statement, func(statement Statement) model.PolicyDocument {
		pd := model.PolicyDocument{}
		pd.Effect = strings.ToLower(statement.Effect)

		pd.Action = statement.Action
		pd.Resource = statement.Resource
		pd.Principal = utils.ToJsonStr(statement.Principal)
		pd.Condition = utils.ToJsonStr(statement.Condition)
		return pd
	})

	bucket.ACL = parseBucketACL(original.ACL)
	bucket.CORS = utils.Map(original.Cors, func(rule Cor) model.BucketCORSRule {
		return model.BucketCORSRule{
			AllowedOrigins: rule.AllowedOrigins,
			AllowedMethods: rule.AllowedMethods,
			AllowedHeaders: rule.AllowedHeaders,
			MaxAgeSeconds:  int(rule.MaxAgeSeconds),
		}
	})
	bucket.Encryption = model.BucketEncryption{
		Enabled:   original.Encryption.ApplyServerSideEncryptionByDefault.SSEAlgorithm != "",
		Algorithm: original.Encryption.ApplyServerSideEncryptionByDefault.SSEAlgorithm,
	}
	bucket.Logging = model.BucketLogging{
		Enabled: original.Logging != nil,
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, bucket)
	return asset
}

func parseBucketACL(acls []ACL) []string {
	acl := []string{}
	_, found := utils.Find(acls, func(item ACL) bool {
		return slices.Contains([]string{"FULL_CONTROL", "WRITE", "WRITE_CAP"}, item.Permission) &&
			item.Grantee.Type == "Group" && item.Grantee.Canned == "ALLUsers"
	})
	if found {
		acl = append(acl, "public-write")
	}
	_, found = utils.Find(acls, func(item ACL) bool {
		return slices.Contains([]string{"FULL_CONTROL", "READ", "READ_CAP"}, item.Permission) &&
			item.Grantee.Type == "Group" && item.Grantee.Canned == "ALLUsers"
	})
	if found {
		acl = append(acl, "public-read")
	}
	if len(acl) == 0 {
		acl = append(acl, "private")
	}
	return acl
}
