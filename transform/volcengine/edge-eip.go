package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type EdgeEip struct {
	Eip EdgeEipClass `json:"eip"`
}

type EdgeEipClass struct {
	AccountIdentity int64          `json:"account_identity"`
	UserIdentity    int64          `json:"user_identity"`
	EipIdentity     string         `json:"eip_identity"`
	EipName         string         `json:"eip_name"`
	Cluster         Cluster        `json:"cluster"`
	EipType         string         `json:"eip_type"`
	EipAddr         string         `json:"eip_addr"`
	BandwidthPeak   int64          `json:"bandwidth_peak"`
	ISP             string         `json:"isp"`
	NetworkType     string         `json:"network_type"`
	BinderResource  BinderResource `json:"binder_resource"`
	Status          string         `json:"status"`
	Desc            string         `json:"desc"`
	BillingConfig   BillingConfig  `json:"billing_config"`
	ClusterBwpID    int64          `json:"cluster_bwp_id"`
	Project         string         `json:"project"`
	CreateTime      int64          `json:"create_time"`
	UpdateTime      int64          `json:"update_time"`
}

type BillingConfig struct {
	IPBillingMethod        string `json:"ip_billing_method"`
	BandwidthBillingMethod string `json:"bandwidth_billing_method"`
}

type BinderResource struct {
	ResourceType           string   `json:"resource_type"`
	ResourceIdentity       string   `json:"resource_identity"`
	ResourceName           string   `json:"resource_name"`
	ResourceVpcIdentity    string   `json:"resource_vpc_identity"`
	ResourceVpcName        string   `json:"resource_vpc_name"`
	ResourceVpcSubnetCidrs []string `json:"resource_vpc_subnet_cidrs"`
}

type EdgeCluster struct {
	ClusterName string `json:"cluster_name"`
	Country     string `json:"country"`
	Region      string `json:"region"`
	Province    string `json:"province"`
	City        string `json:"city"`
	ISP         string `json:"isp"`
	Level       string `json:"level"`
	Alias       string `json:"alias"`
}

func parseEdgeEip(raw map[string]any) model.Asset {
	meta := &model.ObjectMeta{}
	eip := &model.Eip{}
	original := &EdgeEip{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Eip.EipIdentity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "eip", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Eip.Desc
	meta.Kind = "eip"
	meta.Name = original.Eip.EipName

	eip.IP = original.Eip.EipAddr
	eip.Status = lo.Ternary(strings.EqualFold(original.Eip.Status, "bound"), "binded", "available")
	eip.Bandwidth = original.Eip.BandwidthPeak
	eip.ISP = original.Eip.ISP
	switch original.Eip.BinderResource.ResourceType {
	case "lb", "lb7":
		eip.LBID = utils.GenerateUID(ProviderID, "edge-lb", original.Eip.BinderResource.ResourceIdentity)
	case "veen":
		eip.ECSID = utils.GenerateUID(ProviderID, "edge-ecs", original.Eip.BinderResource.ResourceIdentity)
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, eip)
	return asset
}
