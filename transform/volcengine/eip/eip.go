package eip

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eip"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_eip_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eip_list"] = append(resourceData["eip_list"], utils.GenParamsFromStruct(resource))
		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)
		resourceData["nat_list"] = append(resourceData["nat_list"],
			utils.GenParamsFromStructSlice(resource.NAT)...,
		)
		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}

	return resourceData, nil
}

func updateResources(eipData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eipSchema, eipData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniEipSchema, eipData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, natEipSchema, eipData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEipSchema, eipData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbEipSchema, eipData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEipSchema, eipData["label_list"], map[string]any{"last_updated": "test"})

}

type Eip struct {
	Eip EipClass `json:"eip"`
}

type EipClass struct {
	AllocationID            string        `json:"AllocationId"`
	AllocationTime          string        `json:"AllocationTime"`
	Bandwidth               int64         `json:"Bandwidth"`
	BandwidthPackageID      interface{}   `json:"BandwidthPackageId"`
	BillingType             int64         `json:"BillingType"`
	BusinessStatus          string        `json:"BusinessStatus"`
	DeletedTime             string        `json:"DeletedTime"`
	Description             string        `json:"Description"`
	EipAddress              string        `json:"EipAddress"`
	ExpiredTime             string        `json:"ExpiredTime"`
	ISP                     string        `json:"ISP"`
	InstanceID              string        `json:"InstanceId"`
	InstanceType            string        `json:"InstanceType"`
	LockReason              string        `json:"LockReason"`
	Name                    string        `json:"Name"`
	OverdueTime             string        `json:"OverdueTime"`
	ProjectName             string        `json:"ProjectName"`
	ReleaseWithInstance     bool          `json:"ReleaseWithInstance"`
	SecurityProtectionTypes []interface{} `json:"SecurityProtectionTypes"`
	Status                  string        `json:"Status"`
	Tags                    []Tag         `json:"Tags"`
	UpdatedAt               string        `json:"UpdatedAt"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.EipGraph, error) {
	original := &Eip{}
	resource := &model.EipGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Eip.AllocationID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eip", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Eip.Description
	resource.Kind = "eip"
	resource.Name = original.Eip.Name
	resource.OriginalLabels = lo.Map(original.Eip.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.IP = original.Eip.EipAddress
	resource.Status = lo.Ternary(strings.EqualFold(original.Eip.Status, "InUse"), "binded", "available")
	resource.Bandwidth = int(original.Eip.Bandwidth)
	resource.ISP = original.Eip.ISP
	switch original.Eip.InstanceType {
	case "NetworkInterface":
		resource.ENI = append(resource.ENI, &model.ENIGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eni", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "Nat":
		resource.NAT = append(resource.NAT, &model.NATGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "nat", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "EcsInstance":
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "ClbInstance", "Albinstance":
		resource.LB = append(resource.LB, &model.LBGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "lb", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
