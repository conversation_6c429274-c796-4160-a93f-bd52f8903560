package bucket

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	"AssetStandardizer/transform/utils"
	provider_utils "AssetStandardizer/transform/volcengine/utils"
	"context"
	"slices"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "bucket"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "oss_bucket_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["bucket_list"] = append(resourceData["bucket_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)

		resourceData["cors_list"] = append(resourceData["cors_list"],
			utils.GenParamsFromStructSlice(resource.CORS)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, bucketSchema, userData["bucket_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, corsSchema, userData["cors_list"], map[string]any{"last_updated": "test"})
}

type Bucket struct {
	Bucket     BucketClass `json:"bucket"`
	Cors       []Cor       `json:"cors"`
	Encryption Encryption  `json:"encryption"`
	Logging    interface{} `json:"logging"`
	ObjACL     []ObjACL    `json:"objAcl"`
	Policy     string      `json:"policy"`
	ACL        []ACL       `json:"acl,omitempty"`
}

type BucketClass struct {
	CreationDate     string `json:"CreationDate"`
	Name             string `json:"Name"`
	Location         string `json:"Location"`
	ExtranetEndpoint string `json:"ExtranetEndpoint"`
	IntranetEndpoint string `json:"IntranetEndpoint"`
	ProjectName      string `json:"ProjectName"`
}

type Cor struct {
	AllowedOrigins []string `json:"AllowedOrigins"`
	AllowedMethods []string `json:"AllowedMethods"`
	AllowedHeaders []string `json:"AllowedHeaders"`
	MaxAgeSeconds  int64    `json:"MaxAgeSeconds"`
}

type Encryption struct {
	ApplyServerSideEncryptionByDefault ApplyServerSideEncryptionByDefault `json:"ApplyServerSideEncryptionByDefault"`
}

type ApplyServerSideEncryptionByDefault struct {
	SSEAlgorithm string `json:"SSEAlgorithm"`
}

type ObjACL struct {
	ACL    []ACL  `json:"acl"`
	Object string `json:"object"`
}

type ACL struct {
	Grantee    Grantee `json:"Grantee"`
	Permission string  `json:"Permission"`
}

type Grantee struct {
	ID     string `json:"ID"`
	Type   string `json:"Type"`
	Canned string `json:"Canned"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.BucketGraph, error) {
	original := &Bucket{}
	resource := &model.BucketGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Bucket.Name
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "bucket", original.Bucket.Name)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "bucket"
	resource.Name = original.Bucket.Name

	resource.PrivateEndpoint = original.Bucket.IntranetEndpoint
	resource.PublicEndpoint = original.Bucket.ExtranetEndpoint
	resource.Policy = provider_utils.ParsePolicyDocument(original.Policy, resource.UID)
	resource.ACL = parseBucketACL(original.ACL)

	resource.CORS = lo.Map(original.Cors, func(rule Cor, _ int) *model.BucketCORSRuleGraph {
		content, _ := sonic.MarshalString(rule)
		return &model.BucketCORSRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, "bucket_cors", content),
				TargetUID: resource.UID,
			},
			AllowedOrigins: rule.AllowedOrigins,
			AllowedMethods: rule.AllowedMethods,
			AllowedHeaders: rule.AllowedHeaders,
			MaxAgeSeconds:  int(rule.MaxAgeSeconds),
		}
	})
	resource.EncryptionEnabled = original.Encryption.ApplyServerSideEncryptionByDefault.SSEAlgorithm != ""
	resource.EncryptionAlgorithm = original.Encryption.ApplyServerSideEncryptionByDefault.SSEAlgorithm
	resource.LoggingEnabled = original.Logging != nil

	return resource, nil
}

func parseBucketACL(acls []ACL) []string {
	acl := []string{}
	_, found := lo.Find(acls, func(item ACL) bool {
		return slices.Contains([]string{"FULL_CONTROL", "WRITE", "WRITE_CAP"}, item.Permission) &&
			item.Grantee.Type == "Group" && item.Grantee.Canned == "ALLUsers"
	})
	if found {
		acl = append(acl, "public-write")
	}
	_, found = lo.Find(acls, func(item ACL) bool {
		return slices.Contains([]string{"FULL_CONTROL", "READ", "READ_CAP"}, item.Permission) &&
			item.Grantee.Type == "Group" && item.Grantee.Canned == "ALLUsers"
	})
	if found {
		acl = append(acl, "public-read")
	}
	if len(acl) == 0 {
		acl = append(acl, "private")
	}
	return acl
}
