package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type ECS struct {
	Enis     []Eni       `json:"enis"`
	Instance EcsInstance `json:"instance"`
}

type Eni struct {
	AccountID            string              `json:"AccountId"`
	AssociatedElasticIP  AssociatedElasticIP `json:"AssociatedElasticIp"`
	CreatedAt            string              `json:"CreatedAt"`
	Description          string              `json:"Description"`
	DeviceID             string              `json:"DeviceId"`
	IPv6Sets             []string            `json:"IPv6Sets"`
	MACAddress           string              `json:"MacAddress"`
	NetworkInterfaceID   string              `json:"NetworkInterfaceId"`
	NetworkInterfaceName string              `json:"NetworkInterfaceName"`
	PortSecurityEnabled  bool                `json:"PortSecurityEnabled"`
	PrimaryIPAddress     string              `json:"PrimaryIpAddress"`
	PrivateIPSets        PrivateIPSets       `json:"PrivateIpSets"`
	ProjectName          string              `json:"ProjectName"`
	SecurityGroupIDS     []string            `json:"SecurityGroupIds"`
	ServiceManaged       bool                `json:"ServiceManaged"`
	Status               string              `json:"Status"`
	SubnetID             string              `json:"SubnetId"`
	Tags                 []Tag               `json:"Tags"`
	Type                 string              `json:"Type"`
	UpdatedAt            string              `json:"UpdatedAt"`
	VpcID                string              `json:"VpcId"`
	VpcName              string              `json:"VpcName"`
	ZoneID               string              `json:"ZoneId"`
}

type AssociatedElasticIP struct {
	AllocationID        string `json:"AllocationId"`
	EipAddress          string `json:"EipAddress"`
	ReleaseWithInstance bool   `json:"ReleaseWithInstance"`
}

type PrivateIPSets struct {
	PrivateIPSet []PrivateIPSet `json:"PrivateIpSet"`
}

type PrivateIPSet struct {
	AssociatedElasticIP AssociatedElasticIP `json:"AssociatedElasticIp"`
	Primary             bool                `json:"Primary"`
	PrivateIPAddress    string              `json:"PrivateIpAddress"`
}

type EcsInstance struct {
	CPUOptions                   CPUOptions         `json:"CpuOptions"`
	Cpus                         int64              `json:"Cpus"`
	CreatedAt                    string             `json:"CreatedAt"`
	DeploymentSetGroupNumber     int64              `json:"DeploymentSetGroupNumber"`
	DeploymentSetID              string             `json:"DeploymentSetId"`
	Description                  string             `json:"Description"`
	EipAddress                   interface{}        `json:"EipAddress"`
	ElasticScheduledInstanceType string             `json:"ElasticScheduledInstanceType"`
	ExpiredAt                    string             `json:"ExpiredAt"`
	HostName                     interface{}        `json:"HostName"`
	Hostname                     string             `json:"Hostname"`
	HPCClusterID                 string             `json:"HpcClusterId"`
	ImageID                      string             `json:"ImageId"`
	InstanceChargeType           string             `json:"InstanceChargeType"`
	InstanceID                   string             `json:"InstanceId"`
	InstanceName                 string             `json:"InstanceName"`
	InstanceTypeID               string             `json:"InstanceTypeId"`
	KeyPairID                    string             `json:"KeyPairId"`
	KeyPairName                  string             `json:"KeyPairName"`
	LocalVolumes                 []LocalVolume      `json:"LocalVolumes"`
	MemorySize                   int64              `json:"MemorySize"`
	NetworkInterfaces            []NetworkInterface `json:"NetworkInterfaces"`
	OSName                       string             `json:"OsName"`
	OSType                       string             `json:"OsType"`
	Placement                    Placement          `json:"Placement"`
	ProjectName                  string             `json:"ProjectName"`
	RdmaIPAddresses              []string           `json:"RdmaIpAddresses"`
	ScheduledInstanceID          string             `json:"ScheduledInstanceId"`
	SpotPriceLimit               int64              `json:"SpotPriceLimit"`
	SpotStrategy                 string             `json:"SpotStrategy"`
	Status                       string             `json:"Status"`
	StoppedMode                  string             `json:"StoppedMode"`
	Tags                         []Tag              `json:"Tags"`
	UpdatedAt                    string             `json:"UpdatedAt"`
	UUID                         string             `json:"Uuid"`
	VpcID                        string             `json:"VpcId"`
	ZoneID                       string             `json:"ZoneId"`
}
type CPUOptions struct {
	CoreCount      int64 `json:"CoreCount"`
	ThreadsPerCore int64 `json:"ThreadsPerCore"`
}

type LocalVolume struct {
	Count      int64  `json:"Count"`
	Size       int64  `json:"Size"`
	VolumeType string `json:"VolumeType"`
}

type NetworkInterface struct {
	Ipv6Addresses      []interface{} `json:"Ipv6Addresses"`
	MACAddress         string        `json:"MacAddress"`
	NetworkInterfaceID string        `json:"NetworkInterfaceId"`
	PrimaryIPAddress   string        `json:"PrimaryIpAddress"`
	SubnetID           string        `json:"SubnetId"`
	Type               string        `json:"Type"`
	VpcID              string        `json:"VpcId"`
}

type Placement struct {
	Affinity               string `json:"Affinity"`
	DedicatedHostClusterID string `json:"DedicatedHostClusterId"`
	DedicatedHostID        string `json:"DedicatedHostId"`
	Tenancy                string `json:"Tenancy"`
}

type Tags struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func parseEcs(raw map[string]any) model.Asset {
	original := &ECS{}
	meta := &model.ObjectMeta{}
	ecs := &model.ECS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.Instance.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalID = original.Instance.InstanceID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "ecs", original.Instance.InstanceID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Instance.Description
	meta.Kind = "ecs"
	meta.Name = original.Instance.InstanceName

	ecs.Hostname = original.Instance.Hostname
	ecs.Class = "cvm"
	ecs.OSName = original.Instance.OSName
	osType := strings.ToLower(original.Instance.OSType)
	ecs.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")
	ecs.ENIIDList = utils.Map(original.Instance.NetworkInterfaces, func(e NetworkInterface) string { return utils.GenerateUID(ProviderID, "eni", e.NetworkInterfaceID) })
	primaryEni, found := utils.Find(original.Enis, func(e Eni) bool { return e.Type == "primary" })
	if found {
		primaryIpset, found := utils.Find(primaryEni.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet) bool { return e.Primary })
		if found {
			ecs.PrimaryPrivateIP = primaryIpset.PrivateIPAddress
			ecs.PrimaryPublicIP = primaryIpset.AssociatedElasticIP.EipAddress
		}
	}
	ecs.PrivateIPList = utils.Flatten(utils.Map(original.Enis, func(e Eni) []string {
		return utils.Map(e.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet) string { return e.PrivateIPAddress })
	}))
	ecs.PublicIPList = utils.Flatten(utils.Map(original.Enis, func(e Eni) []string {
		return utils.FilterMap(e.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet) (string, bool) {
			return e.AssociatedElasticIP.EipAddress, e.AssociatedElasticIP.EipAddress != ""
		})
	}))
	ecs.SGIDList = utils.Flatten(utils.Map(original.Enis, func(e Eni) []string {
		return utils.Map(e.SecurityGroupIDS, func(e string) string { return utils.GenerateUID(ProviderID, "security-group", e) })
	}))
	ecs.Spec = original.Instance.InstanceTypeID
	ecs.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Instance.VpcID)
	ecs.Status = strings.ToLower(original.Instance.Status)
	// not supported by volcengine
	ecs.DeleteProtection = utils.DataPointer(false)
	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ecs)
	return asset
}
