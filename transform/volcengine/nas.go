package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type NAS struct {
	MountPoints     []MountPoint     `json:"mountPoints"`
	NAS             NASClass         `json:"nas"`
	PermissionRules []PermissionRule `json:"permissionRules"`
}

type MountPoint struct {
	CreateTime      string          `json:"CreateTime"`
	Domain          string          `json:"Domain"`
	IP              string          `json:"Ip"`
	MountPointID    string          `json:"MountPointId"`
	MountPointName  string          `json:"MountPointName"`
	PermissionGroup PermissionGroup `json:"PermissionGroup"`
	Status          string          `json:"Status"`
	SubnetID        string          `json:"SubnetId"`
	SubnetName      string          `json:"SubnetName"`
	UpdateTime      string          `json:"UpdateTime"`
	VpcID           string          `json:"VpcId"`
	VpcName         string          `json:"VpcName"`
}

type PermissionGroup struct {
	PermissionGroupID   string `json:"PermissionGroupId"`
	PermissionGroupName string `json:"PermissionGroupName"`
}

type NASClass struct {
	CachePerformance CachePerformance `json:"CachePerformance"`
	Capacity         Capacity         `json:"Capacity"`
	ChargeType       string           `json:"ChargeType"`
	CreateTime       string           `json:"CreateTime"`
	Description      string           `json:"Description"`
	FileSystemID     string           `json:"FileSystemId"`
	FileSystemName   string           `json:"FileSystemName"`
	FileSystemType   string           `json:"FileSystemType"`
	ProjectName      string           `json:"ProjectName"`
	ProtocolType     string           `json:"ProtocolType"`
	RegionID         string           `json:"RegionId"`
	Status           string           `json:"Status"`
	StorageType      string           `json:"StorageType"`
	UpdateTime       string           `json:"UpdateTime"`
	ZoneID           string           `json:"ZoneId"`
	ZoneName         string           `json:"ZoneName"`
}

type CachePerformance struct {
	BaseBandwidth     int64 `json:"BaseBandwidth"`
	CacheBandwidth    int64 `json:"CacheBandwidth"`
	DataFlowBandwidth int64 `json:"DataFlowBandwidth"`
}

type PermissionRule struct {
	CIDRIP           string `json:"CidrIp"`
	PermissionRuleID string `json:"PermissionRuleId"`
	RwMode           string `json:"RwMode"`
	UserMode         string `json:"UserMode"`
}

func parseNas(raw map[string]any) model.Asset {
	original := &NAS{}
	meta := &model.ObjectMeta{}
	nas := &model.NAS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.NAS.FileSystemID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "nas", original.NAS.FileSystemID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "nas"
	meta.Name = original.NAS.FileSystemName

	nas.Status = lo.Ternary(strings.EqualFold(original.NAS.Status, "running"), "on", "off")
	nas.IPs = lo.Map(original.MountPoints, func(e MountPoint, _ int) string { return e.IP })
	nas.Domains = lo.Map(original.MountPoints, func(e MountPoint, _ int) string { return e.Domain })
	nas.VpcIdList = lo.Map(original.MountPoints, func(e MountPoint, _ int) string { return utils.GenerateUID(ProviderID, "vpc", e.VpcID) })
	nas.SubnetIdList = lo.Map(original.MountPoints, func(e MountPoint, _ int) string { return utils.GenerateUID(ProviderID, "subnet", e.SubnetID) })
	nas.Class = strings.ToLower(original.NAS.ProtocolType)
	nas.ACLRules = lo.Map(original.PermissionRules, func(e PermissionRule, _ int) model.NasACLRule {
		return model.NasACLRule{
			AclID:      e.PermissionRuleID,
			SourceCidr: formatCIDR(e.CIDRIP),
			RWAccess:   lo.Ternary(strings.EqualFold(e.RwMode, "RW"), "RDWR", "RDONLY"),
			UserAccess: lo.Ternary(strings.EqualFold(e.UserMode, "no_root_squash"), "no_squash", strings.ToLower(e.UserMode)),
		}
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, nas)
	return asset
}
