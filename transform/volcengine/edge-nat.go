package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

type EdgeNAT struct {
	NatGateway EdgeNatGateway `json:"nat_gateway"`
	DnatRules  []EdgeDnatRule `json:"dnat_rules"`
	SnatRules  []EdgeSnatRule `json:"snat_rules"`
}

type EdgeNatGateway struct {
	AccountIdentity int64        `json:"account_identity"`
	UserIdentity    int64        `json:"user_identity"`
	NatgwIdentity   string       `json:"natgw_identity"`
	NatgwName       string       `json:"natgw_name"`
	Status          string       `json:"status"`
	Cluster         EdgeCluster  `json:"cluster"`
	Vpc             EdgeNatVpc   `json:"vpc"`
	EipList         []EdgeNatEip `json:"eip_list"`
	BpsLimit        string       `json:"bps_limit"`
	BillingMethod   string       `json:"billing_method"`
	ConnectLimit    string       `json:"connect_limit"`
	NewConnectLimit string       `json:"new_connect_limit"`
	Project         string       `json:"project"`
	IsOverdue       bool         `json:"is_overdue"`
	StartAt         int64        `json:"start_at"`
	CreateTime      int64        `json:"create_time"`
	UpdateTime      int64        `json:"update_time"`
}

type EdgeNatEip struct {
	EipIdentity string `json:"eip_identity"`
	EipAddr     string `json:"eip_addr"`
}

type EdgeNatVpc struct {
	VpcIdentity string `json:"vpc_identity"`
	VpcName     string `json:"vpc_name"`
}

type EdgeDnatRule struct {
	NatgwIdentity    string     `json:"natgw_identity"`
	DnatRuleIdentity string     `json:"dnat_rule_identity"`
	DnatRuleName     string     `json:"dnat_rule_name"`
	Status           string     `json:"status"`
	PortConfig       string     `json:"port_config"`
	Protocol         string     `json:"protocol"`
	Eip              EdgeNatEip `json:"eip"`
	PrivateIP        string     `json:"private_ip"`
	PublicPort       int        `json:"public_port"`
	PrivatePort      int        `json:"private_port"`
	CreateTime       int64      `json:"create_time"`
	UpdateTime       int64      `json:"update_time"`
}

type EdgeSnatRule struct {
	NatgwIdentity    string       `json:"natgw_identity"`
	SnatRuleIdentity string       `json:"snat_rule_identity"`
	SnatRuleName     string       `json:"snat_rule_name"`
	Status           string       `json:"status"`
	BackendType      string       `json:"backend_type"`
	CustomCIDR       string       `json:"custom_cidr"`
	EipList          []EdgeNatEip `json:"eip_list"`
	RevealSrcIP      bool         `json:"reveal_src_ip"`
	CreateTime       int64        `json:"create_time"`
	UpdateTime       int64        `json:"update_time"`
}

func parseEdgeNat(raw map[string]any) model.Asset {
	original := &EdgeNAT{}
	meta := &model.ObjectMeta{}
	nat := &model.NAT{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.NatGateway.NatgwIdentity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "edge-nat", original.NatGateway.NatgwIdentity)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "edge-nat"
	meta.Name = original.NatGateway.NatgwName

	nat.VPCID = utils.GenerateUID(ProviderID, "vpc", original.NatGateway.Vpc.VpcIdentity)
	nat.Status = strings.ToLower(original.NatGateway.Status)
	nat.EipIDList = utils.Map(original.NatGateway.EipList, func(ip EdgeNatEip) string {
		return utils.GenerateUID(ProviderID, "eip", ip.EipIdentity)
	})
	nat.SNATRules = utils.Map(original.SnatRules, func(snatRule EdgeSnatRule) model.SNATRule {
		return model.SNATRule{
			RuleID: snatRule.SnatRuleIdentity,
			Name:   snatRule.SnatRuleName,
			Status: strings.ToLower(snatRule.Status),
			CIDR:   snatRule.CustomCIDR,
			Eip:    utils.Map(snatRule.EipList, func(ip EdgeNatEip) string { return ip.EipAddr }),
		}
	})

	nat.DNATRules = utils.Map(original.DnatRules, func(dnatRule EdgeDnatRule) model.DNATRule {
		return model.DNATRule{
			RuleID:       dnatRule.DnatRuleIdentity,
			Name:         dnatRule.DnatRuleName,
			Status:       strings.ToLower(dnatRule.Status),
			Protocol:     dnatRule.Protocol,
			InternalIP:   dnatRule.PrivateIP,
			InternalPort: dnatRule.PrivatePort,
			ExternalIP:   dnatRule.Eip.EipAddr,
			ExternalPort: dnatRule.PublicPort,
		}
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, nat, utils.WithOmitempty())
	return asset
}
