package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type Redis struct {
	Accounts    []RedisAccount     `json:"accounts"`
	Instance    RedisInstance      `json:"instance"`
	IPWhiteList []RedisIPWhiteList `json:"ipWhiteList"`
}

type RedisAccount struct {
	AccountName string `json:"AccountName"`
	Description string `json:"Description"`
	InstanceID  string `json:"InstanceId"`
	RoleName    string `json:"RoleName"`
}

type RedisIPWhiteList struct {
	Metadata            RedisMetadata             `json:"Metadata"`
	AllowList           string                    `json:"AllowList"`
	AllowListDesc       string                    `json:"AllowListDesc"`
	AllowListID         string                    `json:"AllowListId"`
	AllowListName       string                    `json:"AllowListName"`
	AllowListType       string                    `json:"AllowListType"`
	AssociatedInstances []RedisAssociatedInstance `json:"AssociatedInstances"`
}

type RedisAssociatedInstance struct {
	InstanceID   string `json:"InstanceId"`
	InstanceName string `json:"InstanceName"`
	Vpc          string `json:"VPC"`
}

type RedisMetadata struct {
	RequestID string      `json:"RequestId"`
	Action    string      `json:"Action"`
	Version   string      `json:"Version"`
	Service   string      `json:"Service"`
	Region    string      `json:"Region"`
	HTTPCode  int64       `json:"HTTPCode"`
	Error     interface{} `json:"Error"`
}

type RedisInstance struct {
	Metadata           Metadata    `json:"Metadata"`
	AutoRenew          bool        `json:"AutoRenew"`
	Capacity           Capacity    `json:"Capacity"`
	ChargeType         string      `json:"ChargeType"`
	CreateTime         string      `json:"CreateTime"`
	DeletionProtection string      `json:"DeletionProtection"`
	EngineVersion      string      `json:"EngineVersion"`
	ExpiredTime        string      `json:"ExpiredTime"`
	InstanceClass      string      `json:"InstanceClass"`
	InstanceID         string      `json:"InstanceId"`
	InstanceName       string      `json:"InstanceName"`
	MaintenanceTime    string      `json:"MaintenanceTime"`
	MultiAZ            string      `json:"MultiAZ"`
	NodeNumber         int64       `json:"NodeNumber"`
	ProjectName        string      `json:"ProjectName"`
	RegionID           string      `json:"RegionId"`
	ShardCapacity      int64       `json:"ShardCapacity"`
	ShardCapacityV2    int64       `json:"ShardCapacityV2"`
	ShardNumber        int64       `json:"ShardNumber"`
	ShardedCluster     int64       `json:"ShardedCluster"`
	Status             string      `json:"Status"`
	SubnetID           string      `json:"SubnetId"`
	Tags               []Tags      `json:"Tags"`
	VisitAddrs         []VisitAddr `json:"VisitAddrs"`
	VpcAuthMode        string      `json:"VpcAuthMode"`
	VpcID              string      `json:"VpcId"`
	ZoneIDS            []string    `json:"ZoneIds"`
}

type Capacity struct {
	Total int64 `json:"Total"`
	Used  int64 `json:"Used"`
}

type VisitAddr struct {
	AddrType string      `json:"AddrType"`
	Address  string      `json:"Address"`
	EipID    interface{} `json:"EipId"`
	Port     string      `json:"Port"`
	Vip      string      `json:"VIP"`
}

func parseRedis(raw map[string]any) model.Asset {
	original := &Redis{}
	meta := &model.ObjectMeta{}
	redis := &model.Redis{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Instance.InstanceID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "redis", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "redis"
	meta.Name = original.Instance.InstanceName
	meta.OriginalLabels = utils.Map(original.Instance.Tags, func(e Tags) model.KV { return model.KV{Key: e.Key, Value: e.Value} })

	if len(original.Instance.VisitAddrs) > 0 {
		redis.ConnectionAddress = original.Instance.VisitAddrs[0].Address
	}
	redis.Engine = "Redis"
	redis.EngineVersion = original.Instance.EngineVersion
	_, redis.PublicAllowed = utils.Find(original.Instance.VisitAddrs, func(item VisitAddr) bool {
		return item.AddrType == "Public"
	})
	redis.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Instance.VpcID)
	redis.IpWhiteList = utils.Flatten(
		utils.Map(original.IPWhiteList, func(ipList RedisIPWhiteList) []string {
			return strings.Split(ipList.AllowList, ",")
		}),
	)
	redis.SubnetID = utils.GenerateUID(meta.Provider, "subnet", original.Instance.SubnetID)
	redis.DeleteProtection = utils.DataPointer(strings.EqualFold(original.Instance.DeletionProtection, "enabled"))

	redis.TDEEnabled = nil
	redis.Accounts = utils.Map(original.Accounts, func(account RedisAccount) model.RedisAccount {
		return model.RedisAccount{
			Name:        account.AccountName,
			Enabled:     true,
			Class:       lo.Ternary(strings.EqualFold(account.RoleName, "administrator"), "admin", "user"),
			Description: account.Description,
		}
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, redis)
	return asset
}
