package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"strings"
	"time"
)

type Function struct {
	Function FunctionClass `json:"function"`
}

type FunctionClass struct {
	CodeSizeLimit       int64          `json:"CodeSizeLimit"`
	Command             *string        `json:"Command,omitempty"`
	CreationTime        string         `json:"CreationTime"`
	Description         string         `json:"Description"`
	ExclusiveMode       bool           `json:"ExclusiveMode"`
	ID                  string         `json:"Id"`
	InitializerSEC      int64          `json:"InitializerSec"`
	InstanceType        string         `json:"InstanceType"`
	LastUpdateTime      string         `json:"LastUpdateTime"`
	MaxConcurrency      int64          `json:"MaxConcurrency"`
	MemoryMB            int64          `json:"MemoryMB"`
	Name                string         `json:"Name"`
	Owner               string         `json:"Owner"`
	RequestTimeout      int64          `json:"RequestTimeout"`
	Runtime             string         `json:"Runtime"`
	SourceType          string         `json:"SourceType"`
	TLSConfig           TLSConfig      `json:"TlsConfig"`
	VpcConfig           VpcConfig      `json:"VpcConfig"`
	CodeSize            int64          `json:"CodeSize"`
	SourceLocation      string         `json:"SourceLocation"`
	Envs                []Env          `json:"Envs"`
	ResourcePoolID      string         `json:"ResourcePoolId"`
	TriggersCount       int64          `json:"TriggersCount"`
	UseStatus           string         `json:"UseStatus"`
	DebugInstanceEnable bool           `json:"DebugInstanceEnable"`
	NASStorage          NASStorage     `json:"NasStorage"`
	TosMountConfig      TosMountConfig `json:"TosMountConfig"`
}

type Env struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

type NASStorage struct {
	EnableNAS  bool        `json:"EnableNas"`
	NASConfigs []NASConfig `json:"NasConfigs"`
}

type NASConfig struct {
	FileSystemID   string `json:"FileSystemId"`
	MountPointID   string `json:"MountPointId"`
	RemotePath     string `json:"RemotePath"`
	LocalMountPath string `json:"LocalMountPath"`
	Uid            int64  `json:"Uid"`
	Gid            int64  `json:"Gid"`
}

type TLSConfig struct {
	EnableLog    bool   `json:"EnableLog"`
	TLSProjectID string `json:"TlsProjectId"`
	TLSTopicID   string `json:"TlsTopicId"`
}

type TosMountConfig struct {
	EnableTos   bool            `json:"EnableTos"`
	MountPoints []TosMountPoint `json:"MountPoints"`
}

type TosMountPoint struct {
	Endpoint       string `json:"Endpoint"`
	BucketName     string `json:"BucketName"`
	BucketPath     string `json:"BucketPath"`
	LocalMountPath string `json:"LocalMountPath"`
	ReadOnly       bool   `json:"ReadOnly"`
}

type VpcConfig struct {
	EnableSharedInternetAccess bool     `json:"EnableSharedInternetAccess"`
	EnableVpc                  bool     `json:"EnableVpc"`
	SecurityGroupIDS           []string `json:"SecurityGroupIds"`
	SubnetIDS                  []string `json:"SubnetIds"`
	VpcID                      string   `json:"VpcId"`
}

func parseFunction(raw map[string]any) model.Asset {
	original := &Function{}
	meta := &model.ObjectMeta{}
	function := &model.Function{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Function.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "function", original.Function.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "function"
	meta.Name = original.Function.Name

	function.LogEnabled = original.Function.TLSConfig.EnableLog
	if function.LogEnabled {
		function.LogPath = fmt.Sprintf("/project/%s/topic/%s", original.Function.TLSConfig.TLSProjectID, original.Function.TLSConfig.TLSTopicID)
	}
	function.Runtime = strings.ToLower(original.Function.Runtime)
	// 火山函数默认支持公网访问
	function.DirectInternetAccessAllowed = true

	if original.Function.VpcConfig.EnableVpc {
		// 如果火山函数配置了VPC，则需要判断是否支持直接公网访问
		function.DirectInternetAccessAllowed = original.Function.VpcConfig.EnableSharedInternetAccess
		function.VpcId = utils.GenerateUID(meta.Provider, "vpc", original.Function.VpcConfig.VpcID)
		function.SubnetIdList = utils.Map(original.Function.VpcConfig.SubnetIDS, func(subnetID string) string {
			return utils.GenerateUID(meta.Provider, "subnet", subnetID)
		})
		function.SGIdList = utils.Map(original.Function.VpcConfig.SecurityGroupIDS, func(securityGroupID string) string {
			return utils.GenerateUID(meta.Provider, "security-group", securityGroupID)
		})
	}
	if original.Function.NASStorage.EnableNAS {
		function.NasMountPoints = utils.Map(original.Function.NASStorage.NASConfigs, func(e NASConfig) string {
			return fmt.Sprintf("%s:%s", e.MountPointID, e.RemotePath)
		})
	}
	if original.Function.TosMountConfig.EnableTos {
		function.BucketMountPoints = utils.Map(original.Function.TosMountConfig.MountPoints, func(e TosMountPoint) string {
			return fmt.Sprintf("%s:%s", e.BucketName, e.BucketPath)
		})
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, function)
	return asset
}
