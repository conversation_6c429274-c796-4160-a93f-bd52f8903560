package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

type EdgeClbListenerAgg struct {
	ClbID    string          `json:"clb_id"`
	Listener EdgeClbListener `json:"listener"`
}

func parseEdgeClbListener(raw map[string]any) model.Asset {
	original := &EdgeClbListenerAgg{}
	meta := &model.ObjectMeta{}
	listener := &model.LbListener{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Listener.ListenerUniq
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "edge-lb-listener", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "edge-lb-listener"
	meta.Name = original.Listener.Name

	listener.LbID = utils.GenerateUID(ProviderID, "lb", original.ClbID)
	listener.LbOriginalID = original.ClbID
	listener.Status = "active"
	listener.Port = original.Listener.ListenPort
	listener.Protocol = strings.ToLower(original.Listener.ListenPortProtocol)
	listener.AclStatus = "off"
	listener.AclType = ""
	listener.CertExists = utils.DataPointer(false)
	listener.HealthCheckEnabled = utils.DataPointer(original.Listener.HealthCheck.Period > 0)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, listener, utils.WithOmitempty())
	return asset
}

type EdgeAlbListenerAgg struct {
	AlbID    string          `json:"alb_id"`
	Listener EdgeAlbListener `json:"listener"`
}

func parseEdgeAlbListener(raw map[string]any) model.Asset {
	original := &EdgeAlbListenerAgg{}
	meta := &model.ObjectMeta{}
	listener := &model.LbListener{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Listener.Identity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "edge-lb-listener", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "edge-lb-listener"
	meta.Name = original.Listener.Name

	listener.LbID = utils.GenerateUID(ProviderID, "edge-lb", original.AlbID)
	listener.LbOriginalID = original.AlbID
	listener.Status = "active"
	listener.Port = original.Listener.ListenPort
	listener.AclStatus = "off"
	listener.AclType = ""
	listener.CertExists = utils.DataPointer(false)
	listener.HealthCheckEnabled = nil

	switch original.Listener.ListenProtocol {
	case 1:
		listener.Protocol = "http"
	case 2:
		listener.Protocol = "https"
	default:
		listener.Protocol = "unknown"
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, listener, utils.WithOmitempty())
	return asset
}
