package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

func parseEni(raw map[string]any) model.Asset {
	meta := &model.ObjectMeta{}
	eni := &model.ENI{}
	original := &Eni{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalID = original.NetworkInterfaceID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "eni", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Description
	meta.Kind = "eni"
	meta.Name = original.NetworkInterfaceName

	eni.VPCID = utils.GenerateUID(ProviderID, "vpc", original.VpcID)
	eni.SubnetID = utils.GenerateUID(ProviderID, "subnet", original.SubnetID)
	eni.MacAddress = original.MACAddress
	if original.DeviceID != "" {
		eni.ECSID = utils.GenerateUID(ProviderID, "ecs", original.DeviceID)
	}
	eni.Status = strings.ToLower(original.Status)
	eni.PrivateIPList = utils.FilterMap(original.PrivateIPSets.PrivateIPSet, func(ipSet PrivateIPSet) (string, bool) {
		return ipSet.PrivateIPAddress, ipSet.PrivateIPAddress != ""
	})
	eni.PublicIPList = utils.FilterMap(original.PrivateIPSets.PrivateIPSet, func(ipSet PrivateIPSet) (string, bool) {
		return ipSet.AssociatedElasticIP.EipAddress, ipSet.AssociatedElasticIP.EipAddress != ""
	})
	eni.Class = strings.ToLower(original.Type)
	eni.PrimaryIP = original.PrimaryIPAddress
	eni.EIPIDList = utils.FilterMap(original.PrivateIPSets.PrivateIPSet, func(ipSet PrivateIPSet) (string, bool) {
		return utils.GenerateUID(ProviderID, "eip", ipSet.AssociatedElasticIP.EipAddress), ipSet.AssociatedElasticIP.AllocationID != ""
	})

	eni.SGIDList = utils.Map(original.SecurityGroupIDS, func(e string) string { return utils.GenerateUID(ProviderID, "security-group", e) })

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, eni)
	return asset
}
