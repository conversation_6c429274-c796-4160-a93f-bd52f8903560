package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type Eip struct {
	Eip EipClass `json:"eip"`
}

type EipClass struct {
	AllocationID            string        `json:"AllocationId"`
	AllocationTime          string        `json:"AllocationTime"`
	Bandwidth               int64         `json:"Bandwidth"`
	BandwidthPackageID      interface{}   `json:"BandwidthPackageId"`
	BillingType             int64         `json:"BillingType"`
	BusinessStatus          string        `json:"BusinessStatus"`
	DeletedTime             string        `json:"DeletedTime"`
	Description             string        `json:"Description"`
	EipAddress              string        `json:"EipAddress"`
	ExpiredTime             string        `json:"ExpiredTime"`
	ISP                     string        `json:"ISP"`
	InstanceID              string        `json:"InstanceId"`
	InstanceType            string        `json:"InstanceType"`
	LockReason              string        `json:"LockReason"`
	Name                    string        `json:"Name"`
	OverdueTime             string        `json:"OverdueTime"`
	ProjectName             string        `json:"ProjectName"`
	ReleaseWithInstance     bool          `json:"ReleaseWithInstance"`
	SecurityProtectionTypes []interface{} `json:"SecurityProtectionTypes"`
	Status                  string        `json:"Status"`
	Tags                    []Tag         `json:"Tags"`
	UpdatedAt               string        `json:"UpdatedAt"`
}

func parseEip(raw map[string]any) model.Asset {
	meta := &model.ObjectMeta{}
	eip := &model.Eip{}
	original := &Eip{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.Eip.Tags, func(tag Tag) model.KV {
		return model.KV{
			Key:   tag.Key,
			Value: tag.Value,
		}
	})
	meta.OriginalID = original.Eip.AllocationID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "eip", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Eip.Description
	meta.Kind = "eip"
	meta.Name = original.Eip.Name

	eip.IP = original.Eip.EipAddress
	eip.Status = lo.Ternary(strings.EqualFold(original.Eip.Status, "Attached"), "binded", "available")
	eip.Bandwidth = original.Eip.Bandwidth
	eip.ISP = original.Eip.ISP
	switch original.Eip.InstanceType {
	case "Nat":
		eip.NATID = utils.GenerateUID(ProviderID, "nat", original.Eip.InstanceID)
	case "NetworkInterface":
		eip.ENIID = utils.GenerateUID(ProviderID, "eni", original.Eip.InstanceID)
	case "ClbInstance", "Albinstance":
		eip.LBID = utils.GenerateUID(ProviderID, "lb", original.Eip.InstanceID)
	case "EcsInstance":
		eip.ECSID = utils.GenerateUID(ProviderID, "ecs", original.Eip.InstanceID)
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, eip)
	return asset
}
