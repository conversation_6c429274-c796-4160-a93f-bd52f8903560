package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"time"

	"github.com/samber/lo"
)

type AuditLog struct {
	AuditLogEntries []AuditLogEntry `json:"auditLogEntries"`
}

type AuditLogEntry struct {
	ServiceName string `json:"serviceName"`
	EventName   string `json:"eventName"`
	LastIngest  string `json:"lastIngest"`
}

func parseAuditLog(raw map[string]any) model.Asset {
	original := &AuditLog{}
	meta := &model.ObjectMeta{}
	cs := &model.CloudServiceProvider{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = ProviderID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "cloud-service-provider", ProviderID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "cloud-service-provider"
	meta.Name = ProviderID

	cs.AuditLogStatus = lo.Map(original.AuditLogEntries, func(entry AuditLogEntry, _ int) model.AuditLogStatus {
		e := model.AuditLogStatus{
			ServiceName: entry.ServiceName,
			EventName:   entry.EventName,
		}
		e.LastIngestAt, _ = strconv.ParseInt(entry.LastIngest, 10, 64)
		e.LastIngestAt *= 1000
		return e
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, cs)
	return asset
}
