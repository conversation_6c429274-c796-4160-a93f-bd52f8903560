package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"encoding/json"
	"strings"
	"time"
)

type PolicyDetail struct {
	CreateDate     string `json:"createDate"`
	Description    string `json:"description"`
	PolicyDocument string `json:"policyDocument"`
	PolicyName     string `json:"policyName"`
	PolicyTrn      string `json:"policyTrn"`
	PolicyType     string `json:"policyType"`
	UpdateDate     string `json:"updateDate"`
}

type PolicyDocument struct {
	Statement []Statement `json:"Statement"`
}

type Statement struct {
	Action    []string `json:"Action"`
	Resource  []string `json:"Resource"`
	Effect    string   `json:"Effect"`
	Condition any      `json:"Condition"`
	Principal any      `json:"Principal"`
}

var policyTypeMap = map[string]string{
	"System": "managed",
	"Custom": "custom",
}

func parsePolicyDetail(raw map[string]any) model.Asset {
	original := &PolicyDetail{}
	meta := &model.ObjectMeta{}
	policy := &model.Policy{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalID = original.PolicyTrn
	meta.UID = utils.GenerateUID(meta.Provider, "policy", original.PolicyName)
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = original.Description
	meta.Kind = "policy"
	meta.Name = original.PolicyName

	parsedDocument := PolicyDocument{}
	json.Unmarshal([]byte(original.PolicyDocument), &parsedDocument)

	v, ok := policyTypeMap[original.PolicyType]
	policy.Class = utils.UnwrapOr(v, ok, strings.ToLower(original.PolicyType))

	policy.PolicyDocument = utils.Map(parsedDocument.Statement, func(statement Statement) model.PolicyDocument {
		pd := model.PolicyDocument{}
		pd.Effect = strings.ToLower(statement.Effect)

		pd.Action = statement.Action
		pd.Resource = statement.Resource
		pd.Principal = utils.ToJsonStr(statement.Principal)
		pd.Condition = utils.ToJsonStr(statement.Condition)
		return pd
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, policy)
	return asset
}
