package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"
)

type Subnet struct {
	ACL    []SubnetACL `json:"acl"`
	Subnet SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	AccountID         string            `json:"AccountId"`
	NetworkACLID      string            `json:"NetworkAclId"`
	NetworkACLName    string            `json:"NetworkAclName"`
	Description       string            `json:"Description"`
	VpcID             string            `json:"VpcId"`
	IngressACLEntries []IngressACLEntry `json:"IngressAclEntries"`
	EgressACLEntries  []EgressACLEntry  `json:"EgressAclEntries"`
	Resources         []Resource        `json:"Resources"`
	Status            string            `json:"Status"`
	ProjectName       string            `json:"ProjectName"`
	CreationTime      string            `json:"CreationTime"`
	UpdateTime        string            `json:"UpdateTime"`
}

type EgressACLEntry struct {
	NetworkACLEntryID   string `json:"NetworkAclEntryId"`
	NetworkACLEntryName string `json:"NetworkAclEntryName"`
	Description         string `json:"Description"`
	Policy              string `json:"Policy"`
	DestinationCIDRIP   string `json:"DestinationCidrIp"`
	Protocol            string `json:"Protocol"`
	Priority            int    `json:"Priority"`
	Port                string `json:"Port"`
}

type IngressACLEntry struct {
	NetworkACLEntryID   string `json:"NetworkAclEntryId"`
	NetworkACLEntryName string `json:"NetworkAclEntryName"`
	Description         string `json:"Description"`
	Policy              string `json:"Policy"`
	SourceCIDRIP        string `json:"SourceCidrIp"`
	Protocol            string `json:"Protocol"`
	Priority            int    `json:"Priority"`
	Port                string `json:"Port"`
}

type Resource struct {
	Status     string `json:"Status"`
	ResourceID string `json:"ResourceId"`
}

type SubnetClass struct {
	AccountID               string     `json:"AccountId"`
	AvailableIPAddressCount int        `json:"AvailableIpAddressCount"`
	CIDRBlock               string     `json:"CidrBlock"`
	CreationTime            string     `json:"CreationTime"`
	Description             string     `json:"Description"`
	Ipv6CIDRBlock           string     `json:"Ipv6CidrBlock"`
	IsDefault               bool       `json:"IsDefault"`
	NetworkACLID            string     `json:"NetworkAclId"`
	ProjectName             string     `json:"ProjectName"`
	RouteTable              RouteTable `json:"RouteTable"`
	Status                  string     `json:"Status"`
	SubnetID                string     `json:"SubnetId"`
	SubnetName              string     `json:"SubnetName"`
	TotalIpv4Count          int64      `json:"TotalIpv4Count"`
	UpdateTime              string     `json:"UpdateTime"`
	VpcID                   string     `json:"VpcId"`
	ZoneID                  string     `json:"ZoneId"`
}

type RouteTable struct {
	RouteTableID   string `json:"RouteTableId"`
	RouteTableType string `json:"RouteTableType"`
}

func parseSubnet(raw map[string]any) model.Asset {
	original := &Subnet{}
	meta := &model.ObjectMeta{}
	subnet := &model.Subnet{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Subnet.SubnetID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "subnet", original.Subnet.SubnetID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "subnet"
	meta.Name = original.Subnet.SubnetName
	meta.Description = original.Subnet.Description

	subnet.IsDefault = original.Subnet.IsDefault
	subnet.CIDR = original.Subnet.CIDRBlock
	subnet.CIDRv6 = original.Subnet.Ipv6CIDRBlock
	subnet.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Subnet.VpcID)
	subnet.AvailableIPCount = original.Subnet.AvailableIPAddressCount
	subnet.ACL = parseACL(original.ACL)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, subnet)
	return asset
}

func parseACL(acls []SubnetACL) []model.ACLRule {
	results := []model.ACLRule{}

	for _, acl := range acls {
		for _, entry := range acl.EgressACLEntries {
			rule := model.ACLRule{}
			rule.RuleID = entry.NetworkACLEntryID
			rule.Name = entry.NetworkACLEntryName
			rule.Description = entry.Description
			rule.Protocol = entry.Protocol
			rule.IPVersion = "IPv4"
			rule.Policy = entry.Policy
			rule.Priority = entry.Priority
			rule.Direction = "egress"
			rule.PeerCIDR = entry.DestinationCIDRIP

			if entry.Port == "" {
				rule.PortStart, rule.PortEnd = 1, 65535
			} else {
				ports := strings.Split(entry.Port, "/")
				switch len(ports) {
				case 1:
					port, _ := strconv.Atoi(ports[0])
					rule.PortStart, rule.PortEnd = port, port
				case 2:
					portStart, _ := strconv.Atoi(ports[0])
					portEnd, _ := strconv.Atoi(ports[1])
					rule.PortStart = utils.UnwrapOr(portStart, portStart > 0, 1)
					rule.PortEnd = utils.UnwrapOr(portEnd, portEnd > 0, 65535)
				}
			}

			results = append(results, rule)
		}
		for _, entry := range acl.IngressACLEntries {
			rule := model.ACLRule{}
			rule.RuleID = entry.NetworkACLEntryID
			rule.Name = entry.NetworkACLEntryName
			rule.Description = entry.Description
			rule.Protocol = entry.Protocol
			rule.IPVersion = "IPv4"
			rule.Policy = entry.Policy
			rule.Priority = entry.Priority
			rule.Direction = "egress"
			rule.PeerCIDR = entry.SourceCIDRIP

			if entry.Port == "" {
				rule.PortStart, rule.PortEnd = 1, 65535
			} else {
				ports := strings.Split(entry.Port, "/")
				switch len(ports) {
				case 1:
					port, _ := strconv.Atoi(ports[0])
					rule.PortStart, rule.PortEnd = port, port
				case 2:
					portStart, _ := strconv.Atoi(ports[0])
					portEnd, _ := strconv.Atoi(ports[1])
					rule.PortStart = utils.UnwrapOr(portStart, portStart > 0, 1)
					rule.PortEnd = utils.UnwrapOr(portEnd, portEnd > 0, 65535)
				}
			}

			results = append(results, rule)
		}
	}
	return results
}
