package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"encoding/json"
	"time"
)

type Role struct {
	Policies []RolePolicy `json:"policies"`
	Role     RoleClass    `json:"role"`
}

type RolePolicy struct {
	AttachDate  string `json:"AttachDate"`
	Description string `json:"Description"`
	PolicyName  string `json:"PolicyName"`
	PolicyTrn   string `json:"PolicyTrn"`
	PolicyType  string `json:"PolicyType"`
}

type RoleClass struct {
	CreateDate          string `json:"CreateDate"`
	Description         string `json:"Description"`
	RoleID              int64  `json:"RoleId"`
	RoleName            string `json:"RoleName"`
	Trn                 string `json:"Trn"`
	TrustPolicyDocument string `json:"TrustPolicyDocument"`
}

func parseRole(raw map[string]any) model.Asset {
	original := &Role{}
	meta := &model.ObjectMeta{}
	role := &model.Role{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Role.Trn
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "role", original.Role.RoleName)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "role"
	meta.Name = original.Role.RoleName
	meta.Description = original.Role.Description

	role.MaxSessionDuration = utils.DataPointer(-1)
	role.PolicyIDList = utils.Map(original.Policies, func(policy RolePolicy) string {
		return utils.GenerateUID(meta.Provider, "policy", policy.PolicyName)
	})

	parsedDocument := PolicyDocument{}
	json.Unmarshal([]byte(original.Role.TrustPolicyDocument), &parsedDocument)
	role.AssumeRolePrincipal = utils.Map(parsedDocument.Statement, func(statement Statement) string {
		return utils.ToJsonStr(statement.Principal)
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, role, utils.WithOmitempty())
	return asset
}
