package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"
)

type NAT struct {
	DnatTable  []DnatTable `json:"dnatTable"`
	NatGateway NatGateway  `json:"natGateway"`
	SnatTable  []SnatTable `json:"snatTable"`
}

type DnatTable struct {
	NatGatewayID  string `json:"NatGatewayId"`
	DnatEntryID   string `json:"DnatEntryId"`
	DnatEntryName string `json:"DnatEntryName"`
	Protocol      string `json:"Protocol"`
	InternalIP    string `json:"InternalIp"`
	InternalPort  string `json:"InternalPort"`
	ExternalIP    string `json:"ExternalIp"`
	ExternalPort  string `json:"ExternalPort"`
	Status        string `json:"Status"`
}

type NatGateway struct {
	BillingType        int64           `json:"BillingType"`
	BusinessStatus     string          `json:"BusinessStatus"`
	CreationTime       string          `json:"CreationTime"`
	DeletedTime        string          `json:"DeletedTime"`
	Description        string          `json:"Description"`
	DnatEntryIDS       []interface{}   `json:"DnatEntryIds"`
	EipAddresses       []NatEipAddress `json:"EipAddresses"`
	ExpiredTime        string          `json:"ExpiredTime"`
	LockReason         string          `json:"LockReason"`
	NatGatewayID       string          `json:"NatGatewayId"`
	NatGatewayName     string          `json:"NatGatewayName"`
	NetworkInterfaceID string          `json:"NetworkInterfaceId"`
	NetworkType        string          `json:"NetworkType"`
	OverdueTime        string          `json:"OverdueTime"`
	ProjectName        string          `json:"ProjectName"`
	SnatEntryIDS       []string        `json:"SnatEntryIds"`
	Spec               string          `json:"Spec"`
	Status             string          `json:"Status"`
	SubnetID           string          `json:"SubnetId"`
	Tags               []Tags          `json:"Tags"`
	UpdatedAt          string          `json:"UpdatedAt"`
	VpcID              string          `json:"VpcId"`
	ZoneID             string          `json:"ZoneId"`
}

type NatEipAddress struct {
	AllocationID string `json:"AllocationId"`
	EipAddress   string `json:"EipAddress"`
	UsingStatus  string `json:"UsingStatus"`
}

type SnatTable struct {
	EipAddress    string `json:"EipAddress"`
	EipID         string `json:"EipId"`
	NatGatewayID  string `json:"NatGatewayId"`
	NatIPID       string `json:"NatIpId"`
	SnatEntryID   string `json:"SnatEntryId"`
	SnatEntryName string `json:"SnatEntryName"`
	SourceCIDR    string `json:"SourceCidr"`
	Status        string `json:"Status"`
	SubnetID      string `json:"SubnetId"`
}

func parseNat(raw map[string]any) model.Asset {
	original := &NAT{}
	meta := &model.ObjectMeta{}
	nat := &model.NAT{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.NatGateway.NatGatewayID
	meta.OriginalLabels = utils.Map(original.NatGateway.Tags, func(e Tags) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "nat", original.NatGateway.NatGatewayID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "nat"
	meta.Name = original.NatGateway.NatGatewayName

	nat.VPCID = utils.GenerateUID(ProviderID, "vpc", original.NatGateway.VpcID)
	nat.SubnetID = utils.GenerateUID(ProviderID, "subnet", original.NatGateway.SubnetID)
	nat.Status = strings.ToLower(original.NatGateway.Status)
	nat.Spec = original.NatGateway.Spec
	nat.EipIDList = utils.Map(original.NatGateway.EipAddresses, func(ip NatEipAddress) string {
		return utils.GenerateUID(ProviderID, "eip", ip.AllocationID)
	})
	nat.SNATRules = utils.Map(original.SnatTable, func(snatTable SnatTable) model.SNATRule {
		return model.SNATRule{
			RuleID: snatTable.SnatEntryID,
			Name:   snatTable.SnatEntryName,
			Status: strings.ToLower(snatTable.Status),
			CIDR:   snatTable.SourceCIDR,
			Eip:    strings.Split(snatTable.EipAddress, ","),
		}
	})

	nat.DNATRules = utils.Map(original.DnatTable, func(dnatTable DnatTable) model.DNATRule {
		internalPort, _ := strconv.Atoi(dnatTable.InternalPort)
		externalPort, _ := strconv.Atoi(dnatTable.ExternalPort)
		return model.DNATRule{
			RuleID:       dnatTable.DnatEntryID,
			Name:         dnatTable.DnatEntryName,
			Status:       strings.ToLower(dnatTable.Status),
			Protocol:     dnatTable.Protocol,
			InternalIP:   dnatTable.InternalIP,
			InternalPort: internalPort,
			ExternalIP:   dnatTable.ExternalIP,
			ExternalPort: externalPort,
		}
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, nat, utils.WithOmitempty())
	return asset
}
