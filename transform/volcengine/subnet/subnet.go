package subnet

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "subnet"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_subnet_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["subnet_list"] = append(resourceData["subnet_list"], utils.GenParamsFromStruct(resource))

		resourceData["acl_list"] = append(resourceData["acl_list"], utils.GenParamsFromStructSlice(resource.ACL)...)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.DstPortRange)
			})...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, aclSchema, resourceData["acl_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIpRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIpRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type Subnet struct {
	NetworkACL SubnetACL   `json:"nacl"`
	Subnet     SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	Status            string            `json:"Status"`
	VpcID             string            `json:"VpcId"`
	CreationTime      string            `json:"CreationTime"`
	Description       string            `json:"Description"`
	NetworkACLName    string            `json:"NetworkAclName"`
	NetworkACLID      string            `json:"NetworkAclId"`
	OwnerID           float64           `json:"OwnerId"`
	RegionID          string            `json:"RegionId"`
	IngressACLEntries IngressACLEntries `json:"IngressAclEntries"`
	EgressACLEntries  EgressACLEntries  `json:"EgressAclEntries"`
	Resources         Resources         `json:"Resources"`
	Tags              Tags              `json:"Tags"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	Key   string `json:"TagKey"`
	Value string `json:"TagValue"`
}

type EgressACLEntries struct {
	EgressACLEntry []EgressACLEntry `json:"EgressAclEntry"`
}

type EgressACLEntry struct {
	NetworkACLEntryID   string `json:"NetworkAclEntryId"`
	EntryType           string `json:"EntryType"`
	NetworkACLEntryName string `json:"NetworkAclEntryName"`
	Policy              string `json:"Policy"`
	Description         string `json:"Description"`
	Protocol            string `json:"Protocol"`
	DestinationCIDRIP   string `json:"DestinationCidrIp"`
	IPVersion           string `json:"IpVersion"`
	Port                string `json:"Port"`
}

type IngressACLEntries struct {
	IngressACLEntry []IngressACLEntry `json:"IngressAclEntry"`
}

type IngressACLEntry struct {
	NetworkACLEntryID   string `json:"NetworkAclEntryId"`
	EntryType           string `json:"EntryType"`
	NetworkACLEntryName string `json:"NetworkAclEntryName"`
	Policy              string `json:"Policy"`
	Description         string `json:"Description"`
	SourceCIDRIP        string `json:"SourceCidrIp"`
	IPVersion           string `json:"IpVersion"`
	Protocol            string `json:"Protocol"`
	Port                string `json:"Port"`
}

type Resources struct {
	Resource []Resource `json:"Resource"`
}

type Resource struct {
	Status       string `json:"Status"`
	ResourceType string `json:"ResourceType"`
	ResourceID   string `json:"ResourceId"`
}

type SubnetClass struct {
	AvailableIPAddressCount int        `json:"AvailableIpAddressCount"`
	CIDRBlock               string     `json:"CidrBlock"`
	CreationTime            string     `json:"CreationTime"`
	Description             string     `json:"Description"`
	Ipv6CIDRBlock           string     `json:"Ipv6CidrBlock"`
	IsDefault               bool       `json:"IsDefault"`
	NetworkACLID            string     `json:"NetworkAclId"`
	OwnerID                 int64      `json:"OwnerId"`
	ResourceGroupID         string     `json:"ResourceGroupId"`
	RouteTable              RouteTable `json:"RouteTable"`
	ShareType               string     `json:"ShareType"`
	Status                  string     `json:"Status"`
	VSwitchID               string     `json:"VSwitchId"`
	VSwitchName             string     `json:"VSwitchName"`
	VpcID                   string     `json:"VpcId"`
	ZoneID                  string     `json:"ZoneId"`
}

type RouteTable struct {
	RouteTableID   string `json:"RouteTableId"`
	RouteTableType string `json:"RouteTableType"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.SubnetGraph, error) {
	original := &Subnet{}
	resource := &model.SubnetGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Subnet.VSwitchID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "subnet", original.Subnet.VSwitchID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "subnet"
	resource.Name = original.Subnet.VSwitchName
	resource.Description = original.Subnet.Description

	resource.IsDefault = original.Subnet.IsDefault
	resource.CIDR = original.Subnet.CIDRBlock
	resource.CIDRv6 = original.Subnet.Ipv6CIDRBlock
	resource.AvailableIPCount = original.Subnet.AvailableIPAddressCount

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Subnet.VpcID),
			TargetUID: resource.UID,
		},
	})
	if aclRules, err := parseACL(original.NetworkACL, resource.UID); err != nil {
		return nil, err
	} else {
		resource.ACL = aclRules
	}

	return resource, nil
}

func parseACL(acl SubnetACL, targetUID string) ([]*model.ACLRuleGraph, error) {
	results := []*model.ACLRuleGraph{}

	for _, entry := range acl.EgressACLEntries.EgressACLEntry {
		rule := &model.ACLRuleGraph{}
		rule.UID = utils.GenerateUID(provider_utils.ProviderID, "acl_rule", entry.NetworkACLEntryID)
		rule.TargetUID = targetUID
		rule.RuleID = entry.NetworkACLEntryID
		rule.Name = entry.NetworkACLEntryName
		rule.Description = entry.Description
		rule.Protocol = entry.Protocol
		rule.IPVersion = entry.IPVersion
		rule.Policy = entry.Policy
		rule.Direction = "egress"

		if ipRange, err := provider_utils.ParseIPRange(entry.DestinationCIDRIP, rule.UID); err != nil {
			return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, entry.DestinationCIDRIP)
		} else {
			rule.DstIPRange = []*model.IPRangeGraph{ipRange}
		}

		if portRange, err := provider_utils.ParsePortRange(entry.Port, rule.UID); err != nil {
			return nil, fmt.Errorf("parse port range error: %v, port: %s", err, entry.Port)
		} else {
			rule.DstPortRange = []*model.PortRangeGraph{portRange}
		}
		rule.SrcIPRange = []*model.IPRangeGraph{utils.NewAnyIPRange(rule.UID, strings.EqualFold(entry.IPVersion, "ipv6"))}
		rule.SrcPortRange = []*model.PortRangeGraph{utils.NewPortRange(0, 65535, rule.UID)}

		results = append(results, rule)
	}
	for _, entry := range acl.IngressACLEntries.IngressACLEntry {
		rule := &model.ACLRuleGraph{}
		rule.UID = utils.GenerateUID(provider_utils.ProviderID, "acl_rule", entry.NetworkACLEntryID)
		rule.TargetUID = targetUID
		rule.RuleID = entry.NetworkACLEntryID
		rule.Name = entry.NetworkACLEntryName
		rule.Description = entry.Description
		rule.Protocol = entry.Protocol
		rule.IPVersion = entry.IPVersion
		rule.Policy = entry.Policy
		rule.Direction = "ingress"

		if ipRange, err := provider_utils.ParseIPRange(entry.SourceCIDRIP, rule.UID); err != nil {
			return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, entry.SourceCIDRIP)
		} else {
			rule.SrcIPRange = []*model.IPRangeGraph{ipRange}
		}

		if portRange, err := provider_utils.ParsePortRange(entry.Port, rule.UID); err != nil {
			return nil, fmt.Errorf("parse port range error: %v, port: %s", err, entry.Port)
		} else {
			rule.DstPortRange = []*model.PortRangeGraph{portRange}
		}
		rule.SrcPortRange = []*model.PortRangeGraph{utils.NewPortRange(0, 65535, rule.UID)}
		rule.DstIPRange = []*model.IPRangeGraph{utils.NewAnyIPRange(rule.UID, strings.EqualFold(entry.IPVersion, "ipv6"))}

		results = append(results, rule)
	}

	return results, nil
}
