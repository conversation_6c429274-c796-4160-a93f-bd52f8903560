package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"time"
)

type EdgeSubnet struct {
	VpcID  string     `json:"vpc_id"`
	Subnet EdgeSubNet `json:"subnet"`
}

func parseEdgeSubnet(raw map[string]any) model.Asset {
	original := &EdgeSubnet{}
	meta := &model.ObjectMeta{}
	subnet := &model.Subnet{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Subnet.SubnetIdentity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "subnet", original.Subnet.SubnetIdentity)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "subnet"
	meta.Name = original.Subnet.Name
	meta.Description = original.Subnet.Desc

	subnet.CIDR = fmt.Sprintf("%s/%d", original.Subnet.CIDRIP, original.Subnet.CIDRMask)
	subnet.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.VpcID)
	subnet.AvailableIPCount = -1
	subnet.ACL = []model.ACLRule{}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, subnet)
	return asset
}
