package user

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "user"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ram_user_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["user_list"] = append(resourceData["user_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, userSchema, userData["user_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
}

type User struct {
	AccessKeys   []AccessKey   `json:"accessKeys"`
	LoginProfile *LoginProfile `json:"loginProfile"`
	Mfa          *Mfa          `json:"mfa"`
	Policies     []UserPolicy  `json:"policies"`
	User         UserClass     `json:"user"`
}

type AccessKey struct {
	AccessKeyID string `json:"AccessKeyId"`
	CreateDate  string `json:"CreateDate"`
	Status      string `json:"Status"`
}

type LoginProfile struct {
	LastLoginTime         string `json:"LastLoginTime"`
	MFABindRequired       bool   `json:"MFABindRequired"`
	PasswordResetRequired bool   `json:"PasswordResetRequired"`
	Status                string `json:"Status"`
	UpdateDate            string `json:"UpdateDate"`
	UserPrincipalName     string `json:"UserPrincipalName"`
}

type Mfa struct {
	SerialNumber string `json:"SerialNumber"`
	Type         string `json:"Type"`
}

type UserPolicy struct {
	AttachDate     string `json:"AttachDate"`
	DefaultVersion string `json:"DefaultVersion"`
	Description    string `json:"Description"`
	PolicyName     string `json:"PolicyName"`
	PolicyType     string `json:"PolicyType"`
}

type UserClass struct {
	Comments    string `json:"Comments"`
	CreateDate  string `json:"CreateDate"`
	DisplayName string `json:"DisplayName"`
	UpdateDate  string `json:"UpdateDate"`
	UserID      string `json:"UserId"`
	UserName    string `json:"UserName"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.UserGraph, error) {
	original := &User{}
	resource := &model.UserGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.User.UserID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "user", original.User.UserName)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "user"
	resource.Name = original.User.UserName
	resource.Description = original.User.Comments

	resource.DisplayName = original.User.DisplayName
	resource.Enabled = true
	t, _ := time.Parse(time.RFC3339, original.User.CreateDate)
	resource.CreatedAt = lo.Ternary(t.UnixMilli() > 0, t.UnixMilli(), 0)
	resource.MFAEnabled = original.Mfa != nil
	if original.LoginProfile != nil {
		if original.LoginProfile.Status == "Active" {
			resource.LoginAllowed = true
		}
		t, _ := time.Parse(time.RFC3339, original.LoginProfile.LastLoginTime)
		resource.LastLoginAt = t.UnixMilli()
	}

	resource.Policy = lo.Map(original.Policies, func(policy UserPolicy, _ int) *model.PolicyGraph {
		return &model.PolicyGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "policy", policy.PolicyName),
				TargetUID: resource.UID,
			},
		}
	})
	resource.AccessKey = lo.Map(original.AccessKeys, func(key AccessKey, _ int) *model.AkGraph {
		return &model.AkGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ak", key.AccessKeyID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
