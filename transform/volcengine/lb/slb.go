package lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var slbLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "slb"})

func NewSLBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_slb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSLB,
		UpdateResources: updateSLBResources,
	}
}

func transformSLB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSLB(assetMsg)
		if err != nil {
			slbLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateSLBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type Slb struct {
	LB        SlbClass      `json:"lb"`
	MSServers []interface{} `json:"msServers"`
	Servers   []SlbServer   `json:"servers"`
	VServers  []SlbVServer  `json:"vServers"`
}

type SlbClass struct {
	Address                      string                   `json:"address"`
	AddressIPVersion             string                   `json:"addressIPVersion"`
	AddressType                  string                   `json:"addressType"`
	AutoReleaseTime              any                      `json:"autoReleaseTime"`
	BackendServers               BackendServers           `json:"backendServers"`
	Bandwidth                    int                      `json:"bandwidth"`
	CreateTime                   time.Time                `json:"createTime"`
	CreateTimeStamp              int64                    `json:"createTimeStamp"`
	DeleteProtection             string                   `json:"deleteProtection"`
	EndTime                      time.Time                `json:"endTime"`
	EndTimeStamp                 int64                    `json:"endTimeStamp"`
	InstanceChargeType           string                   `json:"instanceChargeType"`
	InternetChargeType           string                   `json:"internetChargeType"`
	ListenerPorts                ListenerPorts            `json:"listenerPorts"`
	ListenerPortsAndProtocal     ListenerPortsAndProtocal `json:"listenerPortsAndProtocal"`
	ListenerPortsAndProtocol     ListenerPortsAndProtocol `json:"listenerPortsAndProtocol"`
	LoadBalancerID               string                   `json:"loadBalancerId"`
	LoadBalancerName             string                   `json:"loadBalancerName"`
	LoadBalancerSpec             string                   `json:"loadBalancerSpec"`
	LoadBalancerStatus           string                   `json:"loadBalancerStatus"`
	MasterZoneID                 string                   `json:"masterZoneId"`
	ModificationProtectionReason string                   `json:"modificationProtectionReason"`
	ModificationProtectionStatus string                   `json:"modificationProtectionStatus"`
	NetworkType                  string                   `json:"networkType"`
	PayType                      string                   `json:"payType"`
	RegionID                     string                   `json:"regionId"`
	RegionIDAlias                string                   `json:"regionIdAlias"`
	RenewalCycUnit               any                      `json:"renewalCycUnit"`
	RenewalDuration              any                      `json:"renewalDuration"`
	RenewalStatus                any                      `json:"renewalStatus"`
	RequestID                    string                   `json:"requestId"`
	ResourceGroupID              string                   `json:"resourceGroupId"`
	SlaveZoneID                  string                   `json:"slaveZoneId"`
	Tags                         Tags                     `json:"tags"`
	VSwitchID                    string                   `json:"vSwitchId"`
	VpcID                        string                   `json:"vpcId"`
	VswitchID                    string                   `json:"vswitchId"`
}
type BackendServers struct {
	BackendServer []any `json:"backendServer"`
}
type ListenerPorts struct {
	ListenerPort []int `json:"listenerPort"`
}
type ListenerPortAndProtocal struct {
	ListenerPort     int    `json:"listenerPort"`
	ListenerProtocal string `json:"listenerProtocal"`
}
type ListenerPortsAndProtocal struct {
	ListenerPortAndProtocal []ListenerPortAndProtocal `json:"listenerPortAndProtocal"`
}
type ListenerPortAndProtocol struct {
	Description      string `json:"description"`
	ForwardPort      any    `json:"forwardPort"`
	ListenerForward  any    `json:"listenerForward"`
	ListenerPort     int    `json:"listenerPort"`
	ListenerProtocol string `json:"listenerProtocol"`
}
type ListenerPortsAndProtocol struct {
	ListenerPortAndProtocol []ListenerPortAndProtocol `json:"listenerPortAndProtocol"`
}
type SlbServer struct {
	ListenerPort       int    `json:"ListenerPort"`
	Port               int    `json:"Port"`
	Protocol           string `json:"Protocol"`
	ServerHealthStatus string `json:"ServerHealthStatus"`
	ServerID           string `json:"ServerId"`
	ServerIP           string `json:"ServerIp"`
}

type SlbVServer struct {
	Port     int    `json:"Port"`
	ServerID string `json:"ServerId"`
	ServerIP string `json:"ServerIp"`
	Type     string `json:"Type"`
	Weight   int    `json:"Weight"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}
type Tag struct {
	Key   string `json:"TagKey"`
	Value string `json:"TagValue"`
}

func parseSLB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &Slb{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.LB.LoadBalancerID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.LB.LoadBalancerID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.LB.LoadBalancerName
	resource.OriginalLabels = lo.Map(original.LB.Tags.Tag,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Class = "mixed"
	resource.DeleteProtection = lo.ToPtr(strings.EqualFold(original.LB.DeleteProtection, "on"))
	if original.LB.AddressType == "intranet" {
		resource.PrivateIPList = []string{original.LB.Address}
	} else {
		resource.PublicIPList = []string{original.LB.Address}
	}
	resource.Status = lo.Ternary(strings.EqualFold(original.LB.LoadBalancerStatus, "active"), "active", "inactive")
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.LB.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.LB.VSwitchID),
			TargetUID: resource.UID,
		},
	})

	resource.Servers = append(resource.Servers, lo.Map(original.Servers, func(e SlbServer, _ int) *model.LbServerGraph {
		var class string
		if strings.HasPrefix(e.ServerID, "i-") {
			class = "ecs"
		} else if strings.HasPrefix(e.ServerID, "eni-") {
			class = "eni"
		}
		return &model.LbServerGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, class, lo.CoalesceOrEmpty(e.ServerID, e.ServerIP)),
				TargetUID: resource.UID,
			},
			InstanceID: e.ServerID,
			Class:      class,
			IP:         e.ServerIP,
			Port:       e.Port,
		}
	})...)
	resource.Servers = append(resource.Servers, lo.Map(original.VServers, func(e SlbVServer, _ int) *model.LbServerGraph {
		class := strings.ToLower(e.Type)
		return &model.LbServerGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, class, e.ServerID),
				TargetUID: resource.UID,
			},
			InstanceID: e.ServerID,
			Class:      class,
			IP:         e.ServerIP,
			Port:       e.Port,
			Weight:     e.Weight,
		}
	})...)
	return resource, nil
}
