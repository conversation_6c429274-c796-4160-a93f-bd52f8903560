package lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var nlbLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nlb"})

func NewNLBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_nlb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformNLB,
		UpdateResources: updateNLBResources,
	}
}

func transformNLB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseNLB(assetMsg)
		if err != nil {
			nlbLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateNLBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type Nlb struct {
	LB        NlbClass      `json:"lb"`
	Listeners []NlbListener `json:"listeners"`
	Servers   []NlbServer   `json:"servers"`
}

type NlbClass struct {
	AddressIPVersion             string                       `json:"AddressIpVersion"`
	AddressType                  string                       `json:"AddressType"`
	CPS                          int64                        `json:"Cps"`
	CreateTime                   string                       `json:"CreateTime"`
	CrossZoneEnabled             bool                         `json:"CrossZoneEnabled"`
	DNSName                      string                       `json:"DNSName"`
	DeletionProtectionConfig     DeletionProtectionConfig     `json:"DeletionProtectionConfig"`
	Ipv6AddressType              string                       `json:"Ipv6AddressType"`
	LoadBalancerBillingConfig    LoadBalancerBillingConfig    `json:"LoadBalancerBillingConfig"`
	LoadBalancerBusinessStatus   string                       `json:"LoadBalancerBusinessStatus"`
	LoadBalancerID               string                       `json:"LoadBalancerId"`
	LoadBalancerName             string                       `json:"LoadBalancerName"`
	LoadBalancerStatus           string                       `json:"LoadBalancerStatus"`
	LoadBalancerType             string                       `json:"LoadBalancerType"`
	ModificationProtectionConfig ModificationProtectionConfig `json:"ModificationProtectionConfig"`
	RegionID                     string                       `json:"RegionId"`
	RequestID                    string                       `json:"RequestId"`
	ResourceGroupID              string                       `json:"ResourceGroupId"`
	Tags                         []Tag                        `json:"Tags"`
	VpcID                        string                       `json:"VpcId"`
	ZoneMappings                 []ZoneMapping                `json:"ZoneMappings"`
	SecurityGroupIDS             []string                     `json:"SecurityGroupIds"`
}

type DeletionProtectionConfig struct {
	Enabled bool `json:"Enabled"`
}

type LoadBalancerBillingConfig struct {
	PayType string `json:"PayType"`
}

type ModificationProtectionConfig struct {
	Status string `json:"Status"`
}

type ZoneMapping struct {
	LoadBalancerAddresses []LoadBalancerAddress `json:"LoadBalancerAddresses"`
	Status                string                `json:"Status"`
	VSwitchID             string                `json:"VSwitchId"`
	ZoneID                string                `json:"ZoneId"`
}

type LoadBalancerAddress struct {
	EniID               string   `json:"EniId"`
	Ipv4LocalAddresses  []string `json:"Ipv4LocalAddresses"`
	PrivateIPv4Address  string   `json:"PrivateIPv4Address"`
	PrivateIPv4HcStatus string   `json:"PrivateIPv4HcStatus"`
	AllocationID        string   `json:"AllocationId"`
	PublicIPv4Address   string   `json:"PublicIPv4Address"`
	Ipv6Address         string   `json:"Ipv6Address"`
	PrivateIPv6HcStatus string   `json:"PrivateIPv6HcStatus,"`
	Ipv6LocalAddresses  []string `json:"Ipv6LocalAddresses"`
}

type NlbListener struct {
	AlpnEnabled           bool     `json:"AlpnEnabled"`
	CAEnabled             bool     `json:"CaEnabled"`
	CPS                   int64    `json:"Cps"`
	IdleTimeout           int64    `json:"IdleTimeout"`
	ListenerID            string   `json:"ListenerId"`
	ListenerPort          int      `json:"ListenerPort"`
	ListenerProtocol      string   `json:"ListenerProtocol"`
	ListenerStatus        string   `json:"ListenerStatus"`
	LoadBalancerID        string   `json:"LoadBalancerId"`
	Mss                   int64    `json:"Mss"`
	ProxyProtocolEnabled  bool     `json:"ProxyProtocolEnabled"`
	ProxyProtocolV2Config any      `json:"ProxyProtocolV2Config"`
	RegionID              string   `json:"RegionId"`
	SECSensorEnabled      bool     `json:"SecSensorEnabled"`
	ServerGroupID         string   `json:"ServerGroupId"`
	Tags                  []Tag    `json:"Tags"`
	StartPort             string   `json:"StartPort"`
	EndPort               string   `json:"EndPort"`
	ListenerDescription   string   `json:"ListenerDescription"`
	SecurityPolicyID      string   `json:"SecurityPolicyId"`
	CertificateIDS        []string `json:"CertificateIds"`
	CACertificateIDS      []string `json:"CaCertificateIds"`
	AlpnPolicy            string   `json:"AlpnPolicy"`
}

type NlbServer struct {
	Port          int64  `json:"Port"`
	ServerGroupID string `json:"ServerGroupId"`
	ServerID      string `json:"ServerId"`
	ServerIP      string `json:"ServerIp"`
	ServerType    string `json:"ServerType"`
	Status        string `json:"Status"`
	Weight        int64  `json:"Weight"`
	ZoneID        string `json:"ZoneId"`
}

func parseNLB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &Nlb{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.LB.LoadBalancerID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.LB.LoadBalancerID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.LB.LoadBalancerName
	resource.OriginalLabels = lo.Map(original.LB.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Class = "network"
	resource.DeleteProtection = lo.ToPtr(original.LB.DeletionProtectionConfig.Enabled)
	resource.Status = lo.Ternary(strings.EqualFold(original.LB.LoadBalancerStatus, "active"), "active", "inactive")

	ipv6Type := original.LB.Ipv6AddressType
	for _, zoneMapping := range original.LB.ZoneMappings {
		for _, address := range zoneMapping.LoadBalancerAddresses {
			if len(address.PublicIPv4Address) > 0 {
				resource.PublicIPList = append(resource.PublicIPList, address.PublicIPv4Address)
			}
			if len(address.PrivateIPv4Address) > 0 {
				resource.PrivateIPList = append(resource.PrivateIPList, address.PrivateIPv4Address)
			}
			if len(address.Ipv6Address) > 0 {
				if ipv6Type == "intranet" {
					resource.PrivateIPList = append(resource.PrivateIPList, address.Ipv6Address)
				} else {
					resource.PublicIPList = append(resource.PublicIPList, address.Ipv6Address)
				}
			}
		}

		if len(zoneMapping.VSwitchID) > 0 {
			resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", zoneMapping.VSwitchID),
					TargetUID: resource.UID,
				},
			})
		}
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.LB.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = lo.Map(original.LB.SecurityGroupIDS, func(e string, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e),
				TargetUID: resource.UID,
			},
		}
	})
	resource.Servers = lo.Map(original.Servers, func(e NlbServer, _ int) *model.LbServerGraph {
		var class = strings.ToLower(e.ServerType)
		return &model.LbServerGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, class, lo.CoalesceOrEmpty(e.ServerID, e.ServerIP)),
				TargetUID: resource.UID,
			},
			InstanceID: e.ServerID,
			Class:      class,
			IP:         e.ServerIP,
			Port:       int(e.Port),
			Weight:     int(e.Weight),
		}
	})
	return resource, nil
}
