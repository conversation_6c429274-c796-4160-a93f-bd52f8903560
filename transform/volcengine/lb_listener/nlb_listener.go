package lb_listener

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var nlbListenerLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nlb_listener"})

func NewNLBListenerService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_nlbListener_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformNLBListener,
		UpdateResources: updateNLBListeners,
	}
}

func transformNLBListener(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseNLBListener(assetMsg)
		if err != nil {
			nlbListenerLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_listener_list"] = append(resourceData["lb_listener_list"], utils.GenParamsFromStruct(resource))
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["port_range_list"] = append(resourceData["port_range_list"],
			utils.GenParamsFromStructSlice(resource.PortRange)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateNLBListeners(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbListenerSchema, resourceData["lb_listener_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbFromListenerSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, portRangeSchema, resourceData["port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})

}

type NlbListenerAgg struct {
	Listener NlbListener `json:"listener"`
}

type NlbListener struct {
	AlpnEnabled           bool     `json:"AlpnEnabled"`
	CAEnabled             bool     `json:"CaEnabled"`
	CPS                   int64    `json:"Cps"`
	IdleTimeout           int64    `json:"IdleTimeout"`
	ListenerID            string   `json:"ListenerId"`
	ListenerPort          int      `json:"ListenerPort"`
	ListenerProtocol      string   `json:"ListenerProtocol"`
	ListenerStatus        string   `json:"ListenerStatus"`
	LoadBalancerID        string   `json:"LoadBalancerId"`
	Mss                   int64    `json:"Mss"`
	ProxyProtocolEnabled  bool     `json:"ProxyProtocolEnabled"`
	ProxyProtocolV2Config any      `json:"ProxyProtocolV2Config"`
	RegionID              string   `json:"RegionId"`
	SECSensorEnabled      bool     `json:"SecSensorEnabled"`
	ServerGroupID         string   `json:"ServerGroupId"`
	Tags                  []Tag    `json:"Tags"`
	StartPort             string   `json:"StartPort"`
	EndPort               string   `json:"EndPort"`
	ListenerDescription   string   `json:"ListenerDescription"`
	SecurityPolicyID      string   `json:"SecurityPolicyId"`
	CertificateIDS        []string `json:"CertificateIds"`
	CACertificateIDS      []string `json:"CaCertificateIds"`
	AlpnPolicy            string   `json:"AlpnPolicy"`
}

func parseNLBListener(assetMsg *model.AssetMessage) (*model.LbListenerGraph, error) {
	original := &NlbListenerAgg{}
	resource := &model.LbListenerGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Listener.ListenerID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb-listener", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb-listener"
	resource.Name = fmt.Sprintf("%s@%d/%s", original.Listener.LoadBalancerID, original.Listener.ListenerPort, original.Listener.ListenerProtocol)
	resource.Description = original.Listener.ListenerDescription
	resource.OriginalLabels = lo.Map(original.Listener.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Protocol = strings.ToLower(original.Listener.ListenerProtocol)
	resource.Status = lo.Ternary(strings.EqualFold(original.Listener.ListenerStatus, "Running"), "active", "inactive")
	if len(original.Listener.CertificateIDS) > 0 {
		resource.CertExists = lo.ToPtr(true)
	}

	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.Listener.LoadBalancerID),
			TargetUID: resource.UID,
		},
	})
	resource.TargetUID = utils.GenerateUID(resource.Provider, "lb", original.Listener.LoadBalancerID)
	if original.Listener.ListenerPort != 0 {
		resource.PortRange = append(resource.PortRange, utils.NewPortRange(original.Listener.ListenerPort, original.Listener.ListenerPort, resource.UID))
	} else {
		startPort, _ := strconv.Atoi(original.Listener.StartPort)
		endPort, _ := strconv.Atoi(original.Listener.EndPort)
		resource.PortRange = append(resource.PortRange, utils.NewPortRange(startPort, endPort, resource.UID))
	}

	return resource, nil
}
