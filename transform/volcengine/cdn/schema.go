package cdn

import "AssetStandardizer/graph"

var cdnSchema = graph.NodeSchema{
	Label: "CDN",
	Properties: map[string]graph.PropertyRef{
		"uid":                     {Name: "uid"},
		"provider":                {Name: "provider"},
		"original_id":             {Name: "original_id"},
		"transformed_object":      {Name: "transformed_object"},
		"region":                  {Name: "region"},
		"last_seen":               {Name: "last_seen"},
		"description":             {Name: "description"},
		"kind":                    {Name: "kind"},
		"name":                    {Name: "name"},
		"status":                  {Name: "status"},
		"https_enabled":           {Name: "https_enabled"},
		"cname":                   {Name: "cname"},
		"coverage":                {Name: "coverage"},
		"class":                   {Name: "class"},
		"force_https":             {Name: "force_https"},
		"allow_empty_referer":     {Name: "allow_empty_referer"},
		"referer_white_list":      {Name: "referer_white_list"},
		"referer_black_list":      {Name: "referer_black_list"},
		"ip_white_list":           {Name: "ip_white_list"},
		"ip_black_list":           {Name: "ip_black_list"},
		"rate_limit_enabled":      {Name: "rate_limit_enabled"},
		"freq_limit_enabled":      {Name: "freq_limit_enabled"},
		"bandwidth_limit_enabled": {Name: "bandwidth_limit_enabled"},
	},
}

var labelSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"key":          {Name: "key"},
		"value":        {Name: "value"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CDN",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_LABEL",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}