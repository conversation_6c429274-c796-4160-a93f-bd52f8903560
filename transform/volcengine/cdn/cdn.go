package cdn

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	"AssetStandardizer/transform/utils"
	provider_utils "AssetStandardizer/transform/volcengine/utils"
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cdn"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cdn_aggregated", // FIXME: Verify the correct message type for volcengine CDN
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cdn_list"] = append(resourceData["cdn_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}
func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cdnSchema, resourceData["cdn_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type CDN struct {
	CDNDomain    CDNDomain    `json:"cdnDomain"`
	DomainConfig DomainConfig `json:"domainConfig"`
}

type CDNDomain struct {
	BackupCname           interface{} `json:"BackupCname"`
	BackupOrigin          interface{} `json:"BackupOrigin"`
	CacheShared           string      `json:"CacheShared"`
	CacheSharedTargetHost string      `json:"CacheSharedTargetHost"`
	Cname                 string      `json:"Cname"`
	ConfigStatus          string      `json:"ConfigStatus"`
	CreateTime            int64       `json:"CreateTime"`
	Domain                string      `json:"Domain"`
	DomainLock            DomainLock  `json:"DomainLock"`
	FeatureConfig         interface{} `json:"FeatureConfig"`
	HTTPS                 bool        `json:"HTTPS"`
	IPv6                  bool        `json:"IPv6"`
	IsConflictDomain      bool        `json:"IsConflictDomain"`
	OriginProtocol        string      `json:"OriginProtocol"`
	PrimaryOrigin         []string    `json:"PrimaryOrigin"`
	Project               string      `json:"Project"`
	ResourceTags          []string    `json:"ResourceTags"`
	ServiceRegion         string      `json:"ServiceRegion"`
	ServiceType           string      `json:"ServiceType"`
	Status                string      `json:"Status"`
	UpdateTime            int64       `json:"UpdateTime"`
}

type DomainLock struct {
	Remark string `json:"Remark"`
	Status string `json:"Status"`
}

type DomainConfig struct {
	Metadata         Metadata          `json:"Metadata"`
	DomainConfig     DomainConfigClass `json:"DomainConfig"`
	FeatureConfig    interface{}       `json:"FeatureConfig"`
	ModuleLockConfig interface{}       `json:"ModuleLockConfig"`
}

type DomainConfigClass struct {
	Origin             []Origin           `json:"Origin"`
	HTTPForcedRedirect ForcedRedirect     `json:"HttpForcedRedirect"`
	RefererAccessRule  RefererAccessRule  `json:"RefererAccessRule"`
	IPAccessRule       IPAccessRule       `json:"IpAccessRule"`
	DownloadSpeedLimit DownloadSpeedLimit `json:"DownloadSpeedLimit"`
	BandwidthLimit     BandwidthLimit     `json:"BandwidthLimit"`
}

type Origin struct {
	Condition    interface{}  `json:"Condition"`
	OriginAction OriginAction `json:"OriginAction"`
}

type OriginAction struct {
	OriginLines []OriginLine `json:"OriginLines"`
}

type OriginLine struct {
	Address             string      `json:"Address"`
	BucketName          interface{} `json:"BucketName"`
	HTTPPort            string      `json:"HttpPort"`
	HTTPSPort           string      `json:"HttpsPort"`
	InstanceType        string      `json:"InstanceType"`
	OriginHost          string      `json:"OriginHost"`
	OriginType          string      `json:"OriginType"`
	PrivateBucketAccess bool        `json:"PrivateBucketAccess"`
	PrivateBucketAuth   interface{} `json:"PrivateBucketAuth"`
	Region              string      `json:"Region"`
	Weight              string      `json:"Weight"`
}

type ForcedRedirect struct {
	EnableForcedRedirect bool   `json:"EnableForcedRedirect"`
	StatusCode           string `json:"StatusCode"`
}

type RefererAccessRule struct {
	AllowEmpty   bool         `json:"AllowEmpty"`
	ReferersType ReferersType `json:"ReferersType"`
	RuleType     string       `json:"RuleType"`
	Switch       bool         `json:"Switch"`
}

type ReferersType struct {
	CommonType  CommonType  `json:"CommonType"`
	RegularType RegularType `json:"RegularType"`
}

type CommonType struct {
	IgnoreCase   bool     `json:"IgnoreCase"`
	IgnoreScheme bool     `json:"IgnoreScheme"`
	Referers     []string `json:"Referers"`
}

type RegularType struct {
	Referers []string `json:"Referers"`
}

type IPAccessRule struct {
	Switch   bool     `json:"Switch"`
	RuleType string   `json:"RuleType"`
	IP       []string `json:"Ip"`
}

type DownloadSpeedLimit struct {
	DownloadSpeedLimitRules interface{} `json:"DownloadSpeedLimitRules"`
	Switch                  bool        `json:"Switch"`
}

type BandwidthLimit struct {
	BandwidthLimitRule BandwidthLimitRule `json:"BandwidthLimitRule"`
	Switch             bool               `json:"Switch"`
}

type BandwidthLimitRule struct {
	BandwidthLimitAction BandwidthLimitAction `json:"BandwidthLimitAction"`
}

type BandwidthLimitAction struct {
	BandwidthThreshold int64  `json:"BandwidthThreshold"`
	LimitType          string `json:"LimitType"`
	SpeedLimitRate     int64  `json:"SpeedLimitRate"`
}

type Metadata struct {
	RequestID string      `json:"RequestId"`
	Action    string      `json:"Action"`
	Version   string      `json:"Version"`
	Service   string      `json:"Service"`
	Region    string      `json:"Region"`
	HTTPCode  int64       `json:"HTTPCode"`
	Error     interface{} `json:"Error"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CDNGraph, error) {
	original := &CDN{}
	resource := &model.CDNGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.CDNDomain.Domain
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "cdn", original.CDNDomain.Domain)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cdn"
	resource.Name = original.CDNDomain.Domain

	resource.OriginalLabels = lo.FilterMap(original.CDNDomain.ResourceTags, func(tag string, _ int) (*model.KVGraph, bool) {
		kv := strings.SplitN(tag, ":", 2)
		if len(kv) == 2 {
			return utils.NewLabel(kv[0], kv[1], resource.UID), true
		}
		return nil, false
	})

	resource.Status = lo.Ternary(strings.ToLower(original.CDNDomain.Status) == "online", "running", "stopped")
	resource.HTTPSEnabled = original.CDNDomain.HTTPS
	resource.CNAME = original.CDNDomain.Cname
	resource.Coverage = "domestic"
	resource.Class = strings.ToLower(original.CDNDomain.ServiceType)

	for _, origin := range original.DomainConfig.DomainConfig.Origin {
		for _, originLine := range origin.OriginAction.OriginLines {
			weight, _ := strconv.Atoi(originLine.Weight)
			resource.SourceSite = append(resource.SourceSite, &model.SourceSiteGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "source-site", originLine.Address),
					TargetUID: resource.UID,
				},
				URL:    originLine.Address,
				Weight: weight,
			})
		}
	}

	resource.ForceHTTPS = original.DomainConfig.DomainConfig.HTTPForcedRedirect.EnableForcedRedirect

	if original.DomainConfig.DomainConfig.RefererAccessRule.Switch {
		resource.AllowEmptyReferer = original.DomainConfig.DomainConfig.RefererAccessRule.AllowEmpty

		switch original.DomainConfig.DomainConfig.RefererAccessRule.RuleType {
		case "allow":
			resource.RefererWhiteList = append(resource.RefererWhiteList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.CommonType.Referers...)
			resource.RefererWhiteList = append(resource.RefererWhiteList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.RegularType.Referers...)
		case "deny":
			resource.RefererBlackList = append(resource.RefererBlackList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.CommonType.Referers...)
			resource.RefererBlackList = append(resource.RefererBlackList, original.DomainConfig.DomainConfig.RefererAccessRule.ReferersType.RegularType.Referers...)
		}
	}

	if original.DomainConfig.DomainConfig.IPAccessRule.Switch {
		switch original.DomainConfig.DomainConfig.IPAccessRule.RuleType {
		case "allow":
			resource.IPWhiteList = lo.Map(original.DomainConfig.DomainConfig.IPAccessRule.IP, func(ip string, _ int) string { return provider_utils.FormatCIDR(ip) })
		case "deny":
			resource.IPBlackList = lo.Map(original.DomainConfig.DomainConfig.IPAccessRule.IP, func(ip string, _ int) string { return provider_utils.FormatCIDR(ip) })
		}
	}

	resource.RateLimitEnabled = original.DomainConfig.DomainConfig.DownloadSpeedLimit.Switch
	resource.FreqLimitEnabled = false // not supported by volcengine
	resource.BandwidthLimitEnabled = original.DomainConfig.DomainConfig.BandwidthLimit.Switch

	return resource, nil
}
