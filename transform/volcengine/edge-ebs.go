package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"
)

type EdgeVolume struct {
	Volume EdgeVolumeClass `json:"volume"`
}

type EdgeVolumeClass struct {
	AccountID     int64          `json:"account_id"`
	EbsID         string         `json:"ebs_id"`
	EbsName       string         `json:"ebs_name"`
	Status        string         `json:"status"`
	Cluster       EdgeCluster    `json:"cluster"`
	StorageType   string         `json:"storage_type"`
	Capacity      string         `json:"capacity"`
	EbsType       string         `json:"ebs_type"`
	ChargeType    string         `json:"charge_type"`
	Desc          string         `json:"desc"`
	Attachment    EdgeAttachment `json:"attachment"`
	DeleteWithRes bool           `json:"delete_with_res"`
	Project       string         `json:"project"`
	CreateByRes   bool           `json:"create_by_res"`
	StatusReason  string         `json:"status_reason"`
	StartTime     int64          `json:"start_time"`
	CreateTime    int64          `json:"create_time"`
	UpdateTime    int64          `json:"update_time"`
	PvcNS         string         `json:"pvc_ns"`
	PvcName       string         `json:"pvc_name"`
}

type EdgeAttachment struct {
	EbsID   string `json:"ebs_id"`
	ResType string `json:"res_type"`
	ResID   string `json:"res_id"`
	ResName string `json:"res_name"`
}

func parseEdgeVolume(raw map[string]any) model.Asset {
	original := &EdgeVolume{}
	meta := &model.ObjectMeta{}
	ebs := &model.EBS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Volume.EbsID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "ebs", original.Volume.EbsID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ebs"
	meta.Name = original.Volume.EbsName
	meta.Description = original.Volume.Desc

	ebs.Class = "edge-volume"
	ebs.Status = utils.UnwrapOr("inuse", original.Volume.Status == "attached", "available")
	ebs.AutoSnapshotEnabled = utils.DataPointer(false)
	ebs.ECSIDs = []string{utils.GenerateUID(ProviderID, "edge-ecs", original.Volume.Attachment.ResID)}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ebs)
	return asset
}

type EdgeImage struct {
	Image EdgeImageClass `json:"image"`
}

type EdgeImageClass struct {
	ID                    string         `json:"id"`
	ParentID              string         `json:"parent_id"`
	DiskSize              int64          `json:"disk_size"`
	Name                  string         `json:"name"`
	Description           string         `json:"description"`
	Spec                  EdgeImageSpec  `json:"spec"`
	Property              string         `json:"property"`
	Status                string         `json:"status"`
	IsEnable              bool           `json:"is_enable"`
	ImportMethod          string         `json:"import_method"`
	CreateTime            int64          `json:"create_time"`
	UpdateTime            int64          `json:"update_time"`
	LatestVersion         string         `json:"latest_version"`
	ParentVMID            string         `json:"parent_vm_id"`
	SharedCount           int64          `json:"shared_count"`
	Label                 map[string]any `json:"label"`
	SharedAccountIdentity int64          `json:"shared_account_identity"`
}

type EdgeImageSpec struct {
	SystemArch      string `json:"system_arch"`
	SystemBit       string `json:"system_bit"`
	SystemType      string `json:"system_type"`
	SystemVersion   string `json:"system_version"`
	DiskSize        int64  `json:"disk_size"`
	SystemVersionID string `json:"system_version_id"`
}

func parseEdgeImage(raw map[string]any) model.Asset {
	original := &EdgeImage{}
	meta := &model.ObjectMeta{}
	ebs := &model.EBS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Image.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "ebs", original.Image.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ebs"
	meta.Name = original.Image.Name
	meta.Description = original.Image.Description

	ebs.Class = "edge-image"
	ebs.Status = utils.UnwrapOr("available", strings.EqualFold(original.Image.Status, "online"), "unavailable")

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ebs)
	return asset
}
