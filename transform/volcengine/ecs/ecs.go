package ecs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type ECS struct {
	Enis     []Eni       `json:"enis"`
	Instance EcsInstance `json:"instance"`
}

type Eni struct {
	AccountID            string              `json:"AccountId"`
	AssociatedElasticIP  AssociatedElasticIP `json:"AssociatedElasticIp"`
	CreatedAt            string              `json:"CreatedAt"`
	Description          string              `json:"Description"`
	DeviceID             string              `json:"DeviceId"`
	IPv6Sets             []string            `json:"IPv6Sets"`
	MACAddress           string              `json:"MacAddress"`
	NetworkInterfaceID   string              `json:"NetworkInterfaceId"`
	NetworkInterfaceName string              `json:"NetworkInterfaceName"`
	PortSecurityEnabled  bool                `json:"PortSecurityEnabled"`
	PrimaryIPAddress     string              `json:"PrimaryIpAddress"`
	PrivateIPSets        PrivateIPSets       `json:"PrivateIpSets"`
	ProjectName          string              `json:"ProjectName"`
	SecurityGroupIDS     []string            `json:"SecurityGroupIds"`
	ServiceManaged       bool                `json:"ServiceManaged"`
	Status               string              `json:"Status"`
	SubnetID             string              `json:"SubnetId"`
	Tags                 []Tag               `json:"Tags"`
	Type                 string              `json:"Type"`
	UpdatedAt            string              `json:"UpdatedAt"`
	VpcID                string              `json:"VpcId"`
	VpcName              string              `json:"VpcName"`
	ZoneID               string              `json:"ZoneId"`
}

type AssociatedElasticIP struct {
	AllocationID        string `json:"AllocationId"`
	EipAddress          string `json:"EipAddress"`
	ReleaseWithInstance bool   `json:"ReleaseWithInstance"`
}

type PrivateIPSets struct {
	PrivateIPSet []PrivateIPSet `json:"PrivateIpSet"`
}

type PrivateIPSet struct {
	AssociatedElasticIP AssociatedElasticIP `json:"AssociatedElasticIp"`
	Primary             bool                `json:"Primary"`
	PrivateIPAddress    string              `json:"PrivateIpAddress"`
}

type EcsInstance struct {
	CPUOptions                   CPUOptions         `json:"CpuOptions"`
	Cpus                         int64              `json:"Cpus"`
	CreatedAt                    string             `json:"CreatedAt"`
	DeploymentSetGroupNumber     int64              `json:"DeploymentSetGroupNumber"`
	DeploymentSetID              string             `json:"DeploymentSetId"`
	Description                  string             `json:"Description"`
	EipAddress                   interface{}        `json:"EipAddress"`
	ElasticScheduledInstanceType string             `json:"ElasticScheduledInstanceType"`
	ExpiredAt                    string             `json:"ExpiredAt"`
	HostName                     interface{}        `json:"HostName"`
	Hostname                     string             `json:"Hostname"`
	HPCClusterID                 string             `json:"HpcClusterId"`
	ImageID                      string             `json:"ImageId"`
	InstanceChargeType           string             `json:"InstanceChargeType"`
	InstanceID                   string             `json:"InstanceId"`
	InstanceName                 string             `json:"InstanceName"`
	InstanceTypeID               string             `json:"InstanceTypeId"`
	KeyPairID                    string             `json:"KeyPairId"`
	KeyPairName                  string             `json:"KeyPairName"`
	LocalVolumes                 []LocalVolume      `json:"LocalVolumes"`
	MemorySize                   int64              `json:"MemorySize"`
	NetworkInterfaces            []NetworkInterface `json:"NetworkInterfaces"`
	OSName                       string             `json:"OsName"`
	OSType                       string             `json:"OsType"`
	Placement                    Placement          `json:"Placement"`
	ProjectName                  string             `json:"ProjectName"`
	RdmaIPAddresses              []string           `json:"RdmaIpAddresses"`
	ScheduledInstanceID          string             `json:"ScheduledInstanceId"`
	SpotPriceLimit               int64              `json:"SpotPriceLimit"`
	SpotStrategy                 string             `json:"SpotStrategy"`
	Status                       string             `json:"Status"`
	StoppedMode                  string             `json:"StoppedMode"`
	Tags                         []Tag              `json:"Tags"`
	UpdatedAt                    string             `json:"UpdatedAt"`
	UUID                         string             `json:"Uuid"`
	VpcID                        string             `json:"VpcId"`
	ZoneID                       string             `json:"ZoneId"`
}

type CPUOptions struct {
	CoreCount      int64 `json:"CoreCount"`
	ThreadsPerCore int64 `json:"ThreadsPerCore"`
}

type LocalVolume struct {
	Count      int64  `json:"Count"`
	Size       int64  `json:"Size"`
	VolumeType string `json:"VolumeType"`
}

type NetworkInterface struct {
	Ipv6Addresses      []interface{} `json:"Ipv6Addresses"`
	MACAddress         string        `json:"MacAddress"`
	NetworkInterfaceID string        `json:"NetworkInterfaceId"`
	PrimaryIPAddress   string        `json:"PrimaryIpAddress"`
	SubnetID           string        `json:"SubnetId"`
	Type               string        `json:"Type"`
	VpcID              string        `json:"VpcId"`
}

type Placement struct {
	Affinity               string `json:"Affinity"`
	DedicatedHostClusterID string `json:"DedicatedHostClusterId"`
	DedicatedHostID        string `json:"DedicatedHostId"`
	Tenancy                string `json:"Tenancy"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &ECS{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Instance.Description
	resource.Kind = "ecs"
	resource.Name = original.Instance.InstanceName
	resource.OriginalLabels = lo.Map(original.Instance.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Hostname = original.Instance.Hostname
	resource.Class = "cvm"
	resource.OSName = original.Instance.OSName
	osType := strings.ToLower(original.Instance.OSType)
	resource.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")
	resource.Spec = original.Instance.InstanceTypeID
	resource.Status = strings.ToLower(original.Instance.Status)
	resource.DeleteProtection = lo.ToPtr(false)

	primaryEni, found := lo.Find(original.Enis, func(e Eni) bool { return e.Type == "primary" })
	if found {
		primaryIpset, found := lo.Find(primaryEni.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet) bool { return e.Primary })
		if found {
			resource.PrimaryPrivateIP = primaryIpset.PrivateIPAddress
			resource.PrimaryPublicIP = primaryIpset.AssociatedElasticIP.EipAddress
		}
	}
	resource.PrivateIPList = lo.Flatten(lo.Map(original.Enis, func(e Eni, _ int) []string {
		return lo.Map(e.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet, _ int) string { return e.PrivateIPAddress })
	}))
	resource.PublicIPList = lo.Flatten(lo.Map(original.Enis, func(e Eni, _ int) []string {
		return lo.FilterMap(e.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet, _ int) (string, bool) {
			return e.AssociatedElasticIP.EipAddress, e.AssociatedElasticIP.EipAddress != ""
		})
	}))

	var vpcIds, subnetIds, sgIds, eniIds []string
	for _, eni := range original.Enis {
		vpcIds = append(vpcIds, eni.VpcID)
		subnetIds = append(subnetIds, eni.SubnetID)
		sgIds = append(sgIds, eni.SecurityGroupIDS...)
		eniIds = append(eniIds, eni.NetworkInterfaceID)
	}

	resource.VPC = append(resource.VPC, lo.Map(lo.Uniq(vpcIds), func(e string, _ int) *model.VPCGraph {
		return &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", e),
				TargetUID: resource.UID,
			},
		}
	})...)
	resource.Subnet = append(resource.Subnet, lo.Map(lo.Uniq(subnetIds), func(e string, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", e),
				TargetUID: resource.UID,
			},
		}
	})...)
	resource.SG = append(resource.SG, lo.Map(lo.Uniq(sgIds), func(e string, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e),
				TargetUID: resource.UID,
			},
		}
	})...)
	resource.ENI = append(resource.ENI, lo.Map(lo.Uniq(eniIds), func(e string, _ int) *model.ENIGraph {
		return &model.ENIGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eni", e),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
