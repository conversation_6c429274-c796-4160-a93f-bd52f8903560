package lb

import "AssetStandardizer/graph"

var lbSchema = graph.NodeSchema{
	Label: "LoadBalancer",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"class":              {Name: "class"},
		"status":             {Name: "status"},
		"delete_protection":  {Name: "delete_protection"},
		"private_ip_list":    {Name: "private_ip_list"},
		"public_ip_list":     {Name: "public_ip_list"},
	},
}

var labelLbSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"key":          {Name: "key"},
		"value":        {Name: "value"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LABEL",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcLbSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var subnetLbSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "lb_id"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var sgLbSchema = graph.NodeSchema{
	Label: "SecurityGroup",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ATTACHED_TO",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var ecsLbSchema = graph.NodeSchema{
	Label: "ECS",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "SERVES",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"uid":          {Name: "uid"},
				"instance_id":  {Name: "instance_id"},
				"class":        {Name: "class"},
				"ip":           {Name: "ip"},
				"port":         {Name: "port"},
				"weight":       {Name: "weight"},
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var eniLbSchema = graph.NodeSchema{
	Label: "ENI",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "SERVES",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"uid":          {Name: "uid"},
				"instance_id":  {Name: "instance_id"},
				"class":        {Name: "class"},
				"ip":           {Name: "ip"},
				"port":         {Name: "port"},
				"weight":       {Name: "weight"},
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var ipLbSchema = graph.NodeSchema{
	Label: "NetworkAddress",
	Properties: map[string]graph.PropertyRef{
		"uid":     {Name: "uid"},
		"address": {Name: "ip"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "SERVES",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"uid":          {Name: "uid"},
				"instance_id":  {Name: "instance_id"},
				"class":        {Name: "class"},
				"ip":           {Name: "ip"},
				"port":         {Name: "port"},
				"weight":       {Name: "weight"},
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
