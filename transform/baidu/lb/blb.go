package lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var blbLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "blb"})

func NewBLBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_blb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformBLB,
		UpdateResources: updateBLBResources,
	}
}

func transformBLB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseBLB(assetMsg)
		if err != nil {
			blbLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateBLBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type BLB struct {
	BlbBackendServers           []BlbBackendServer           `json:"blbBackendServers"`
	BlbDetail                   BlbDetail                    `json:"blbDetail"`
	BlbEnterpriseSecurityGroups []BlbEnterpriseSecurityGroup `json:"blbEnterpriseSecurityGroups"`
	BlbListeners                []BlbWithListener            `json:"blbListeners"`
	BlbSecurityGroups           []BlbSecurityGroup           `json:"blbSecurityGroups"`
}

type BlbBackendServer struct {
	InstanceID string `json:"instanceId"`
	Weight     int    `json:"weight"`
	PrivateIP  string `json:"privateIp"`
}

type BlbDetail struct {
	BlbID                  string              `json:"blbId"`
	Status                 string              `json:"status"`
	Name                   string              `json:"name"`
	Desc                   string              `json:"desc"`
	Address                string              `json:"address"`
	PublicIP               string              `json:"publicIp"`
	CIDR                   string              `json:"cidr"`
	VpcName                string              `json:"vpcName"`
	CreateTime             string              `json:"createTime"`
	Layer4ClusterID        string              `json:"layer4ClusterId"`
	Layer7ClusterID        string              `json:"layer7ClusterId"`
	Listener               []BlbDetailListener `json:"listener"`
	Tags                   []Tag               `json:"tags"`
	EipRouteType           string              `json:"eipRouteType"`
	Layer4ClusterExclusive bool                `json:"layer4ClusterExclusive"`
	Layer7ClusterExclusive bool                `json:"layer7ClusterExclusive"`
	Layer4ClusterMode      string              `json:"layer4ClusterMode"`
	Layer7ClusterMode      string              `json:"layer7ClusterMode"`
	Layer4MasterAz         string              `json:"layer4MasterAz"`
	Layer7MasterAz         string              `json:"layer7MasterAz"`
	Layer4SlaveAz          string              `json:"layer4SlaveAz"`
	Layer7SlaveAz          string              `json:"layer7SlaveAz"`
	PaymentTiming          string              `json:"paymentTiming"`
	PerformanceLevel       string              `json:"performanceLevel"`
	ExpireTime             string              `json:"expireTime"`
	AllowDelete            bool                `json:"allowDelete"`
	VpcID                  string              `json:"vpcId"`
}

type BlbDetailListener struct {
	Port string `json:"port"`
	Type string `json:"type"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type BlbWithListener struct {
	ListenerPort               int64       `json:"listenerPort"`
	ListenerType               string      `json:"listenerType"`
	BackendPort                int64       `json:"backendPort"`
	Scheduler                  string      `json:"scheduler"`
	GetBlbIP                   bool        `json:"getBlbIp"`
	TCPSessionTimeout          int64       `json:"tcpSessionTimeout"`
	UDPSessionTimeout          int64       `json:"udpSessionTimeout"`
	HealthCheckString          string      `json:"healthCheckString"`
	KeepSession                bool        `json:"keepSession"`
	KeepSessionType            string      `json:"keepSessionType"`
	KeepSessionDuration        int64       `json:"keepSessionDuration"`
	KeepSessionCookieName      string      `json:"keepSessionCookieName"`
	XForwardFor                bool        `json:"xForwardFor"`
	XForwardedProto            bool        `json:"xForwardedProto"`
	HealthCheckType            string      `json:"healthCheckType"`
	HealthCheckPort            int64       `json:"healthCheckPort"`
	HealthCheckURI             string      `json:"healthCheckURI"`
	HealthCheckTimeoutInSecond int64       `json:"healthCheckTimeoutInSecond"`
	HealthCheckInterval        int64       `json:"healthCheckInterval"`
	UnhealthyThreshold         int64       `json:"unhealthyThreshold"`
	HealthyThreshold           int64       `json:"healthyThreshold"`
	HealthCheckNormalStatus    string      `json:"healthCheckNormalStatus"`
	HealthCheckHost            string      `json:"healthCheckHost"`
	ServerTimeout              int64       `json:"serverTimeout"`
	RedirectPort               int64       `json:"redirectPort"`
	CERTIDS                    interface{} `json:"certIds"`
	DualAuth                   bool        `json:"dualAuth"`
	ClientCERTIDS              interface{} `json:"clientCertIds"`
	EncryptionType             string      `json:"encryptionType"`
	EncryptionProtocols        interface{} `json:"encryptionProtocols"`
	AppliedCiphers             string      `json:"appliedCiphers"`
}

type BlbSecurityGroup struct {
	SecurityGroupDesc  string              `json:"securityGroupDesc"`
	SecurityGroupID    string              `json:"securityGroupId"`
	SecurityGroupName  string              `json:"securityGroupName"`
	VpcName            string              `json:"vpcName"`
	SecurityGroupRules []SecurityGroupRule `json:"securityGroupRules"`
}

type SecurityGroupRule struct {
	DestGroupID         string `json:"destGroupId"`
	DestIP              string `json:"destIp"`
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	SecurityGroupRuleID string `json:"securityGroupRuleId"`
}

type BlbEnterpriseSecurityGroup struct {
	EnterpriseSecurityGroupID    string                        `json:"enterpriseSecurityGroupId"`
	EnterpriseSecurityGroupName  string                        `json:"enterpriseSecurityGroupName"`
	EnterpriseSecurityGroupDesc  string                        `json:"enterpriseSecurityGroupDesc"`
	EnterpriseSecurityGroupRules []EnterpriseSecurityGroupRule `json:"enterpriseSecurityGroupRules"`
}

type EnterpriseSecurityGroupRule struct {
	Remark                        string `json:"remark"`
	Direction                     string `json:"direction"`
	Action                        string `json:"action"`
	Priority                      int64  `json:"priority"`
	Ethertype                     string `json:"ethertype"`
	PortRange                     string `json:"portRange"`
	SourceIP                      string `json:"sourceIp"`
	DestIP                        string `json:"destIp"`
	EnterpriseSecurityGroupRuleID string `json:"enterpriseSecurityGroupRuleId"`
	Protocol                      string `json:"protocol"`
}

func parseBLB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &BLB{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.BlbDetail.BlbID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.BlbDetail.BlbID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.BlbDetail.Name
	resource.Description = original.BlbDetail.Desc
	resource.OriginalLabels = lo.Map(original.BlbDetail.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Class = "mixed"
	resource.DeleteProtection = lo.ToPtr(!original.BlbDetail.AllowDelete)
	resource.Status = lo.Ternary(strings.EqualFold(original.BlbDetail.Status, "available"), "active", "inactive")

	if len(original.BlbDetail.PublicIP) > 0 {
		resource.PublicIPList = []string{original.BlbDetail.PublicIP}
	}
	if len(original.BlbDetail.Address) > 0 {
		resource.PrivateIPList = []string{original.BlbDetail.Address}
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.BlbDetail.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Servers = append(resource.Servers, lo.Map(original.BlbBackendServers, func(e BlbBackendServer, _ int) *model.LbServerGraph {
		return &model.LbServerGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", e.InstanceID),
				TargetUID: resource.UID,
			},
			InstanceID: e.InstanceID,
			Class:      "ecs",
			IP:         e.PrivateIP,
			Weight:     e.Weight,
		}
	})...)

	resource.SG = lo.Map(original.BlbSecurityGroups, func(e BlbSecurityGroup, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e.SecurityGroupID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.SG = append(resource.SG, lo.Map(original.BlbEnterpriseSecurityGroups, func(e BlbEnterpriseSecurityGroup, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e.EnterpriseSecurityGroupID),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
