package lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var albLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "alb"})

func NewALBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_appBlb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformALB,
		UpdateResources: updateALBResources,
	}
}

func transformALB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseALB(assetMsg)
		if err != nil {
			albLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateALBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type AppBLB struct {
	AppBlbBackendServers           []AppBlbBackendServer        `json:"appBlbBackendServers"`
	AppBlbIPGroup                  []AppBlbIPGroup              `json:"appBlbIPGroup"`
	AppBlbDetail                   AppBlbDetail                 `json:"appBlbDetail"`
	AppBlbEnterpriseSecurityGroups []BlbEnterpriseSecurityGroup `json:"appBlbEnterpriseSecurityGroups"`
	AppBlbListeners                []AppBlbWithListener         `json:"appBlbListeners"`
	AppBlbSecurityGroups           []BlbSecurityGroup           `json:"appBlbSecurityGroups"`
}

type AppBlbBackendServer struct {
	InstanceID string     `json:"instanceId"`
	Weight     int        `json:"weight"`
	PrivateIP  string     `json:"privateIp"`
	PortList   []PortList `json:"portList"`
}

type PortList struct {
	ListenerPort        int64  `json:"listenerPort"`
	BackendPort         string `json:"backendPort"`
	PortType            string `json:"portType"`
	HealthCheckPortType string `json:"healthCheckPortType"`
	Status              string `json:"status"`
	PortID              string `json:"portId"`
	PolicyID            string `json:"policyId"`
}

type AppBlbDetail struct {
	BlbID                  string                 `json:"blbId"`
	Name                   string                 `json:"name"`
	Status                 string                 `json:"status"`
	Desc                   string                 `json:"desc"`
	Address                string                 `json:"address"`
	PublicIP               string                 `json:"publicIp"`
	CIDR                   string                 `json:"cidr"`
	VpcName                string                 `json:"vpcName"`
	SubnetCider            string                 `json:"subnetCider"`
	SubnetName             string                 `json:"subnetName"`
	CreateTime             string                 `json:"createTime"`
	ReleaseTime            string                 `json:"releaseTime"`
	Layer4ClusterID        string                 `json:"layer4ClusterId"`
	Layer7ClusterID        string                 `json:"layer7ClusterId"`
	Listener               []AppBlbDetailListener `json:"listener"`
	Tags                   []Tag                  `json:"tags"`
	EipRouteType           string                 `json:"eipRouteType"`
	Layer4ClusterExclusive bool                   `json:"layer4ClusterExclusive"`
	Layer7ClusterExclusive bool                   `json:"layer7ClusterExclusive"`
	Layer4ClusterMode      string                 `json:"layer4ClusterMode"`
	Layer7ClusterMode      string                 `json:"layer7ClusterMode"`
	Layer4MasterAz         string                 `json:"layer4MasterAz"`
	Layer7MasterAz         string                 `json:"layer7MasterAz"`
	Layer4SlaveAz          string                 `json:"layer4SlaveAz"`
	Layer7SlaveAz          string                 `json:"layer7SlaveAz"`
	PaymentTiming          string                 `json:"paymentTiming"`
	PerformanceLevel       string                 `json:"performanceLevel"`
	ExpireTime             string                 `json:"expireTime"`
	AllowDelete            bool                   `json:"allowDelete"`
	VpcID                  string                 `json:"vpcId"`
}

type AppBlbDetailListener struct {
	Port string `json:"port"`
	Type string `json:"type"`
}

type AppBlbWithListener struct {
	ListenerPort          int64       `json:"listenerPort"`
	ListenerType          string      `json:"listenerType"`
	Scheduler             string      `json:"scheduler"`
	TCPSessionTimeout     int64       `json:"tcpSessionTimeout"`
	UDPSessionTimeout     int64       `json:"udpSessionTimeout"`
	KeepSession           bool        `json:"keepSession"`
	KeepSessionType       string      `json:"keepSessionType"`
	KeepSessionTimeout    int64       `json:"keepSessionTimeout"`
	KeepSessionCookieName string      `json:"keepSessionCookieName"`
	XForwardFor           bool        `json:"xForwardFor"`
	XForwardedProto       bool        `json:"xForwardedProto"`
	ServerTimeout         int64       `json:"serverTimeout"`
	RedirectPort          int64       `json:"redirectPort"`
	CERTIDS               interface{} `json:"certIds"`
	EncryptionType        string      `json:"encryptionType"`
	EncryptionProtocols   interface{} `json:"encryptionProtocols"`
	AppliedCiphers        string      `json:"appliedCiphers"`
	DualAuth              bool        `json:"dualAuth"`
	ClientCERTIDS         interface{} `json:"clientCertIds"`
}

type AppBlbIPGroup struct {
	IP       string                  `json:"ip"`
	Port     int                     `json:"port"`
	Weight   int                     `json:"weight"`
	MemberID string                  `json:"memberId"`
	PortList []AppBlbIPGroupPortList `json:"portList"`
}

type AppBlbIPGroupPortList struct {
	HealthCheckPortType string `json:"healthCheckPortType"`
	Status              string `json:"status"`
}

func parseALB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &AppBLB{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.AppBlbDetail.BlbID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.AppBlbDetail.BlbID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.AppBlbDetail.Name
	resource.Description = original.AppBlbDetail.Desc
	resource.OriginalLabels = lo.Map(original.AppBlbDetail.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Class = "application"
	resource.DeleteProtection = lo.ToPtr(!original.AppBlbDetail.AllowDelete)
	resource.Status = lo.Ternary(strings.EqualFold(original.AppBlbDetail.Status, "available"), "active", "inactive")
	if len(original.AppBlbDetail.PublicIP) > 0 {
		resource.PublicIPList = []string{original.AppBlbDetail.PublicIP}
	}
	if len(original.AppBlbDetail.Address) > 0 {
		resource.PrivateIPList = []string{original.AppBlbDetail.Address}
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.AppBlbDetail.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Servers = append(resource.Servers, lo.Map(original.AppBlbBackendServers, func(e AppBlbBackendServer, _ int) *model.LbServerGraph {
		return &model.LbServerGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", e.InstanceID),
				TargetUID: resource.UID,
			},
			InstanceID: e.InstanceID,
			Class:      "ecs",
			IP:         e.PrivateIP,
			Weight:     e.Weight,
		}
	})...)
	resource.Servers = append(resource.Servers, lo.Map(original.AppBlbIPGroup, func(e AppBlbIPGroup, _ int) *model.LbServerGraph {
		return &model.LbServerGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ip", e.MemberID),
				TargetUID: resource.UID,
			},
			Class:  "ip",
			IP:     e.IP,
			Weight: e.Weight,
		}
	})...)

	resource.SG = append(resource.SG, lo.Map(original.AppBlbSecurityGroups, func(e BlbSecurityGroup, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e.SecurityGroupID),
				TargetUID: resource.UID,
			},
		}
	})...)

	resource.SG = append(resource.SG, lo.Map(original.AppBlbEnterpriseSecurityGroups, func(e BlbEnterpriseSecurityGroup, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e.EnterpriseSecurityGroupID),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
