package cdn

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var cdnLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cdn"})

func NewCDNService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cdn_domain_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformCDN,
		UpdateResources: updateCDNs,
	}
}

func transformCDN(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseCDN(assetMsg)
		if err != nil {
			cdnLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cdn_list"] = append(resourceData["cdn_list"], utils.GenParamsFromStruct(resource))
		resourceData["source_site_list"] = append(resourceData["source_site_list"],
			utils.GenParamsFromStructSlice(resource.SourceSite)...,
		)
	}

	return resourceData, nil
}

func updateCDNs(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cdnSchema, resourceData["cdn_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sourceSiteSchema, resourceData["source_site_list"], map[string]any{"last_updated": "test"})

}

type CDN struct {
	CDNDomain CDNDomain `json:"cdnDomain"`
}

type CDNDomain struct {
	Domain                  string         `json:"domain"`
	Cname                   string         `json:"cname"`
	Status                  string         `json:"status"`
	CreateTime              string         `json:"createTime"`
	LastModifyTime          string         `json:"lastModifyTime"`
	IsBan                   string         `json:"isBan"`
	Origin                  []Origin       `json:"origin"`
	OriginProtocol          OriginProtocol `json:"originProtocol"`
	OriginTimeout           OriginTimeout  `json:"originTimeout"`
	RequestHostAsOriginHost bool           `json:"requestHostAsOriginHost"`
	CacheTTL                []CacheTTL     `json:"cacheTTL"`
	LimitRate               int64          `json:"limitRate"`
	HTTPS                   HTTPS          `json:"https"`
	FollowProtocol          bool           `json:"followProtocol"`
	SEOSwitch               SEOSwitch      `json:"seoSwitch"`
	Form                    string         `json:"form"`
	RangeSwitch             string         `json:"rangeSwitch"`
	OfflineMode             bool           `json:"offlineMode"`
	ClientIP                interface{}    `json:"clientIp"`
	Ocsp                    bool           `json:"ocsp"`
	HTTPHeader              []HTTPHeader   `json:"httpHeader"`
	MediaDragConf           interface{}    `json:"mediaDragConf"`
	FileTrim                bool           `json:"fileTrim"`
	Quic                    bool           `json:"quic"`
	RefererACL              RefererACL     `json:"refererACL"`
	IPACL                   IPACL          `json:"ipACL"`
	UaACL                   UaACL          `json:"uaAcl"`
	AccessLimit             AccessLimit    `json:"accessLimit"`
	TrafficLimit            TrafficLimit   `json:"trafficLimit"`
	ErrorPage               interface{}    `json:"errorPage"`
	CacheShare              AccessLimit    `json:"cacheShare"`
	Compress                Compress       `json:"compress"`
	Cors                    Cors           `json:"cors"`
	Ipv6Dispatch            Ipv6Dispatch   `json:"ipv6Dispatch"`
	RetryOrigin             RetryOrigin    `json:"retryOrigin"`
	Tags                    []Tag          `json:"tags"`
	LimitBandwidth          LimitBandwidth `json:"limitBandwidth"`
}

type AccessLimit struct {
	Enabled bool `json:"enabled"`
}

type CacheTTL struct {
	Type   string `json:"type"`
	Value  string `json:"value"`
	Weight int64  `json:"weight"`
	TTL    int64  `json:"ttl"`
}

type Compress struct {
	Allow string `json:"allow"`
	Type  string `json:"type"`
}

type Cors struct {
	IsAllow bool        `json:"IsAllow"`
	Origins interface{} `json:"Origins"`
}

type HTTPHeader struct {
	Type     string `json:"type"`
	Header   string `json:"header"`
	Value    string `json:"value"`
	Action   string `json:"action"`
	Describe string `json:"describe"`
}

type HTTPS struct {
	Enabled           bool     `json:"enabled"`
	CERTID            string   `json:"certId"`
	HTTPRedirect      bool     `json:"httpRedirect"`
	HTTPSRedirect     bool     `json:"httpsRedirect"`
	HTTPSRedirectCode int64    `json:"httpsRedirectCode"`
	Http2Enabled      bool     `json:"http2Enabled"`
	VerifyClient      bool     `json:"verifyClient"`
	SSLProtocols      []string `json:"sslProtocols"`
}

type IPACL struct {
	BlackList []string `json:"blackList"`
	WhiteList []string `json:"whiteList"`
}

type Ipv6Dispatch struct {
	Enable bool `json:"enable"`
}

type LimitBandwidth struct {
	Enabled   bool   `json:"enabled"`
	Threshold string `json:"threshold"`
	Action    string `json:"action"`
	Actived   bool   `json:"actived"`
}

type Origin struct {
	Peer      string `json:"peer"`
	Backup    bool   `json:"backup"`
	Follow302 bool   `json:"follow302"`
	Weight    int    `json:"weight"`
}

type OriginProtocol struct {
	Value string `json:"value"`
}

type OriginTimeout struct {
	ConnectTimeout int64 `json:"connectTimeout"`
	LoadTimeout    int64 `json:"loadTimeout"`
}

type RefererACL struct {
	BlackList  []string `json:"blackList"`
	WhiteList  []string `json:"whiteList"`
	AllowEmpty bool     `json:"allowEmpty"`
}

type RetryOrigin struct {
	Codes []interface{} `json:"codes"`
}

type SEOSwitch struct {
	DiretlyOrigin string `json:"diretlyOrigin"`
	PushRecord    string `json:"pushRecord"`
}

type TrafficLimit struct {
	Enable         bool  `json:"enable"`
	LimitStartHour int64 `json:"limitStartHour"`
	LimitEndHour   int64 `json:"limitEndHour"`
}

type UaACL struct {
	BlackList interface{}   `json:"blackList"`
	WhiteList []interface{} `json:"whiteList"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseCDN(assetMsg *model.AssetMessage) (*model.CDNGraph, error) {
	original := &CDN{}
	resource := &model.CDNGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.CDNDomain.Domain
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "cdn", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cdn"
	resource.Name = original.CDNDomain.Domain

	resource.Status = lo.Ternary(strings.EqualFold(original.CDNDomain.Status, "running"), "running", "stopped")
	resource.HTTPSEnabled = original.CDNDomain.HTTPS.Enabled
	resource.CNAME = original.CDNDomain.Cname
	resource.Coverage = "domestic"
	form := strings.ToLower(original.CDNDomain.Form)
	resource.Class = lo.Ternary(form == "media", "video", form)

	resource.SourceSite = lo.Map(original.CDNDomain.Origin, func(e Origin, _ int) *model.SourceSiteGraph {
		return &model.SourceSiteGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "source_site", fmt.Sprintf("%s-%d", e.Peer, e.Weight)),
				TargetUID: resource.UID,
			},
			URL:    e.Peer,
			Weight: e.Weight,
		}
	})

	resource.ForceHTTPS = original.CDNDomain.HTTPS.HTTPRedirect
	resource.AllowEmptyReferer = original.CDNDomain.RefererACL.AllowEmpty
	resource.RefererBlackList = original.CDNDomain.RefererACL.BlackList
	resource.RefererWhiteList = original.CDNDomain.RefererACL.WhiteList

	resource.IPBlackList = lo.Map(original.CDNDomain.IPACL.BlackList, func(e string, _ int) string {
		return provider_utils.FormatCIDR(e)
	})
	resource.IPWhiteList = lo.Map(original.CDNDomain.IPACL.WhiteList, func(e string, _ int) string {
		return provider_utils.FormatCIDR(e)
	})

	resource.RateLimitEnabled = original.CDNDomain.LimitRate != 0
	resource.FreqLimitEnabled = original.CDNDomain.AccessLimit.Enabled
	resource.BandwidthLimitEnabled = original.CDNDomain.LimitBandwidth.Enabled

	return resource, nil
}
