package cdn

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var abroadCDNLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cdn"})

func NewAbroadCDNService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cdn_abroadDomain_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformAbroadCDN,
		UpdateResources: updateAbroadCDNs,
	}
}

func transformAbroadCDN(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseCDN(assetMsg)
		if err != nil {
			abroadCDNLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cdn_list"] = append(resourceData["cdn_list"], utils.GenParamsFromStruct(resource))
		resourceData["source_site_list"] = append(resourceData["source_site_list"],
			utils.GenParamsFromStructSlice(resource.SourceSite)...,
		)
	}

	return resourceData, nil
}

func updateAbroadCDNs(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cdnSchema, resourceData["cdn_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sourceSiteSchema, resourceData["source_site_list"], map[string]any{"last_updated": "test"})

}

type AbroadCDN struct {
	CDNDomain AbroadCDNDomain `json:"cdnDomain"`
}

type AbroadCDNDomain struct {
	CacheFullURL        bool           `json:"cacheFullUrl"`
	CacheTTL            []CacheTTL     `json:"cacheTtl"`
	Cname               string         `json:"cname"`
	CreateTime          string         `json:"createTime"`
	Domain              string         `json:"domain"`
	HTTPS               HTTPS          `json:"https"`
	IPACL               IPACL          `json:"ipACL"`
	LastModifyTime      string         `json:"lastModifyTime"`
	OriginConfig        []OriginConfig `json:"originConfig"`
	OriginProtocol      string         `json:"originProtocol"`
	RefererACL          RefererACL     `json:"refererACL"`
	Status              string         `json:"status"`
	Tags                []Tag          `json:"tags"`
	VerificationContent string         `json:"verificationContent"`
	VerificationName    string         `json:"verificationName"`
}

type OriginConfig struct {
	Addr   string `json:"addr"`
	Backup bool   `json:"backup"`
	Type   string `json:"type"`
}

func parseAbroadCDN(assetMsg *model.AssetMessage) (*model.CDNGraph, error) {
	original := &AbroadCDN{}
	resource := &model.CDNGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.CDNDomain.Domain
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "cdn", resource.OriginalID+"/overseas")
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cdn"
	resource.Name = original.CDNDomain.Domain

	resource.Status = lo.Ternary(strings.EqualFold(original.CDNDomain.Status, "running"), "running", "stopped")
	resource.HTTPSEnabled = original.CDNDomain.HTTPS.Enabled
	resource.CNAME = original.CDNDomain.Cname
	resource.Coverage = "domestic"
	resource.Class = "default"

	resource.SourceSite = lo.Map(original.CDNDomain.OriginConfig, func(e OriginConfig, _ int) *model.SourceSiteGraph {
		return &model.SourceSiteGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "source_site", e.Addr),
				TargetUID: resource.UID,
			},
			URL: e.Addr,
		}
	})

	resource.ForceHTTPS = original.CDNDomain.HTTPS.HTTPRedirect
	resource.AllowEmptyReferer = original.CDNDomain.RefererACL.AllowEmpty
	resource.RefererBlackList = original.CDNDomain.RefererACL.BlackList
	resource.RefererWhiteList = original.CDNDomain.RefererACL.WhiteList

	resource.IPBlackList = lo.Map(original.CDNDomain.IPACL.BlackList, func(e string, _ int) string {
		return provider_utils.FormatCIDR(e)
	})
	resource.IPWhiteList = lo.Map(original.CDNDomain.IPACL.WhiteList, func(e string, _ int) string {
		return provider_utils.FormatCIDR(e)
	})

	resource.RateLimitEnabled = false
	resource.FreqLimitEnabled = false
	resource.BandwidthLimitEnabled = false

	return resource, nil
}
