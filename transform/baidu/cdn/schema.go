package cdn

import graph "AssetStandardizer/graph"

var (
	cdnSchema = graph.NodeSchema{
		Label: "CDN",
		Properties: graph.NodeProperties{
			"uid":                     graph.PropertyRef{Name: "uid"},
			"provider":                graph.PropertyRef{Name: "provider"},
			"original_id":             graph.PropertyRef{Name: "original_id"},
			"transformed_object":      {Name: "transformed_object"},
			"region":                  graph.PropertyRef{Name: "region"},
			"last_seen":               graph.PropertyRef{Name: "last_seen"},
			"description":             graph.PropertyRef{Name: "description"},
			"kind":                    graph.PropertyRef{Name: "kind"},
			"name":                    graph.PropertyRef{Name: "name"},
			"status":                  graph.PropertyRef{Name: "status"},
			"https_enabled":           graph.PropertyRef{Name: "https_enabled"},
			"force_https":             graph.PropertyRef{Name: "force_https"},
			"coverage":                graph.PropertyRef{Name: "coverage"},
			"cname":                   graph.PropertyRef{Name: "cname"},
			"class":                   graph.PropertyRef{Name: "class"},
			"ip_white_list":           graph.PropertyRef{Name: "ip_white_list"},
			"ip_black_list":           graph.PropertyRef{Name: "ip_black_list"},
			"rate_limit_enabled":      graph.PropertyRef{Name: "rate_limit_enabled"},
			"freq_limit_enabled":      graph.PropertyRef{Name: "freq_limit_enabled"},
			"bandwidth_limit_enabled": graph.PropertyRef{Name: "bandwidth_limit_enabled"},
			"allow_empty_referer":     graph.PropertyRef{Name: "allow_empty_referer"},
			"referer_white_list":      graph.PropertyRef{Name: "referer_white_list"},
			"referer_black_list":      graph.PropertyRef{Name: "referer_black_list"},
		},
	}

	sourceSiteSchema = graph.NodeSchema{
		Label: "SourceSite",
		Properties: graph.NodeProperties{
			"uid":    {Name: "uid"},
			"url":    {Name: "url"},
			"weight": {Name: "weight"},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "CDN",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "target_uid"},
				},
				RelLabel:  "HAS_SOURCE",
				Direction: graph.INWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
			graph.RelSchema{
				TargetNodeLabel: "Bucket",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"private_endpoint": {Name: "url"},
				},
				RelLabel:  "SERVES",
				Direction: graph.INWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
			graph.RelSchema{
				TargetNodeLabel: "Bucket",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"public_endpoint": {Name: "url"},
				},
				RelLabel:  "SERVES",
				Direction: graph.INWARD,
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}
)
