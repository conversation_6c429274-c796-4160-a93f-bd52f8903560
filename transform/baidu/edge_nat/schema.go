package edge_nat

import "AssetStandardizer/graph"

var natSchema = graph.NodeSchema{
	Label: "NAT",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"status":             {Name: "status"},
		"spec":               {Name: "spec"},
	},
}

var snatRuleSchema = graph.NodeSchema{
	Label: "SNATRule",
	Properties: map[string]graph.PropertyRef{
		"uid":     {Name: "uid"},
		"rule_id": {Name: "rule_id"},
		"name":    {Name: "name"},
		"status":  {Name: "status"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAT",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_SNAT_RULE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var ipRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SNATRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_IP_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dnatRuleSchema = graph.NodeSchema{
	Label: "DNATRule",
	Properties: map[string]graph.PropertyRef{
		"uid":           {Name: "uid"},
		"rule_id":       {Name: "rule_id"},
		"name":          {Name: "name"},
		"status":        {Name: "status"},
		"protocol":      {Name: "protocol"},
		"internal_ip":   {Name: "internal_ip"},
		"internal_port": {Name: "internal_port"},
		"external_ip":   {Name: "external_ip"},
		"external_port": {Name: "external_port"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAT",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_DNAT_RULE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var eipSchema = graph.NodeSchema{
	Label: "EIP",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAT",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ASSIGNED_TO",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAT",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var subnetSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "NAT",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
