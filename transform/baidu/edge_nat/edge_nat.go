package edge_nat

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strconv"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nat"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bec_nat_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nat_list"] = append(resourceData["nat_list"], utils.GenParamsFromStruct(resource))

		resourceData["snat_rule_list"] = append(resourceData["snat_rule_list"],
			utils.GenParamsFromStructSlice(resource.SNATRules)...,
		)
		resourceData["ip_range_list"] = append(resourceData["ip_range_list"],
			lo.FlatMap(resource.SNATRules, func(snatRule *model.SNATRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(snatRule.IPRange)
			})...,
		)

		resourceData["dnat_rule_list"] = append(resourceData["dnat_rule_list"],
			utils.GenParamsFromStructSlice(resource.DNATRules)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, natSchema, resourceData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, snatRuleSchema, resourceData["snat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dnatRuleSchema, resourceData["dnat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipRangeSchema, resourceData["ip_range_list"], map[string]any{"last_updated": "test"})
}

type EdgeNAT struct {
	NatGateway EdgeNatGateway `json:"natGateway"`
	DnatRules  []EdgeDnatRule `json:"dnatRules"`
	SnatRules  []EdgeSnatRule `json:"snatRules"`
}

type EdgeNatGateway struct {
	Bandwidth  int64             `json:"bandwidth"`
	CreateTime string            `json:"createTime"`
	Desc       string            `json:"desc"`
	DnatEips   []PublicIPAddress `json:"dnatEips"`
	Eips       []PublicIPAddress `json:"eips"`
	Name       string            `json:"name"`
	NatID      string            `json:"natId"`
	RegionID   string            `json:"regionId"`
	Spec       string            `json:"spec"`
	Status     string            `json:"status"`
	VpcID      string            `json:"vpcId"`
	Tags       []Tag             `json:"tags"`
}

type EdgeDnatRule struct {
	NatID            string          `json:"natId"`
	PrivateIPAddress string          `json:"privateIpAddress"`
	PrivatePort      string          `json:"privatePort"`
	Protocol         string          `json:"protocol"`
	PublicIPAddress  PublicIPAddress `json:"publicIpAddress"`
	PublicPort       string          `json:"publicPort"`
	RuleID           string          `json:"ruleId"`
	RuleName         string          `json:"ruleName"`
	Status           string          `json:"status"`
}

type EdgeSnatRule struct {
	NatID            string            `json:"natId"`
	PublicIPSAddress []PublicIPAddress `json:"publicIpsAddress"`
	RuleID           string            `json:"ruleId"`
	RuleName         string            `json:"ruleName"`
	SourceCIDR       string            `json:"sourceCIDR"`
	Status           string            `json:"status"`
}

type PublicIPAddress struct {
	IP              string `json:"ip"`
	ServiceProvider string `json:"serviceProvider"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NATGraph, error) {
	original := &EdgeNAT{}
	resource := &model.NATGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NatGateway.NatID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nat", original.NatGateway.NatID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nat"
	resource.Name = original.NatGateway.Name
	resource.OriginalLabels = lo.Map(original.NatGateway.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Status = strings.ToLower(original.NatGateway.Status)
	var eipList []string
	eipList = append(eipList, lo.Map(original.NatGateway.Eips, func(e PublicIPAddress, _ int) string { return e.IP })...)
	eipList = append(eipList, lo.Map(original.NatGateway.DnatEips, func(e PublicIPAddress, _ int) string { return e.IP })...)
	eipList = append(eipList, lo.Flatten(
		lo.Map(original.SnatRules, func(snatRule EdgeSnatRule, _ int) []string {
			return lo.Map(snatRule.PublicIPSAddress, func(e PublicIPAddress, _ int) string { return e.IP })
		}))...,
	)
	eipList = append(eipList, lo.Map(original.DnatRules, func(dnatRule EdgeDnatRule, _ int) string {
		return dnatRule.PublicIPAddress.IP
	})...)
	resource.EIP = lo.Map(lo.Uniq(eipList), func(eip string, _ int) *model.EipGraph {
		return &model.EipGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eip", eip),
				TargetUID: resource.UID,
			},
		}
	})

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.NatGateway.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.SNATRules = lo.FilterMap(original.SnatRules, func(snatTable EdgeSnatRule, _ int) (*model.SNATRuleGraph, bool) {
		natUID := utils.GenerateUID(resource.Provider, "snat_rule", snatTable.RuleID)
		ipRange, err := provider_utils.ParseIPRange(snatTable.SourceCIDR, false, natUID)
		if err != nil {
			logger.Errorf("parse snat table ip range error, snatTable: %v, error: %v", snatTable, err)
			return nil, false
		}

		return &model.SNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       natUID,
				TargetUID: resource.UID,
			},
			RuleID:  snatTable.RuleID,
			Name:    snatTable.RuleName,
			Status:  strings.ToLower(snatTable.Status),
			IPRange: []*model.IPRangeGraph{ipRange},
		}, true
	})

	resource.DNATRules = lo.Map(original.DnatRules, func(dnatRule EdgeDnatRule, _ int) *model.DNATRuleGraph {
		internalPort, _ := strconv.Atoi(dnatRule.PrivatePort)
		externalPort, _ := strconv.Atoi(dnatRule.PublicPort)
		return &model.DNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "dnat_rule", dnatRule.RuleID),
				TargetUID: resource.UID,
			},
			RuleID:       dnatRule.RuleID,
			Name:         dnatRule.RuleName,
			Status:       strings.ToLower(dnatRule.Status),
			Protocol:     strings.ToLower(dnatRule.Protocol),
			InternalIP:   dnatRule.PrivateIPAddress,
			InternalPort: internalPort,
			ExternalIP:   dnatRule.PublicIPAddress.IP,
			ExternalPort: externalPort,
		}
	})

	return resource, nil
}
