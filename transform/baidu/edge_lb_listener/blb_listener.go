package edge_lb_listener

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var blbListenerLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "blb_listener"})

func NewBLBListenerService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bec_blbListener_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformBLBListener,
		UpdateResources: updateBLBListeners,
	}
}

func transformBLBListener(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseBLBListener(assetMsg)
		if err != nil {
			blbListenerLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_listener_list"] = append(resourceData["lb_listener_list"], utils.GenParamsFromStruct(resource))
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["port_range_list"] = append(resourceData["port_range_list"],
			utils.GenParamsFromStructSlice(resource.PortRange)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateBLBListeners(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbListenerSchema, resourceData["lb_listener_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbFromListenerSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, portRangeSchema, resourceData["port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})

}

type EdgeBlbListenerWithBlbId struct {
	BlbID    string          `json:"blbId"`
	Listener EdgeBlbListener `json:"listener"`
}

type EdgeBlbListener struct {
	Protocol         string `json:"protocol,omitempty"`
	Port             int    `json:"port,omitempty"`
	BackendPort      int    `json:"backendPort,omitempty"`
	KeepaliveTimeout int    `json:"keepaliveTimeout,omitempty"`
	Scheduler        string `json:"scheduler,omitempty"`
	EnableCipTTM     bool   `json:"enableCipTTM,omitempty"`
	EnableVipTTM     bool   `json:"enableVipTTM,omitempty"`

	// health check config
	HealthCheckInterval  int    `json:"healthCheckInterval,omitempty"`
	HealthCheckRetry     int    `json:"healthCheckRetry,omitempty"`
	HealthCheckTimeout   int    `json:"healthCheckTimeout,omitempty"`
	UdpHealthCheckString string `json:"udpHealthCheckString,omitempty"`
	HealthCheckType      string `json:"healthCheckType,omitempty"`
}

func parseBLBListener(assetMsg *model.AssetMessage) (*model.LbListenerGraph, error) {
	original := &EdgeBlbListenerWithBlbId{}
	resource := &model.LbListenerGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb-listener", fmt.Sprintf("%s+%d+%s", original.BlbID, original.Listener.Port, original.Listener.Protocol))
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb-listener"
	resource.Name = fmt.Sprintf("%s@%d/%s", original.BlbID, original.Listener.Port, original.Listener.Protocol)

	resource.Status = "active"
	resource.Protocol = strings.ToLower(original.Listener.Protocol)
	resource.CertExists = lo.ToPtr(false)
	resource.HealthCheckEnabled = lo.ToPtr(original.Listener.HealthCheckInterval > 0)

	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.BlbID),
			TargetUID: resource.UID,
		},
	})
	resource.PortRange = append(resource.PortRange, utils.NewPortRange(original.Listener.Port, original.Listener.Port, resource.UID))

	return resource, nil
}
