package nas

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nas"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cfs_cfs_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nas_list"] = append(resourceData["nas_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["nas_acl_rule_list"] = append(resourceData["nas_acl_rule_list"],
			utils.GenParamsFromStructSlice(resource.ACLRule)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, nasSchema, resourceData["nas_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, nasACLRuleSchema, resourceData["nas_acl_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type CFS struct {
	Cfs         CfsClass `json:"cfs"`
	MountPoints []Mount  `json:"mountPoints"`
}

type CfsClass struct {
	FSID            string  `json:"fsId"`
	FSName          string  `json:"fsName"`
	VpcID           string  `json:"vpcId"`
	Type            string  `json:"type"`
	Protocol        string  `json:"protocol"`
	FSUsage         string  `json:"fsUsage"`
	Status          string  `json:"status"`
	MountTargetList []Mount `json:"mountTargetList"`
}

type Mount struct {
	AccessGroupName string `json:"accessGroupName"`
	MountID         string `json:"mountId"`
	Ovip            string `json:"ovip"`
	Domain          string `json:"domain"`
	SubnetID        string `json:"subnetId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NASGraph, error) {
	original := &CFS{}
	resource := &model.NASGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cfs.FSID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nas", original.Cfs.FSID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nas"
	resource.Name = original.Cfs.FSName

	resource.Status = lo.Ternary(strings.EqualFold(original.Cfs.Status, "available"), "on", "off")
	resource.Class = strings.ToLower(original.Cfs.Protocol)
	resource.Encrypt = false
	resource.Domains = lo.Map(original.MountPoints, func(mount Mount, _ int) string { return mount.Domain })
	resource.IPs = lo.Map(original.MountPoints, func(mount Mount, _ int) string { return mount.Ovip })
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Cfs.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = lo.Map(original.MountPoints, func(mount Mount, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", mount.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
