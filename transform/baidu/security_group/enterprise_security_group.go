package security_group

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var esgLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "security_group"})

func NewESGService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bcc_esg_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformESG,
		UpdateResources: updateESGs,
	}
}

func transformESG(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseESG(assetMsg)
		if err != nil {
			esgLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["sg_list"] = append(resourceData["sg_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_rule_list"] = append(resourceData["sg_rule_list"],
			utils.GenParamsFromStructSlice(resource.Rules)...,
		)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstPortRange)
			})...,
		)
	}
	return resourceData, nil
}

func updateESGs(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgRuleSchema, resourceData["sg_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIPRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIPRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type Esg struct {
	Esg EsgClass `json:"esg"`
}

type EsgClass struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Desc        string    `json:"desc"`
	Rules       []EsgRule `json:"rules"`
	Tags        []Tag     `json:"tags"`
	CreatedTime string    `json:"createdTime"`
}

type EsgRule struct {
	EnterpriseSecurityGroupRuleID string `json:"enterpriseSecurityGroupRuleId"`
	Remark                        string `json:"remark"`
	Direction                     string `json:"direction"`
	Ethertype                     string `json:"ethertype"`
	PortRange                     string `json:"portRange"`
	SourcePortRange               string `json:"sourcePortRange"`
	Protocol                      string `json:"protocol"`
	SourceIP                      string `json:"sourceIp"`
	DestIP                        string `json:"destIp"`
	LocalIP                       string `json:"localIp"`
	RemoteIPSet                   string `json:"remoteIpSet"`
	RemoteIPGroup                 string `json:"remoteIpGroup"`
	Action                        string `json:"action"`
	Priority                      int64  `json:"priority"`
	CreatedTime                   string `json:"createdTime"`
	UpdatedTime                   string `json:"updatedTime"`
}

func parseESG(assetMsg *model.AssetMessage) (*model.SGGraph, error) {
	original := &Esg{}
	resource := &model.SGGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Esg.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "security-group", original.Esg.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "security-group"
	resource.Name = original.Esg.Name
	resource.Description = original.Esg.Desc
	resource.OriginalLabels = lo.Map(original.Esg.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.IsDefault = strings.Contains(original.Esg.Name, "默认安全组") || strings.Contains(original.Esg.Name, "default")

	for _, perm := range original.Esg.Rules {
		rule, err := parseESGRule(perm, resource.UID)
		if err != nil {
			return nil, fmt.Errorf("parseSGRule failed, perm: %v, err: %v", perm, err)
		}
		resource.Rules = append(resource.Rules, rule)
	}
	return resource, nil
}

func parseESGRule(perm EsgRule, targetUID string) (*model.SGRuleGraph, error) {
	rule := &model.SGRuleGraph{}
	rule.UID = utils.GenerateUID(provider_utils.ProviderID, "sg_rule", perm.EnterpriseSecurityGroupRuleID)
	rule.TargetUID = targetUID
	rule.RuleID = perm.EnterpriseSecurityGroupRuleID
	rule.Description = perm.Remark
	rule.Protocol = strings.ToLower(perm.Protocol)
	rule.Policy = lo.Ternary(strings.EqualFold(perm.Action, "allow"), "accept", "drop")
	rule.Direction = perm.Direction
	rule.Priority = int(perm.Priority)

	if ipRange, err := provider_utils.ParseIPRange(perm.SourceIP, perm.Ethertype == "ipv6", rule.UID); err != nil {
		return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, perm.SourceIP)
	} else {
		rule.SrcIPRange = []*model.IPRangeGraph{ipRange}
	}

	if ipRange, err := provider_utils.ParseIPRange(perm.DestIP, perm.Ethertype == "ipv6", rule.UID); err != nil {
		return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, perm.DestIP)
	} else {
		rule.DstIPRange = []*model.IPRangeGraph{ipRange}
	}

	if portRange, err := provider_utils.ParsePortRange(perm.SourcePortRange, rule.UID); err != nil {
		return nil, fmt.Errorf("parse port range error: %v, port: %s", err, perm.PortRange)
	} else {
		rule.DstPortRange = []*model.PortRangeGraph{portRange}
	}

	if portRange, err := provider_utils.ParsePortRange(perm.PortRange, rule.UID); err != nil {
		return nil, fmt.Errorf("parse port range error: %v, port: %s", err, perm.PortRange)
	} else {
		rule.DstPortRange = []*model.PortRangeGraph{portRange}
	}

	return rule, nil
}
