package security_group

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var sgLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "security_group"})

func NewSGService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bcc_sg_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSG,
		UpdateResources: updateSGs,
	}
}

func transformSG(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSG(assetMsg)
		if err != nil {
			sgLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["sg_list"] = append(resourceData["sg_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_rule_list"] = append(resourceData["sg_rule_list"],
			utils.GenParamsFromStructSlice(resource.Rules)...,
		)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstPortRange)
			})...,
		)
	}
	return resourceData, nil
}

func updateSGs(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgRuleSchema, resourceData["sg_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIPRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIPRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type SG struct {
	SecurityGroup SGSecurityGroup `json:"securityGroup"`
}

type SGSecurityGroup struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	VpcID       string `json:"vpcId"`
	Rules       []Rule `json:"rules"`
	Tags        []Tag  `json:"tags"`
	CreatedTime string `json:"createdTime"`
}

type Rule struct {
	Remark              string `json:"remark"`
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	SourceGroupID       string `json:"sourceGroupId"`
	SourceIP            string `json:"sourceIp"`
	DestGroupID         string `json:"destGroupId"`
	DestIP              string `json:"destIp"`
	SecurityGroupID     string `json:"securityGroupId"`
	SecurityGroupRuleID string `json:"securityGroupRuleId"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseSG(assetMsg *model.AssetMessage) (*model.SGGraph, error) {
	original := &SG{}
	resource := &model.SGGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.SecurityGroup.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "security-group", original.SecurityGroup.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "security-group"
	resource.Name = original.SecurityGroup.Name
	resource.Description = original.SecurityGroup.Desc
	resource.OriginalLabels = lo.Map(original.SecurityGroup.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.IsDefault = strings.Contains(original.SecurityGroup.Name, "默认安全组") || strings.Contains(original.SecurityGroup.Name, "default")
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.SecurityGroup.VpcID),
			TargetUID: resource.UID,
		},
	})

	for _, perm := range original.SecurityGroup.Rules {
		rule, err := parseSGRule(perm, resource.UID)
		if err != nil {
			return nil, fmt.Errorf("parseSGRule failed, perm: %v, err: %v", perm, err)
		}
		resource.Rules = append(resource.Rules, rule)
	}
	return resource, nil
}

func parseSGRule(perm Rule, targetUID string) (*model.SGRuleGraph, error) {
	rule := &model.SGRuleGraph{}
	rule.UID = utils.GenerateUID(provider_utils.ProviderID, "sg_rule", perm.SecurityGroupRuleID)
	rule.TargetUID = targetUID
	rule.RuleID = perm.SecurityGroupRuleID
	rule.Description = perm.Remark
	rule.Protocol = strings.ToLower(perm.Protocol)
	rule.Policy = "accept"
	rule.Direction = perm.Direction

	if perm.SourceGroupID != "" || perm.DestGroupID != "" {
		sgId := lo.Ternary(perm.Direction == "ingress", perm.SourceGroupID, perm.DestGroupID)
		rule.PeerSG = []*model.SGGraph{
			{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(provider_utils.ProviderID, "security-group", sgId),
					TargetUID: rule.UID,
				},
			},
		}
	} else {
		if ipRange, err := provider_utils.ParseIPRange(perm.SourceIP, perm.Ethertype == "ipv6", rule.UID); err != nil {
			return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, perm.SourceIP)
		} else {
			rule.SrcIPRange = []*model.IPRangeGraph{ipRange}
		}

		if ipRange, err := provider_utils.ParseIPRange(perm.DestIP, perm.Ethertype == "ipv6", rule.UID); err != nil {
			return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, perm.DestIP)
		} else {
			rule.DstIPRange = []*model.IPRangeGraph{ipRange}
		}
	}

	rule.SrcPortRange = []*model.PortRangeGraph{utils.NewPortRange(0, 65535, rule.UID)}
	if portRange, err := provider_utils.ParsePortRange(perm.PortRange, rule.UID); err != nil {
		return nil, fmt.Errorf("parse port range error: %v, port: %s", err, perm.PortRange)
	} else {
		rule.DstPortRange = []*model.PortRangeGraph{portRange}
	}

	return rule, nil
}


