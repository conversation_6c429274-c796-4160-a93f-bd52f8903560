package utils

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/utils"
	"fmt"
	"net"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
)

const ProviderID = "baidu"

func ParseIPRange(cidr string, isIPv6 bool, targetUID string) (*model.IPRangeGraph, error) {
	if len(cidr) == 0 || cidr == "all" {
		return utils.NewAnyIPRange(targetUID, isIPv6), nil
	} else if !strings.Contains(cidr, "/") {
		return &model.IPRangeGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID("", "ip_range", fmt.Sprintf("%s-%s", cidr, cidr)),
				TargetUID: targetUID,
			},
			Start: cidr,
			End:   cidr,
		}, nil
	}

	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, err
	}

	startIP := ipnet.IP

	mask := ipnet.Mask
	endIP := make(net.IP, len(startIP))
	for i := range startIP {
		endIP[i] = startIP[i] | ^mask[i]
	}

	return &model.IPRangeGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID("", "ip_range", fmt.Sprintf("%s-%s", startIP.String(), endIP.String())),
			TargetUID: targetUID,
		},
		Start: startIP.String(),
		End:   endIP.String(),
	}, nil
}

func ParsePortRange(portRange string, targetUID string) (*model.PortRangeGraph, error) {
	if lo.Contains([]string{"", "all", "null"}, portRange) {
		return utils.NewPortRange(0, 65535, targetUID), nil
	}

	ports := strings.Split(portRange, "-")
	if len(ports) == 2 {
		portStart, _ := strconv.Atoi(ports[0])
		portStart = lo.Ternary(portStart < 0, 0, portStart)
		portEnd, _ := strconv.Atoi(ports[1])
		portEnd = lo.Ternary(portEnd < 0, 65535, portEnd)
		return utils.NewPortRange(portStart, portEnd, targetUID), nil
	} else if len(ports) == 1 {
		port, _ := strconv.Atoi(ports[0])
		return utils.NewPortRange(port, port, targetUID), nil
	} else {
		return nil, fmt.Errorf("invalid port range: %s", portRange)
	}
}

type ACL struct {
	Service    string    `json:"service"`
	Region     string    `json:"region"`
	Grantee    []Grantee `json:"grantee"`
	Permission []string  `json:"permission"`
	Condition  Condition `json:"condition"`
	Resource   []string  `json:"resource"`
	Effect     string    `json:"effect"`
}

type Condition struct {
	IPAddress interface{} `json:"ipAddress"`
	Referer   Referer     `json:"referer"`
}

type Referer struct {
	StringLike   interface{} `json:"stringLike"`
	StringEquals interface{} `json:"stringEquals"`
}

type Grantee struct {
	ID           string `json:"id"`
	SAMLProvider string `json:"saml-provider"`
}

func ParsePolicyDocument(policyDocument []byte, targetUID string) []*model.PolicyStatementGraph {
	if len(policyDocument) == 0 {
		return nil
	}

	parsedDocument := []ACL{}
	sonic.Unmarshal(policyDocument, &parsedDocument)

	return lo.Map(parsedDocument, func(statement ACL, _ int) *model.PolicyStatementGraph {
		pd := &model.PolicyStatementGraph{}
		pd.Effect = lo.Ternary(statement.Effect != "", strings.ToLower(statement.Effect), "allow")

		service := lo.CoalesceOrEmpty(statement.Service, "*")

		pd.Action = lo.Map(statement.Permission, func(permission string, _ int) string {
			return fmt.Sprintf("%v:%v", service, permission)
		})

		pd.Resource = statement.Resource
		pd.Condition = utils.ToJsonStr(statement.Condition)
		pd.Principal = utils.ToJsonStr(statement.Grantee)
		return pd
	})
}

func FormatCIDR(cidr string) string {
	cidr = strings.TrimSpace(cidr)

	if cidr == "" || cidr == "%" {
		cidr = "0.0.0.0/0"
	} else if !strings.Contains(cidr, "/") {
		cidr = cidr + lo.Ternary(strings.Contains(cidr, ":"), "/128", "/32")
	}
	return cidr
}
