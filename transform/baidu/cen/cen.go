package cen

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cen"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "csn_csn_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cen_list"] = append(resourceData["cen_list"], utils.GenParamsFromStruct(resource))

		resourceData["cen_route_entry_list"] = append(resourceData["cen_route_entry_list"],
			utils.GenParamsFromStructSlice(resource.CenRouteEntry)...,
		)

		for _, attachedInstance := range resource.AttachedInstance {
			instanceType := attachedInstance.InstanceType
			resourceData[instanceType] = append(resourceData[instanceType], utils.GenParamsFromStruct(attachedInstance))
		}

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cenSchema, resourceData["cen_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, cenRouteEntrySchema, resourceData["cen_route_entry_list"], map[string]any{"last_updated": "test"})

	for key, instances := range resourceData {
		if strings.HasSuffix(key, "_list") {
			continue
		}

		instanceSchema := cenAttachedInstanceSchema
		instanceSchema.OtherRelationships = append(instanceSchema.OtherRelationships, relToInstance(key)...)
		graph.Run(n4jSession, instanceSchema, instances, map[string]any{"last_updated": "test"})
	}
}

type Csn struct {
	AttachedInstances []AttachedInstance `json:"attachedInstances"`
	Csn               CsnClass           `json:"csn"`
	RouteTables       []RouteTable       `json:"routeTables"`
}

type AttachedInstance struct {
	AttachID          string `json:"attachId"`
	InstanceType      string `json:"instanceType"`
	InstanceID        string `json:"instanceId"`
	InstanceName      string `json:"instanceName"`
	InstanceRegion    string `json:"instanceRegion"`
	InstanceAccountID string `json:"instanceAccountId"`
	Status            string `json:"status"`
}

type CsnClass struct {
	CsnID       string `json:"csnId"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"`
	InstanceNum int64  `json:"instanceNum"`
	CsnBpNum    int64  `json:"csnBpNum"`
}

type RouteTable struct {
	RouteTables RouteTables `json:"RouteTables"`
	Entries     []Entry     `json:"Entries"`
}

type Entry struct {
	RuleID        string `json:"ruleId"`
	RouteType     string `json:"routeType"`
	CsnID         string `json:"csnId"`
	CsnRtID       string `json:"csnRtId"`
	Description   string `json:"description"`
	FromAttachID  string `json:"fromAttachId"`
	Status        string `json:"status"`
	SourceAddress string `json:"sourceAddress"`
	DestAddress   string `json:"destAddress"`
	NextHopID     string `json:"nextHopId"`
	NextHopName   string `json:"nextHopName"`
	NextHopRegion string `json:"nextHopRegion"`
	NextHopType   string `json:"nextHopType"`
	AsPath        string `json:"asPath"`
	Community     string `json:"community"`
	BlackHole     bool   `json:"blackHole"`
}

type RouteTables struct {
	CsnRtID     string `json:"csnRtId"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Type        string `json:"type"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CenGraph, error) {
	original := &Csn{}
	resource := &model.CenGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Csn.CsnID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "cen", original.Csn.CsnID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cen"
	resource.Name = original.Csn.Name
	resource.Description = original.Csn.Description

	resource.Status = lo.Ternary(strings.EqualFold(original.Csn.Status, "active"), "running", "stopped")
	resource.AttachedInstance = lo.Map(original.AttachedInstances, func(e AttachedInstance, _ int) *model.CenAttachedInstanceGraph {
		normalizedType := strings.ToLower(e.InstanceType)
		return &model.CenAttachedInstanceGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, normalizedType, e.InstanceID),
				TargetUID: resource.UID,
			},
			InstanceID:   e.InstanceID,
			InstanceName: e.InstanceName,
			InstanceType: normalizedType,
		}
	})

	attachIdToInstance := lo.SliceToMap(original.AttachedInstances, func(e AttachedInstance) (string, AttachedInstance) {
		return e.AttachID, e
	})

	resource.CenRouteEntry = lo.FlatMap(original.RouteTables, func(e RouteTable, _ int) []*model.CenRouteEntryGraph {
		return lo.FilterMap(e.Entries, func(e Entry, _ int) (*model.CenRouteEntryGraph, bool) {
			srcInstance, ok := attachIdToInstance[e.FromAttachID]
			if !ok {
				return nil, false
			}

			return &model.CenRouteEntryGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "cen_route_entry", e.RuleID),
					TargetUID: resource.UID,
				},
				SrcInstanceID:   srcInstance.InstanceID,
				SrcInstanceUID:  utils.GenerateUID(resource.Provider, strings.ToLower(srcInstance.InstanceType), srcInstance.InstanceID),
				NextInstanceID:  e.NextHopID,
				NextInstanceUID: utils.GenerateUID(resource.Provider, strings.ToLower(e.NextHopType), e.NextHopID),
				DstCidrBlock:    e.DestAddress,
				Status:          lo.Ternary(strings.EqualFold(e.Status, "active"), "active", "inactive"),
				Class:           lo.Ternary(strings.EqualFold(e.RouteType, "custom"), "custom", "system"),
			}, true
		})
	})

	return resource, nil
}
