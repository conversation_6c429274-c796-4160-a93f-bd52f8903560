package nat

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nat"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "nat_gateway_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nat_list"] = append(resourceData["nat_list"], utils.GenParamsFromStruct(resource))

		resourceData["snat_rule_list"] = append(resourceData["snat_rule_list"],
			utils.GenParamsFromStructSlice(resource.SNATRules)...,
		)
		resourceData["ip_range_list"] = append(resourceData["ip_range_list"],
			lo.FlatMap(resource.SNATRules, func(snatRule *model.SNATRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(snatRule.IPRange)
			})...,
		)

		resourceData["dnat_rule_list"] = append(resourceData["dnat_rule_list"],
			utils.GenParamsFromStructSlice(resource.DNATRules)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, natSchema, resourceData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, snatRuleSchema, resourceData["snat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dnatRuleSchema, resourceData["dnat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipRangeSchema, resourceData["ip_range_list"], map[string]any{"last_updated": "test"})
}

type NAT struct {
	DnatTable  []DnatTable `json:"dnatTable"`
	NatGateway NatGateway  `json:"natGateway"`
	SnatTable  []SnatTable `json:"snatTable"`
}

type DnatTable struct {
	RuleID           string `json:"ruleId"`
	RuleName         string `json:"ruleName"`
	PublicIPAddress  string `json:"publicIpAddress"`
	PrivateIPAddress string `json:"privateIpAddress"`
	PublicPort       int    `json:"publicPort"`
	PrivatePort      int    `json:"privatePort"`
	Status           string `json:"status"`
	Protocol         string `json:"protocol"`
}

type NatGateway struct {
	ID            string   `json:"id"`
	Name          string   `json:"name"`
	VpcID         string   `json:"vpcId"`
	CuNum         int64    `json:"cuNum"`
	Status        string   `json:"status"`
	Eips          []string `json:"eips"`
	DnatEips      []string `json:"dnatEips"`
	PaymentTiming string   `json:"paymentTiming"`
	ExpiredTime   string   `json:"expiredTime"`
	Tags          []Tag    `json:"tags"`
}

type SnatTable struct {
	RuleID           string   `json:"ruleId"`
	RuleName         string   `json:"ruleName"`
	PublicIPSAddress []string `json:"publicIpsAddress"`
	SourceCIDR       string   `json:"sourceCIDR"`
	Status           string   `json:"status"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NATGraph, error) {
	original := &NAT{}
	resource := &model.NATGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NatGateway.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nat", original.NatGateway.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nat"
	resource.Name = original.NatGateway.Name
	resource.OriginalLabels = lo.Map(original.NatGateway.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Status = strings.ToLower(original.NatGateway.Status)
	var eipList []string
	eipList = append(eipList, original.NatGateway.Eips...)
	eipList = append(eipList, original.NatGateway.DnatEips...)
	eipList = append(eipList, lo.Flatten(lo.Map(original.SnatTable, func(snatTable SnatTable, _ int) []string {
		return snatTable.PublicIPSAddress
	}))...)
	eipList = append(eipList, lo.Map(original.DnatTable, func(dnatTable DnatTable, _ int) string {
		return dnatTable.PublicIPAddress
	})...)
	resource.EIP = lo.Map(lo.Uniq(eipList), func(eip string, _ int) *model.EipGraph {
		return &model.EipGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eip", eip),
				TargetUID: resource.UID,
			},
		}
	})

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.NatGateway.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.SNATRules = lo.FilterMap(original.SnatTable, func(snatTable SnatTable, _ int) (*model.SNATRuleGraph, bool) {
		natUID := utils.GenerateUID(resource.Provider, "snat_rule", snatTable.RuleID)
		ipRange, err := provider_utils.ParseIPRange(snatTable.SourceCIDR, false, natUID)
		if err != nil {
			logger.Errorf("parse snat table ip range error, snatTable: %v, error: %v", snatTable, err)
			return nil, false
		}

		return &model.SNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       natUID,
				TargetUID: resource.UID,
			},
			RuleID:  snatTable.RuleID,
			Name:    snatTable.RuleName,
			Status:  strings.ToLower(snatTable.Status),
			IPRange: []*model.IPRangeGraph{ipRange},
		}, true
	})

	resource.DNATRules = lo.Map(original.DnatTable, func(dnatTable DnatTable, _ int) *model.DNATRuleGraph {
		return &model.DNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "dnat_rule", dnatTable.RuleID),
				TargetUID: resource.UID,
			},
			RuleID:       dnatTable.RuleID,
			Name:         dnatTable.RuleName,
			Status:       strings.ToLower(dnatTable.Status),
			Protocol:     strings.ToLower(dnatTable.Protocol),
			InternalIP:   dnatTable.PrivateIPAddress,
			InternalPort: dnatTable.PrivatePort,
			ExternalIP:   dnatTable.PublicIPAddress,
			ExternalPort: dnatTable.PublicPort,
		}
	})

	return resource, nil
}
