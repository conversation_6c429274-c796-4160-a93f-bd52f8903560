package k8s

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "k8s"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "k8s_cluster_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["k8s_list"] = append(resourceData["k8s_list"], utils.GenParamsFromStruct(resource))

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, k8sSchema, resourceData["k8s_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
}

type K8S struct {
	Cluster         Cluster `json:"cluster"`
	AuditLogEnabled bool    `json:"audit_log_enabled"`
}

type Cluster struct {
	Spec      Spec   `json:"spec"`
	Status    Status `json:"status"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

type Spec struct {
	ClusterID              string                 `json:"clusterID"`
	ClusterName            string                 `json:"clusterName"`
	ClusterType            string                 `json:"clusterType"`
	Description            string                 `json:"description"`
	K8SVersion             string                 `json:"k8sVersion"`
	VpcID                  string                 `json:"vpcID"`
	VpcCIDR                string                 `json:"vpcCIDR"`
	Plugins                []string               `json:"plugins"`
	MasterConfig           MasterConfig           `json:"masterConfig"`
	ContainerNetworkConfig ContainerNetworkConfig `json:"containerNetworkConfig"`
	Tags                   []Tag                  `json:"tags"`
}

type ContainerNetworkConfig struct {
	Mode                 string          `json:"mode"`
	EniVPCSubnetIDs      EniVPCSubnetIDs `json:"eniVPCSubnetIDs"`
	EniSecurityGroupID   string          `json:"eniSecurityGroupID"`
	IPVersion            string          `json:"ipVersion"`
	LBServiceVPCSubnetID string          `json:"lbServiceVPCSubnetID"`
	NodePortRangeMin     int64           `json:"nodePortRangeMin"`
	NodePortRangeMax     int64           `json:"nodePortRangeMax"`
	ClusterPodCIDR       string          `json:"clusterPodCIDR"`
	ClusterIPServiceCIDR string          `json:"clusterIPServiceCIDR"`
	MaxPodsPerNode       int64           `json:"maxPodsPerNode"`
	KubeProxyMode        string          `json:"kubeProxyMode"`
}

type EniVPCSubnetIDs map[string][]string

type MasterConfig struct {
	MasterType                 string                     `json:"masterType"`
	ClusterBLBVPCSubnetID      string                     `json:"clusterBLBVPCSubnetID"`
	ManagedClusterMasterOption ManagedClusterMasterOption `json:"managedClusterMasterOption"`
}

type ManagedClusterMasterOption struct {
}

type Status struct {
	ClusterBLB   ClusterBLB `json:"clusterBLB"`
	ClusterPhase string     `json:"clusterPhase"`
	NodeNum      int64      `json:"nodeNum"`
}

type ClusterBLB struct {
	ID    string `json:"id"`
	VpcIP string `json:"vpcIP"`
	Eip   string `json:"eip"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.K8SGraph, error) {
	original := &K8S{}
	resource := &model.K8SGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cluster.Spec.ClusterID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "kubernetes", original.Cluster.Spec.ClusterID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "kubernetes"
	resource.Name = original.Cluster.Spec.ClusterName
	resource.OriginalLabels = lo.Map(original.Cluster.Spec.Tags, func(e Tag, _ int) *model.KVGraph {
		return utils.NewLabel(e.TagKey, e.TagValue, resource.UID)
	})

	resource.Status = lo.Ternary(strings.EqualFold(original.Cluster.Status.ClusterPhase, "Running"), "running", "stopped")
	resource.EngineVersion = original.Cluster.Spec.K8SVersion
	if len(original.Cluster.Status.ClusterBLB.VpcIP) > 0 {
		resource.PrivateEndpoint = fmt.Sprintf("https://%s:6443", original.Cluster.Status.ClusterBLB.VpcIP)
	}
	if len(original.Cluster.Status.ClusterBLB.Eip) > 0 {
		resource.PublicEndpoint = fmt.Sprintf("https://%s:6443", original.Cluster.Status.ClusterBLB.Eip)
	}
	resource.PodCIDRs = append(resource.PodCIDRs, original.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR)
	resource.ServiceCIDRs = []string{original.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR}
	resource.AuditLogEnabled = lo.ToPtr(original.AuditLogEnabled)

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Cluster.Spec.VpcID),
			TargetUID: resource.UID,
		},
	})

	for _, subnets := range original.Cluster.Spec.ContainerNetworkConfig.EniVPCSubnetIDs {
		resource.Subnet = append(resource.Subnet, lo.Map(subnets, func(e string, _ int) *model.SubnetGraph {
			return &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", e),
					TargetUID: resource.UID,
				},
			}
		})...)
	}
	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.Cluster.Status.ClusterBLB.ID),
			TargetUID: resource.UID,
		},
	})

	return resource, nil
}
