package user

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "user"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "iam_user_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["user_list"] = append(resourceData["user_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, userSchema, userData["user_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
}

type User struct {
	AccessKeys   []AccessKey  `json:"accessKeys"`
	LoginProfile LoginProfile `json:"loginProfile"`
	Policies     []UserPolicy `json:"policies"`
	User         UserClass    `json:"user"`
}

type AccessKey struct {
	ID           string `json:"id"`
	Secret       string `json:"secret"`
	CreateTime   string `json:"createTime"`
	LastUsedTime string `json:"lastUsedTime"`
	Enabled      bool   `json:"enabled"`
	Description  string `json:"description"`
}

type LoginProfile struct {
	NeedResetPassword bool `json:"needResetPassword"`
	EnabledLogin      bool `json:"enabledLogin"`
	EnabledLoginMfa   bool `json:"enabledLoginMfa"`
}

type UserPolicy struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	CreateTime  string `json:"createTime"`
	Description string `json:"description"`
	Document    string `json:"document"`
}

type UserClass struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	CreateTime  string `json:"createTime"`
	Description string `json:"description"`
	Enabled     bool   `json:"enabled"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.UserGraph, error) {
	original := &User{}
	resource := &model.UserGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.User.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "user", original.User.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "user"
	resource.Name = original.User.Name
	resource.Description = original.User.Description

	resource.DisplayName = original.User.Name
	resource.Enabled = original.User.Enabled
	t, _ := time.Parse(time.RFC3339, original.User.CreateTime)
	resource.CreatedAt = lo.Ternary(t.UnixMilli() > 0, t.UnixMilli(), 0)
	resource.MFAEnabled = original.LoginProfile.EnabledLoginMfa
	resource.LoginAllowed = original.LoginProfile.EnabledLogin

	resource.Policy = lo.Map(original.Policies, func(policy UserPolicy, _ int) *model.PolicyGraph {
		return &model.PolicyGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "policy", policy.Name),
				TargetUID: resource.UID,
			},
		}
	})
	resource.AccessKey = lo.Map(original.AccessKeys, func(key AccessKey, _ int) *model.AkGraph {
		return &model.AkGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ak", key.ID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
