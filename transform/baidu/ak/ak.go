package ak

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ak"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "iam_ak_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ak_list"] = append(resourceData["ak_list"], utils.GenParamsFromStruct(resource))
		resourceData["user_list"] = append(resourceData["user_list"],
			utils.GenParamsFromStructSlice(resource.User)...,
		)
	}
	return resourceData, nil
}

func updateResources(akData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, akSchema, akData["ak_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, akUserSchema, akData["user_list"], map[string]any{"last_updated": "test"})
}

type AK struct {
	AccessKey AKAccessKey `json:"access_key"`
	UserName  string      `json:"user_name"`
}

type AKAccessKey struct {
	ID           string `json:"id"`
	Secret       string `json:"secret"`
	CreateTime   string `json:"createTime"`
	LastUsedTime string `json:"lastUsedTime"`
	Enabled      bool   `json:"enabled"`
	Description  string `json:"description"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.AkGraph, error) {
	original := &AK{}
	resource := &model.AkGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.AccessKey.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ak", original.AccessKey.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ak"
	resource.Name = original.AccessKey.ID

	resource.Enabled = lo.ToPtr(original.AccessKey.Enabled)
	resource.User = append(resource.User, &model.UserGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "user", original.UserName),
			TargetUID: resource.UID,
		},
	})
	t, _ := time.Parse(time.RFC3339, original.AccessKey.CreateTime)
	resource.CreatedAt = lo.Ternary(t.UnixMilli() > 0, t.UnixMilli(), 0)
	t, _ = time.Parse(time.RFC3339, original.AccessKey.LastUsedTime)
	resource.LastUsedAt = lo.Ternary(t.UnixMilli() > resource.CreatedAt, t.UnixMilli(), resource.CreatedAt)

	return resource, nil
}
