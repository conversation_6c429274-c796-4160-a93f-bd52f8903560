package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var snapshotLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "snapshot"})

func NewSnapshotService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_snapshot_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSnapshot,
		UpdateResources: updateSnapshots,
	}
}

func NewBBCSnapshotService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bbc_snapshot_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSnapshot,
		UpdateResources: updateSnapshots,
	}
}

func transformSnapshot(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSnapshot(assetMsg)
		if err != nil {
			snapshotLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["snapshot_list"] = append(resourceData["snapshot_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateSnapshots(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, snapshotSchema, resourceData["snapshot_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Snapshot struct {
	Snapshot SnapshotClass `json:"snapshot"`
}

type SnapshotClass struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	SizeInGB     int64  `json:"sizeInGB"`
	CreateTime   string `json:"createTime"`
	Status       string `json:"status"`
	CreateMethod string `json:"createMethod"`
	VolumeID     string `json:"volumeId"`
	Desc         string `json:"desc"`
	ExpireTime   string `json:"expireTime"`
	Package      bool   `json:"package"`
	TemplateID   string `json:"templateId"`
	InsnapID     string `json:"insnapId"`
	Encrypted    bool   `json:"encrypted"`
}

func parseSnapshot(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Snapshot{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Snapshot.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Snapshot.Name
	resource.Description = original.Snapshot.Desc

	resource.Class = "snapshot"
	resource.Status = lo.Ternary(strings.EqualFold(original.Snapshot.Status, "available"), "available", "unavailable")
	resource.Encrypted = original.Snapshot.Encrypted

	return resource, nil
}
