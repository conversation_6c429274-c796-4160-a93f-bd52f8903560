package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var imageLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "image"})

func NewImageService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_image_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformImage,
		UpdateResources: updateImages,
	}
}

func NewBBCImageService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bbc_image_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformImage,
		UpdateResources: updateImages,
	}
}

func transformImage(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseImage(assetMsg)
		if err != nil {
			imageLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["image_list"] = append(resourceData["image_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateImages(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Image struct {
	Image ImageClass `json:"image"`
}

type ImageClass struct {
	OSVersion      string      `json:"osVersion"`
	OSArch         string      `json:"osArch"`
	OSLang         string      `json:"osLang"`
	Status         string      `json:"status"`
	Desc           string      `json:"desc"`
	ID             string      `json:"id"`
	Name           string      `json:"name"`
	OSName         string      `json:"osName"`
	OSBuild        string      `json:"osBuild"`
	CreateTime     string      `json:"createTime"`
	Type           string      `json:"type"`
	OSType         string      `json:"osType"`
	SpecialVersion string      `json:"specialVersion"`
	Package        bool        `json:"package"`
	Encrypted      bool        `json:"encrypted"`
	MinDiskGB      int64       `json:"minDiskGb"`
	DiskSize       int64       `json:"diskSize"`
	Snapshots      interface{} `json:"snapshots"`
}

func parseImage(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Image{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Image.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Image.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Image.Name
	resource.Description = original.Image.Desc

	resource.Class = "image"
	resource.Status = lo.Ternary(strings.EqualFold(original.Image.Status, "available"), "available", "unavailable")
	resource.Encrypted = original.Image.Encrypted

	return resource, nil
}
