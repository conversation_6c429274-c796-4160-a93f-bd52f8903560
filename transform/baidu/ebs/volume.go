package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var volumeLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "volume"})

func NewVolumeService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_disk_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformVolume,
		UpdateResources: updateVolumes,
	}
}

func NewBBCVolumeService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bbc_disk_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformVolume,
		UpdateResources: updateVolumes,
	}
}

func transformVolume(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseVolume(assetMsg)
		if err != nil {
			volumeLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["volume_list"] = append(resourceData["volume_list"], utils.GenParamsFromStruct(resource))

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["image_list"] = append(resourceData["image_list"],
			utils.GenParamsFromStructSlice(resource.Image)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateVolumes(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, volumeSchema, resourceData["volume_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsVolumeSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
}

type Disk struct {
	Disk DiskClass `json:"disk"`
}

type DiskClass struct {
	Type               string             `json:"type"`
	StorageType        string             `json:"storageType"`
	ID                 string             `json:"id"`
	Name               string             `json:"name"`
	DiskSizeInGB       int64              `json:"diskSizeInGB"`
	PaymentTiming      string             `json:"paymentTiming"`
	ExpireTime         string             `json:"expireTime"`
	Status             string             `json:"status"`
	EbcDiskSize        int64              `json:"ebcDiskSize"`
	Desc               string             `json:"desc"`
	Attachments        []Attachment       `json:"attachments"`
	ZoneName           string             `json:"zoneName"`
	AutoSnapshotPolicy AutoSnapshotPolicy `json:"autoSnapshotPolicy"`
	CreateTime         string             `json:"createTime"`
	IsSystemVolume     bool               `json:"isSystemVolume"`
	RegionID           string             `json:"regionId"`
	SourceSnapshotID   string             `json:"sourceSnapshotId"`
	SnapshotNum        string             `json:"snapshotNum"`
	Tags               []Tag              `json:"tags"`
	ResGroupInfos      []interface{}      `json:"resGroupInfos"`
	EnableAutoRenew    bool               `json:"enableAutoRenew"`
	AutoRenewTime      int64              `json:"autoRenewTime"`
	Encrypted          bool               `json:"encrypted"`
	ClusterID          string             `json:"clusterId"`
	RoleName           string             `json:"roleName"`
	CreatedFrom        string             `json:"createdFrom"`
	ReleaseTime        string             `json:"releaseTime"`
	VolumeID           string             `json:"volumeId"`
}

type Attachment struct {
	VolumeID   string `json:"volumeId"`
	InstanceID string `json:"instanceId"`
	Device     string `json:"device"`
	Serial     string `json:"serial"`
}

type AutoSnapshotPolicy struct {
	CreatedTime     string  `json:"createdTime"`
	ID              string  `json:"id"`
	Status          string  `json:"status"`
	RetentionDays   int64   `json:"retentionDays"`
	UpdatedTime     string  `json:"updatedTime"`
	DeletedTime     string  `json:"deletedTime"`
	LastExecuteTime string  `json:"lastExecuteTime"`
	VolumeCount     int64   `json:"volumeCount"`
	Name            string  `json:"name"`
	TimePoints      []int64 `json:"timePoints"`
	RepeatWeekdays  []int64 `json:"repeatWeekdays"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseVolume(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Disk{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Disk.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Disk.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Disk.Name
	resource.Description = original.Disk.Desc
	resource.OriginalLabels = lo.Map(original.Disk.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID) },
	)

	resource.Class = "volume"
	resource.Status = lo.Ternary(strings.EqualFold(original.Disk.Status, "inuse"), "inuse", "available")
	resource.Encrypted = original.Disk.Encrypted
	resource.AutoSnapshotEnabled = lo.ToPtr(strings.EqualFold(original.Disk.AutoSnapshotPolicy.Status, "active"))
	resource.ECS = lo.Map(original.Disk.Attachments,
		func(e Attachment, _ int) *model.ECSGraph {
			return &model.ECSGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "ecs", e.InstanceID),
					TargetUID: resource.UID,
				},
			}
		},
	)

	return resource, nil
}
