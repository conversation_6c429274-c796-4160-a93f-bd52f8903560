package lb_listener

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "alb_listener"})

func NewALBListenerService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_appBlbListener_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformALBListener,
		UpdateResources: updateALBListeners,
	}
}

func transformALBListener(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseALBListener(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_listener_list"] = append(resourceData["lb_listener_list"], utils.GenParamsFromStruct(resource))
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["port_range_list"] = append(resourceData["port_range_list"],
			utils.GenParamsFromStructSlice(resource.PortRange)...,
		)
		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateALBListeners(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbListenerSchema, resourceData["lb_listener_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbFromListenerSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, portRangeSchema, resourceData["port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type AppBlbListener struct {
	BlbID    string              `json:"blbId"`
	Listener AppBLBListenerClass `json:"listener"`
}

type AppBLBListenerClass struct {
	ListenerPort          int         `json:"listenerPort"`
	ListenerType          string      `json:"listenerType"`
	Scheduler             string      `json:"scheduler"`
	TCPSessionTimeout     int64       `json:"tcpSessionTimeout"`
	UDPSessionTimeout     int64       `json:"udpSessionTimeout"`
	KeepSession           bool        `json:"keepSession"`
	KeepSessionType       string      `json:"keepSessionType"`
	KeepSessionTimeout    int64       `json:"keepSessionTimeout"`
	KeepSessionCookieName string      `json:"keepSessionCookieName"`
	XForwardFor           bool        `json:"xForwardFor"`
	XForwardedProto       bool        `json:"xForwardedProto"`
	ServerTimeout         int64       `json:"serverTimeout"`
	RedirectPort          int64       `json:"redirectPort"`
	CERTIDS               interface{} `json:"certIds"`
	EncryptionType        string      `json:"encryptionType"`
	EncryptionProtocols   interface{} `json:"encryptionProtocols"`
	AppliedCiphers        string      `json:"appliedCiphers"`
	DualAuth              bool        `json:"dualAuth"`
	ClientCERTIDS         interface{} `json:"clientCertIds"`
}

func parseALBListener(assetMsg *model.AssetMessage) (*model.LbListenerGraph, error) {
	original := &AppBlbListener{}
	resource := &model.LbListenerGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb-listener", fmt.Sprintf("%s+%d+%s", original.BlbID, original.Listener.ListenerPort, original.Listener.ListenerType))
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb-listener"
	resource.Name = fmt.Sprintf("%s@%d/%s", original.BlbID, original.Listener.ListenerPort, original.Listener.ListenerType)

	resource.Protocol = strings.ToLower(original.Listener.ListenerType)
	resource.Status = "active"
	resource.CertExists = lo.ToPtr(original.Listener.CERTIDS != nil)

	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.BlbID),
			TargetUID: resource.UID,
		},
	})
	resource.PortRange = append(resource.PortRange, utils.NewPortRange(original.Listener.ListenerPort, original.Listener.ListenerPort, resource.UID))
	return resource, nil
}
