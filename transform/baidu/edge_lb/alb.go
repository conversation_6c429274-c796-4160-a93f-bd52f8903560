package edge_lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var albLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "alb"})

func NewALBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bec_appBlb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformALB,
		UpdateResources: updateALBResources,
	}
}

func transformALB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseALB(assetMsg)
		if err != nil {
			albLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateALBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type EdgeAppBLB struct {
	AppBlbDetail EdgeAppBlbDetail `json:"appBlbDetail"`
}

type EdgeAppBlbDetail struct {
	Address    string               `json:"address"`
	BlbId      string               `json:"blbId"`
	Cidr       string               `json:"cidr"`
	CreateTime string               `json:"createTime"`
	Desc       string               `json:"desc"`
	Listener   []EdgeAppBlbListener `json:"listener"`
	Name       string               `json:"name"`
	PublicIp   string               `json:"publicIp"`
	RegionId   string               `json:"regionId"`
	Status     string               `json:"status"`
	SubnetCidr string               `json:"subnetCidr"`
	SubnetId   string               `json:"subnetId"`
	VpcId      string               `json:"vpcId"`
	SubnetName string               `json:"subnetName"`
	VpcName    string               `json:"vpcName"`
}

type EdgeAppBlbListener struct {
	Port string `json:"port"`
	Type string `json:"type"`
}

func parseALB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &EdgeAppBLB{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.AppBlbDetail.BlbId
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.AppBlbDetail.BlbId)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.AppBlbDetail.Name
	resource.Description = original.AppBlbDetail.Desc

	resource.Class = "edge-application"
	resource.DeleteProtection = lo.ToPtr(false)
	resource.Status = lo.Ternary(strings.EqualFold(original.AppBlbDetail.Status, "available"), "active", "inactive")
	if len(original.AppBlbDetail.PublicIp) > 0 {
		resource.PublicIPList = []string{original.AppBlbDetail.PublicIp}
	}
	if len(original.AppBlbDetail.Address) > 0 {
		resource.PrivateIPList = []string{original.AppBlbDetail.Address}
	}

	return resource, nil
}
