package edge_lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var blbLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "blb"})

func NewBLBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bec_blb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformBLB,
		UpdateResources: updateBLBResources,
	}
}

func transformBLB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseBLB(assetMsg)
		if err != nil {
			blbLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateBLBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type EdgeLB struct {
	Blb EdgeBlbDetail `json:"blbDetail"`
}

type EdgeBlbDetail struct {
	BlbId                string            `json:"blbId"`
	BlbName              string            `json:"blbName"`
	Status               string            `json:"status"`
	LbType               string            `json:"lbType"`
	Region               string            `json:"region"`
	ServiceProvider      string            `json:"serviceProvider"`
	City                 string            `json:"city"`
	RegionId             string            `json:"regionId"`
	PublicIp             string            `json:"publicIp"`
	CmPublicIP           string            `json:"cmPublicIP"`
	CtPublicIP           string            `json:"ctPublicIP"`
	UnPublicIP           string            `json:"unPublicIP"`
	InternalIp           string            `json:"internalIp"`
	Ports                []EdgeBlbListener `json:"ports"`
	PodCount             int               `json:"podCount"`
	BandwidthInMbpsLimit int               `json:"bandwidthInMbpsLimit"`
	CreateTime           string            `json:"createTime"`
}

type EdgeBlbListener struct {
	Protocol         string `json:"protocol,omitempty"`
	Port             int    `json:"port,omitempty"`
	BackendPort      int    `json:"backendPort,omitempty"`
	KeepaliveTimeout int    `json:"keepaliveTimeout,omitempty"`
	Scheduler        string `json:"scheduler,omitempty"`
	EnableCipTTM     bool   `json:"enableCipTTM,omitempty"`
	EnableVipTTM     bool   `json:"enableVipTTM,omitempty"`

	// health check config
	HealthCheckInterval  int    `json:"healthCheckInterval,omitempty"`
	HealthCheckRetry     int    `json:"healthCheckRetry,omitempty"`
	HealthCheckTimeout   int    `json:"healthCheckTimeout,omitempty"`
	UdpHealthCheckString string `json:"udpHealthCheckString,omitempty"`
	HealthCheckType      string `json:"healthCheckType,omitempty"`
}

func parseBLB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &EdgeLB{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Blb.BlbId
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.Blb.BlbId)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.Blb.BlbName

	resource.Class = "edge-mixed"
	resource.DeleteProtection = lo.ToPtr(false)
	resource.Status = lo.Ternary(strings.EqualFold(original.Blb.Status, "available"), "active", "inactive")

	if len(original.Blb.PublicIp) > 0 {
		resource.PublicIPList = []string{original.Blb.PublicIp}
	}
	if len(original.Blb.InternalIp) > 0 {
		resource.PrivateIPList = []string{original.Blb.InternalIp}
	}

	return resource, nil
}
