package subnet

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "subnet"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_subnet_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["subnet_list"] = append(resourceData["subnet_list"], utils.GenParamsFromStruct(resource))

		resourceData["acl_list"] = append(resourceData["acl_list"], utils.GenParamsFromStructSlice(resource.ACL)...)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.DstPortRange)
			})...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, aclSchema, resourceData["acl_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIpRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIpRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type Subnet struct {
	ACL    SubnetACL   `json:"acl"`
	Subnet SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	SubnetID   string    `json:"subnetId"`
	SubnetName string    `json:"subnetName"`
	SubnetCIDR string    `json:"subnetCidr"`
	ACLRules   []ACLRule `json:"aclRules"`
}

type ACLRule struct {
	ID                   string `json:"id"`
	SubnetID             string `json:"subnetId"`
	Description          string `json:"description"`
	Protocol             string `json:"protocol"`
	SourceIPAddress      string `json:"sourceIpAddress"`
	DestinationIPAddress string `json:"destinationIpAddress"`
	SourcePort           string `json:"sourcePort"`
	DestinationPort      string `json:"destinationPort"`
	Position             int    `json:"position"`
	Direction            string `json:"direction"`
	Action               string `json:"action"`
}

type SubnetClass struct {
	SubnetID    string `json:"subnetId"`
	Name        string `json:"name"`
	ZoneName    string `json:"zoneName"`
	CIDR        string `json:"cidr"`
	Ipv6CIDR    string `json:"ipv6Cidr"`
	VpcID       string `json:"vpcId"`
	SubnetType  string `json:"subnetType"`
	Description string `json:"description"`
	CreatedTime string `json:"createdTime"`
	AvailableIP int    `json:"availableIp"`
	Tags        []Tag  `json:"tags"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.SubnetGraph, error) {
	original := &Subnet{}
	resource := &model.SubnetGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Subnet.SubnetID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "subnet", original.Subnet.SubnetID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "subnet"
	resource.Name = original.Subnet.Name
	resource.Description = original.Subnet.Description
	resource.OriginalLabels = lo.Map(original.Subnet.Tags, func(e Tag, _ int) *model.KVGraph {
		return utils.NewLabel(e.TagKey, e.TagValue, resource.UID)
	})

	_, resource.IsDefault = lo.Find(original.Subnet.Tags, func(e Tag) bool { return e.TagKey == "默认项目" })
	resource.CIDR = original.Subnet.CIDR
	resource.CIDRv6 = original.Subnet.Ipv6CIDR
	resource.AvailableIPCount = original.Subnet.AvailableIP

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Subnet.VpcID),
			TargetUID: resource.UID,
		},
	})
	for _, rule := range original.ACL.ACLRules {
		aclRule, err := parseACL(rule, resource.UID)
		if err != nil {
			return nil, err
		}
		resource.ACL = append(resource.ACL, aclRule)
	}

	return resource, nil
}

func parseACL(rule ACLRule, targetUID string) (*model.ACLRuleGraph, error) {
	result := &model.ACLRuleGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(provider_utils.ProviderID, "acl_rule", rule.ID),
			TargetUID: targetUID,
		},
		RuleID:      rule.ID,
		Description: rule.Description,
		Protocol:    rule.Protocol,
		IPVersion:   "IPv4",
		Policy:      lo.Ternary(rule.Action == "allow", "accept", "drop"),
		Priority:    rule.Position,
		Direction:   rule.Direction,
	}
	if ipRange, err := provider_utils.ParseIPRange(rule.SourceIPAddress, false, result.UID); err != nil {
		return nil, err
	} else {
		result.SrcIPRange = []*model.IPRangeGraph{ipRange}
	}
	if ipRange, err := provider_utils.ParseIPRange(rule.DestinationIPAddress, false, result.UID); err != nil {
		return nil, err
	} else {
		result.DstIPRange = []*model.IPRangeGraph{ipRange}
	}
	if portRange, err := provider_utils.ParsePortRange(rule.SourcePort, result.UID); err != nil {
		return nil, err
	} else {
		result.SrcPortRange = []*model.PortRangeGraph{portRange}
	}
	if portRange, err := provider_utils.ParsePortRange(rule.DestinationPort, result.UID); err != nil {
		return nil, err
	} else {
		result.DstPortRange = []*model.PortRangeGraph{portRange}
	}
	return result, nil
}
