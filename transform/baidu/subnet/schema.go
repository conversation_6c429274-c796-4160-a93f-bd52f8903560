package subnet

import "AssetStandardizer/graph"

var subnetSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"cidr":               {Name: "cidr"},
		"cidr_v6":            {Name: "cidr_v6"},
		"is_default":         {Name: "is_default"},
		"available_ip_count": {Name: "available_ip_count"},
	},
}

var aclSchema = graph.NodeSchema{
	Label: "SubnetACL",
	Properties: map[string]graph.PropertyRef{
		"uid":         {Name: "uid"},
		"rule_id":     {Name: "rule_id"},
		"name":        {Name: "name"},
		"description": {Name: "description"},
		"protocol":    {Name: "protocol"},
		"ip_version":  {Name: "ip_version"},
		"policy":      {Name: "policy"},
		"direction":   {Name: "direction"},
		"priority":    {Name: "priority"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Subnet",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ATTACHED_TO",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var srcIpRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SubnetACL",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_SRC_IP_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var srcPortRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SubnetACL",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_SRC_PORT_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dstIpRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SubnetACL",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_DST_IP_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dstPortRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SubnetACL",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_DST_PORT_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
