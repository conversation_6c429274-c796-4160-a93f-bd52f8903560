package function

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "function"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cfc_function_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["function_list"] = append(resourceData["function_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, functionSchema, resourceData["function_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Function struct {
	Function FunctionClass `json:"cfc"`
}

type FunctionClass struct {
	Uid                   string            `json:"Uid"`
	Description           string            `json:"Description"`
	FunctionBrn           string            `json:"FunctionBrn"`
	Region                string            `json:"Region"`
	Timeout               int64             `json:"Timeout"`
	VersionDesc           string            `json:"VersionDesc"`
	UpdatedAt             string            `json:"UpdatedAt"`
	LastModified          string            `json:"LastModified"`
	SourceTag             string            `json:"SourceTag"`
	BlueprintTag          string            `json:"BlueprintTag"`
	CodeSha256            string            `json:"CodeSha256"`
	LayerSha256           string            `json:"LayerSha256"`
	CodeSize              int64             `json:"CodeSize"`
	FunctionArn           string            `json:"FunctionArn"`
	FunctionName          string            `json:"FunctionName"`
	ServiceName           string            `json:"ServiceName"`
	Handler               string            `json:"Handler"`
	Version               string            `json:"Version"`
	Runtime               string            `json:"Runtime"`
	MemorySize            int64             `json:"MemorySize"`
	Environment           Environment       `json:"Environment"`
	VpcConfig             VpcConfig         `json:"VpcConfig"`
	CommitID              string            `json:"CommitId"`
	Role                  string            `json:"Role"`
	LogType               string            `json:"LogType"`
	LogBosDir             string            `json:"LogBosDir"`
	BlsLogSet             string            `json:"BlsLogSet"`
	Layers                []interface{}     `json:"Layers"`
	PodConcurrentQuota    int64             `json:"PodConcurrentQuota"`
	FunctionMaxTimeout    interface{}       `json:"FunctionMaxTimeout"`
	AsyncInvokeConfig     AsyncInvokeConfig `json:"AsyncInvokeConfig"`
	CFSConfig             CFSConfig         `json:"CFSConfig"`
	Ban                   interface{}       `json:"Ban"`
	LimitMemorySize       interface{}       `json:"LimitMemorySize"`
	LimitTimeout          interface{}       `json:"LimitTimeout"`
	LimitMaxRetryAttempts interface{}       `json:"LimitMaxRetryAttempts"`
}

type AsyncInvokeConfig struct {
	MaxRetryIntervalInSeconds int64       `json:"MaxRetryIntervalInSeconds"`
	MaxRetryAttempts          int64       `json:"MaxRetryAttempts"`
	OnSuccess                 interface{} `json:"OnSuccess"`
	OnFailure                 interface{} `json:"OnFailure"`
}

type VpcConfig struct {
	VpcID            string
	SubnetIds        []string
	SecurityGroupIds []string
}

type CFSConfig struct {
	FSName     string      `json:"FsName"`
	FSID       string      `json:"FsId"`
	SubnetID   interface{} `json:"SubnetID"`
	Domain     interface{} `json:"Domain"`
	RemotePath string      `json:"RemotePath"`
	LocalPath  string      `json:"LocalPath"`
	Ovip       interface{} `json:"Ovip"`
	VpcID      interface{} `json:"VpcId"`
}

type Environment struct {
	Variables Variables `json:"Variables"`
}

type Variables struct {
}

func parseOne(assetMsg *model.AssetMessage) (*model.FunctionGraph, error) {
	original := &Function{}
	resource := &model.FunctionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Function.FunctionArn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "function", original.Function.FunctionArn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "function"
	resource.Name = original.Function.FunctionName
	resource.Description = original.Function.Description

	resource.DirectInternetAccessAllowed = true
	resource.Runtime = strings.ToLower(original.Function.Runtime)
	resource.LogEnabled = original.Function.LogType != "" && original.Function.LogType != "none"
	resource.LogPath = original.Function.LogBosDir

	if original.Function.CFSConfig.FSID != "" {
		resource.NasMountPoints = []string{fmt.Sprintf("%s:%s", original.Function.CFSConfig.FSName, original.Function.CFSConfig.RemotePath)}
	}

	if original.Function.VpcConfig.VpcID != "" {
		resource.DirectInternetAccessAllowed = false
		resource.VPC = append(resource.VPC, &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", original.Function.VpcConfig.VpcID),
				TargetUID: resource.UID,
			},
		})
		resource.Subnet = append(resource.Subnet,
			lo.Map(original.Function.VpcConfig.SubnetIds, func(e string, _ int) *model.SubnetGraph {
				return &model.SubnetGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "subnet", e),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
		resource.SG = append(resource.SG,
			lo.Map(original.Function.VpcConfig.SecurityGroupIds, func(e string, _ int) *model.SGGraph {
				return &model.SGGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "security-group", e),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
	}

	return resource, nil
}
