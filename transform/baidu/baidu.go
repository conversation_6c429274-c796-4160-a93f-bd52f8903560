package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/baidu/ak"
	"AssetStandardizer/transform/baidu/audit_log"
	"AssetStandardizer/transform/baidu/bucket"
	"AssetStandardizer/transform/baidu/cdn"
	"AssetStandardizer/transform/baidu/cen"
	"AssetStandardizer/transform/baidu/cfw"
	"AssetStandardizer/transform/baidu/ebs"
	"AssetStandardizer/transform/baidu/ecs"
	"AssetStandardizer/transform/baidu/edge_ebs"
	"AssetStandardizer/transform/baidu/edge_ecs"
	"AssetStandardizer/transform/baidu/edge_eni"
	"AssetStandardizer/transform/baidu/edge_lb"
	"AssetStandardizer/transform/baidu/edge_lb_listener"
	"AssetStandardizer/transform/baidu/edge_nat"
	"AssetStandardizer/transform/baidu/edge_security_group"
	"AssetStandardizer/transform/baidu/edge_subnet"
	"AssetStandardizer/transform/baidu/edge_vpc"
	"AssetStandardizer/transform/baidu/eip"
	"AssetStandardizer/transform/baidu/elasticsearch"
	"AssetStandardizer/transform/baidu/eni"
	"AssetStandardizer/transform/baidu/function"
	"AssetStandardizer/transform/baidu/k8s"
	"AssetStandardizer/transform/baidu/lb"
	"AssetStandardizer/transform/baidu/lb_listener"
	"AssetStandardizer/transform/baidu/mq"
	"AssetStandardizer/transform/baidu/nas"
	"AssetStandardizer/transform/baidu/nat"
	"AssetStandardizer/transform/baidu/peer_connection"
	"AssetStandardizer/transform/baidu/policy"
	"AssetStandardizer/transform/baidu/rds"
	"AssetStandardizer/transform/baidu/role"
	"AssetStandardizer/transform/baidu/security_group"
	"AssetStandardizer/transform/baidu/subnet"
	"AssetStandardizer/transform/baidu/user"
	"AssetStandardizer/transform/baidu/vpc"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

const ProviderID = "baidu"

var AssetMessageChan = make(chan *model.AssetMessage, 100)
var AssetTypeMap = map[string]chan *model.AssetMessage{}

func init() {
	resourceServices := []model.ResourceService{
		eip.NewResourceService(),
		ak.NewResourceService(),
		user.NewResourceService(),
		policy.NewResourceService(),
		role.NewResourceService(),
		ecs.NewResourceService(),
		security_group.NewSGService(),
		security_group.NewESGService(),
		vpc.NewResourceService(),
		subnet.NewResourceService(),
		lb.NewBLBService(),
		lb.NewALBService(),
		lb_listener.NewBLBListenerService(),
		lb_listener.NewALBListenerService(),
		eni.NewResourceService(),
		nat.NewResourceService(),
		bucket.NewResourceService(),
		peer_connection.NewResourceService(),
		cdn.NewCDNService(),
		cdn.NewAbroadCDNService(),
		ebs.NewVolumeService(),
		ebs.NewImageService(),
		ebs.NewSnapshotService(),
		ebs.NewBBCImageService(),
		ebs.NewBBCVolumeService(),
		ebs.NewBBCSnapshotService(),
		cen.NewResourceService(),
		rds.NewResourceService(),
		elasticsearch.NewResourceService(),
		function.NewResourceService(),
		nas.NewResourceService(),
		k8s.NewResourceService(),
		cfw.NewResourceService(),
		audit_log.NewResourceService(),
		mq.NewResourceService(),
		edge_ecs.NewResourceService(),
		edge_ebs.NewVolumeService(),
		edge_ebs.NewImageService(),
		edge_ebs.NewSnapshotService(),
		edge_lb.NewBLBService(),
		edge_lb.NewALBService(),
		edge_lb_listener.NewBLBListenerService(),
		edge_lb_listener.NewALBListenerService(),
		edge_eni.NewResourceService(),
		edge_nat.NewResourceService(),
		edge_security_group.NewSGService(),
		edge_vpc.NewResourceService(),
		edge_subnet.NewResourceService(),
	}

	for _, resourceService := range resourceServices {
		if err := resourceService.Validate(); err != nil {
			logger.Errorf("validate resource service failed: %s", err)
			continue
		}
		resourceService.Start()
		AssetTypeMap[resourceService.GetMessageType()] = resourceService.GetAssetMsgCh()
	}
	go dispatch()
}

func dispatch() {
	for assetMsg := range AssetMessageChan {
		if assetMsgChan, ok := AssetTypeMap[assetMsg.Type]; ok {
			assetMsgChan <- assetMsg
		} else {
			logger.Errorf("unknown type: %s", assetMsg.Type)
			continue
		}
	}
}

// func Parse(raw map[string]any) (model.Asset, error) {
// 	switch utils.ReadFieldString(raw, "type") {
// 	case "eip_eip_aggregated":
// 		return parseEip(raw), nil
// 	case "ecs_bccInstance_aggregated":
// 		return parseEcs(raw), nil
// 	case "lb_blb_aggregated":
// 		return parseBlb(raw), nil
// 	case "lb_appBlb_aggregated":
// 		return parseAppBlb(raw), nil
// 	case "lb_blbListener_aggregated":
// 		return parseBlbListener(raw)
// 	case "lb_appBlbListener_aggregated":
// 		return parseApplbListener(raw)
// 	case "vpc_eni_aggregated":
// 		return parseEni(raw), nil
// 	case "bcc_sg_aggregated":
// 		return parseSG(raw), nil
// 	case "bcc_esg_aggregated":
// 		return parseESG(raw), nil
// 	case "iam_ak_aggregated":
// 		return parseAccessKey(raw), nil
// 	case "rds_instance_aggregated":
// 		return parseRds(raw), nil
// 	case "iam_user_aggregated":
// 		return parseUser(raw), nil
// 	case "iam_role_aggregated":
// 		return parseRole(raw), nil
// 	case "iam_raw_policy":
// 		return parsePolicy(raw), nil
// 	case "nat_gateway_aggregated":
// 		return parseNat(raw), nil
// 	case "bos_bucket_aggregated":
// 		return parseBucket(raw), nil
// 	case "vpc_vpc_aggregated":
// 		return parseVPC(raw), nil
// 	case "vpc_subnet_aggregated":
// 		return parseSubnet(raw), nil
// 	case "es_instance_aggregated":
// 		return parseES(raw), nil
// 	case "cfw_instance_aggregated":
// 		return parseCFW(raw), nil
// 	case "k8s_cluster_aggregated":
// 		return parseK8S(raw)
// 	case "ecs_disk_aggregated":
// 		return parseVolume(raw), nil
// 	case "ecs_image_aggregated":
// 		return parseImage(raw), nil
// 	case "ecs_snapshot_aggregated":
// 		return parseSnapshot(raw), nil
// 	case "cdn_domain_aggregated":
// 		return parseCDN(raw), nil
// 	case "cdn_abroadDomain_aggregated":
// 		return parseAbroadCDN(raw), nil
// 	case "vpc_peerConnection_aggregated":
// 		return parsePeerConnection(raw), nil
// 	case "bec_instance_aggregated":
// 		return parseBec(raw), nil
// 	case "bec_eni_aggregated":
// 		return parseEdgeEni(raw), nil
// 	case "bec_image_aggregated":
// 		return parseEdgeImage(raw), nil
// 	case "bec_disk_aggregated":
// 		return parseEdgeVolume(raw), nil
// 	case "bec_snapshot_aggregated":
// 		return parseEdgeSnapshot(raw), nil
// 	case "bec_securityGroup_aggregated":
// 		return parseEdgeSG(raw), nil
// 	case "bec_vpc_aggregated":
// 		return parseEdgeVPC(raw), nil
// 	case "bec_subnet_aggregated":
// 		return parseEdgeSubnet(raw), nil
// 	case "bec_nat_aggregated":
// 		return parseEdgeNat(raw), nil
// 	case "bec_blb_aggregated":
// 		return parseEdgeBlb(raw), nil
// 	case "bec_blbListener_aggregated":
// 		return parseEdgeBlbListener(raw)
// 	case "bec_appBlb_aggregated":
// 		return parseEdgeAppBlb(raw), nil
// 	case "bec_appBlbListener_aggregated":
// 		return parseEdgeAppBlbListener(raw)
// 	case "csn_csn_aggregated":
// 		return parseCen(raw), nil
// 	case "bbc_image_aggregated":
// 		return parseImage(raw), nil
// 	case "bbc_bbcInstance_aggregated":
// 		return parseBbc(raw), nil
// 	case "bbc_disk_aggregated":
// 		return parseVolume(raw), nil
// 	case "cfs_cfs_aggregated":
// 		return parseCFS(raw), nil
// 	case "cfc_function_aggregated":
// 		return parseFunction(raw), nil
// 	case "auditlog-entries-aggregated":
// 		return parseAuditLog(raw), nil
// 	case "mq_kafka_aggregated":
// 		return parseKafka(raw), nil

// 	default:
// 		return nil, fmt.Errorf("unknown type")
// 	}
// }
