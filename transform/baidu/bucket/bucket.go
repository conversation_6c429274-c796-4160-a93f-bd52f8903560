package bucket

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "bucket"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bos_bucket_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["bucket_list"] = append(resourceData["bucket_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)

		resourceData["cors_list"] = append(resourceData["cors_list"],
			utils.GenParamsFromStructSlice(resource.CORS)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, bucketSchema, userData["bucket_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, corsSchema, userData["cors_list"], map[string]any{"last_updated": "test"})
}

type Bucket struct {
	ACL        sonic.NoCopyRawMessage `json:"acl"`
	Bucket     BucketClass            `json:"bucket"`
	Cors       []Cor                  `json:"cors"`
	Encryption string                 `json:"encryption"`
	Logging    Logging                `json:"logging"`
}

type ACL struct {
	Grantee    []Grantee `json:"grantee"`
	Permission []string  `json:"permission"`
	Condition  Condition `json:"condition"`
	Resource   []string  `json:"resource"`
	Effect     string    `json:"effect"`
}

type Condition struct {
	IPAddress interface{} `json:"ipAddress"`
	Referer   Referer     `json:"referer"`
}

type Referer struct {
	StringLike   interface{} `json:"stringLike"`
	StringEquals interface{} `json:"stringEquals"`
}

type Grantee struct {
	ID           string `json:"id"`
	SAMLProvider string `json:"saml-provider"`
}

type BucketClass struct {
	Name         string `json:"name"`
	Location     string `json:"location"`
	CreationDate string `json:"creationDate"`
}

type Cor struct {
	AllowedOrigins       []string `json:"allowedOrigins"`
	AllowedMethods       []string `json:"allowedMethods"`
	AllowedHeaders       []string `json:"allowedHeaders"`
	AllowedExposeHeaders []string `json:"allowedExposeHeaders"`
	MaxAgeSeconds        int64    `json:"maxAgeSeconds"`
}

type Logging struct {
	Status       string `json:"status"`
	TargetBucket string `json:"targetBucket"`
	TargetPrefix string `json:"targetPrefix"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.BucketGraph, error) {
	original := &Bucket{}
	resource := &model.BucketGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Bucket.Name
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "bucket", original.Bucket.Name)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "bucket"
	resource.Name = original.Bucket.Name

	resource.PublicEndpoint = fmt.Sprintf("%s.%s.bcebos.com", original.Bucket.Name, original.Bucket.Location)
	resource.Policy = provider_utils.ParsePolicyDocument(original.ACL, resource.UID)

	acls := make([]ACL, 0)
	err := sonic.Unmarshal(original.ACL, &acls)
	if err != nil {
		logger.Errorf("parseBucketACL failed, assetMsg: %v, err: %v", assetMsg, err)
	}
	resource.ACL = parseBucketACL(acls)

	resource.CORS = lo.Map(original.Cors, func(rule Cor, _ int) *model.BucketCORSRuleGraph {
		content, _ := sonic.MarshalString(rule)
		return &model.BucketCORSRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, "bucket_cors", content),
				TargetUID: resource.UID,
			},
			AllowedOrigins: rule.AllowedOrigins,
			AllowedMethods: rule.AllowedMethods,
			AllowedHeaders: rule.AllowedHeaders,
			ExposeHeaders:  rule.AllowedExposeHeaders,
			MaxAgeSeconds:  int(rule.MaxAgeSeconds),
		}
	})
	resource.EncryptionEnabled = original.Encryption != "none"
	resource.EncryptionAlgorithm = original.Encryption
	resource.LoggingEnabled = original.Logging.Status == "enabled"

	return resource, nil
}

func parseBucketACL(acls []ACL) []string {
	acl := []string{}
	_, found := lo.Find(acls, func(item ACL) bool {
		return intersects([]string{"FULL_CONTROL", "WRITE", "WRITE_CAP"}, item.Permission) &&
			slices.Contains(lo.Map(item.Grantee, func(g Grantee, _ int) string { return g.ID }), "*") &&
			(strings.ToLower(item.Effect) == "allow" || item.Effect == "")
	})
	if found {
		acl = append(acl, "public-write")
	}
	_, found = lo.Find(acls, func(item ACL) bool {
		return intersects([]string{"FULL_CONTROL", "READ", "READ_CAP"}, item.Permission) &&
			slices.Contains(lo.Map(item.Grantee, func(g Grantee, _ int) string { return g.ID }), "*") &&
			(strings.ToLower(item.Effect) == "allow" || item.Effect == "")
	})
	if found {
		acl = append(acl, "public-read")
	}

	if len(acl) == 0 {
		acl = append(acl, "private")
	}
	return acl
}

func intersects(a, b []string) bool {
	for _, v := range a {
		if slices.Contains(b, v) {
			return true
		}
	}
	return false
}
