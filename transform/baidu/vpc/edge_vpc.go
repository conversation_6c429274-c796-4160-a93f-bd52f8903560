package vpc

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "vpc"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_vpc_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["vpc_list"] = append(resourceData["vpc_list"], utils.GenParamsFromStruct(resource))

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type EdgeVpc struct {
	Vpc EdgeVpcClass `json:"vpc"`
}

type EdgeVpcSubnet struct {
	SubnetID    string `json:"subnetId"`
	Name        string `json:"name"`
	CIDR        string `json:"cidr"`
	VpcID       string `json:"vpcId"`
	Description string `json:"description"`
	RegionID    string `json:"regionId"`
}

type EdgeVpcClass struct {
	VpcID           string          `json:"vpcId"`
	Name            string          `json:"name"`
	CIDR            string          `json:"cidr"`
	Description     string          `json:"description"`
	RegionID        string          `json:"regionId"`
	Region          string          `json:"region"`
	City            string          `json:"city"`
	ServiceProvider string          `json:"serviceProvider"`
	CreatedTime     string          `json:"createdTime"`
	Subnets         []EdgeVpcSubnet `json:"subnets"`
	IsDefault       bool            `json:"isDefault"`
	Tags            []Tag           `json:"tags"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.VPCGraph, error) {
	original := &EdgeVpc{}
	resource := &model.VPCGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Vpc.VpcID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "vpc", original.Vpc.VpcID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "vpc"
	resource.Name = original.Vpc.Name
	resource.Description = original.Vpc.Description
	resource.OriginalLabels = lo.Map(original.Vpc.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.CIDR = original.Vpc.CIDR
	resource.IsDefault = original.Vpc.IsDefault
	resource.SecondaryCIDRs = lo.Map(original.Vpc.Subnets, func(e EdgeVpcSubnet, _ int) string {
		return utils.GenerateUID(resource.Provider, "subnet", e.SubnetID)
	})
	resource.Subnet = lo.Map(original.Vpc.Subnets, func(e EdgeVpcSubnet, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", e.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
