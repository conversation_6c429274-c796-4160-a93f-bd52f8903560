package policy

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

func NewResourceService() *model.CommonResourceService{
	return &model.CommonResourceService{
		MessageType:     "iam_raw_policy",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   10 * time.Second,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resources := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		policy, err := parseOne(assetMsg)
		if err != nil {
			return nil, err
		} else {
			policy.TransformedObject, _ = sonic.MarshalString(policy)
		}

		resources["policy_list"] = append(resources["policy_list"], utils.GenParamsFromStruct(policy))
		for _, statement := range policy.PolicyStatement {
			resources["policy_statement_list"] = append(resources["policy_statement_list"],
				utils.GenParamsFromStruct(statement),
			)
		}
	}
	return resources, nil
}

func updateResources(resources map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, policySchema, resources["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policyStatementSchema, resources["policy_statement_list"], map[string]any{"last_updated": "test"})
}

type Policy struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	CreateTime  string                 `json:"createTime"`
	Description string                 `json:"description"`
	Document    sonic.NoCopyRawMessage `json:"document"`
}

var policyTypeMap = map[string]string{
	"System": "managed",
	"Custom": "custom",
}

type Grantee struct {
	ID           string `json:"id"`
	SAMLProvider string `json:"saml-provider"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.PolicyGraph, error) {
	original := &Policy{}
	resource := &model.PolicyGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.Region = assetMsg.Region
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.OriginalID = original.ID
	resource.UID = utils.GenerateUID(resource.Provider, "policy", original.Name)
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Description
	resource.Kind = "policy"
	resource.Name = original.Name

	v, ok := policyTypeMap[original.Type]
	resource.Class = lo.Ternary(ok, v, strings.ToLower(original.Type))
	resource.PolicyStatement = provider_utils.ParsePolicyDocument(original.Document, resource.UID)

	return resource, nil
}
