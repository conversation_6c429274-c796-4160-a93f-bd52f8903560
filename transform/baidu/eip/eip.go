package eip

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eip"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "eip_eip_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eip_list"] = append(resourceData["eip_list"], utils.GenParamsFromStruct(resource))
		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)
		resourceData["nat_list"] = append(resourceData["nat_list"],
			utils.GenParamsFromStructSlice(resource.NAT)...,
		)
		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}

	return resourceData, nil
}

func updateResources(eipData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eipSchema, eipData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniEipSchema, eipData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, natEipSchema, eipData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEipSchema, eipData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbEipSchema, eipData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEipSchema, eipData["label_list"], map[string]any{"last_updated": "test"})

}

type Eip struct {
	Eip EipClass `json:"eip"`
}

type EipClass struct {
	Name            string `json:"name"`
	Eip             string `json:"eip"`
	EipID           string `json:"eipId"`
	Status          string `json:"status"`
	EipInstanceType string `json:"eipInstanceType"`
	InstanceType    string `json:"instanceType"`
	InstanceID      string `json:"instanceId"`
	ShareGroupID    string `json:"shareGroupId"`
	ClusterID       string `json:"clusterId"`
	BandwidthInMbps int64  `json:"bandwidthInMbps"`
	PaymentTiming   string `json:"paymentTiming"`
	BillingMethod   string `json:"billingMethod"`
	CreateTime      string `json:"createTime"`
	ExpireTime      string `json:"expireTime"`
	Tags            []Tag  `json:"tags"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.EipGraph, error) {
	original := &Eip{}
	resource := &model.EipGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Eip.EipID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eip", original.Eip.Eip)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "eip"
	resource.Name = original.Eip.Name
	resource.OriginalLabels = lo.Map(original.Eip.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.IP = original.Eip.Eip
	resource.Status = lo.Ternary(strings.EqualFold(original.Eip.Status, "binded"), "binded", "available")
	resource.Bandwidth = int(original.Eip.BandwidthInMbps)
	switch original.Eip.InstanceType {
	case "ENI":
		resource.ENI = append(resource.ENI, &model.ENIGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eni", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "NAT":
		resource.NAT = append(resource.NAT, &model.NATGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "nat", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "BCC", "BBC", "DCC":
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "BLB":
		resource.LB = append(resource.LB, &model.LBGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "lb", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
