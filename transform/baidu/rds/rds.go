package rds

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "rds_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["rds_list"] = append(resourceData["rds_list"], utils.GenParamsFromStruct(resource))

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, rdsSchema, resourceData["rds_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, rdsAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type Rds struct {
	Instance       Instance     `json:"instance"`
	IPWhiteList    IPWhiteList  `json:"ipWhiteList"`
	Accounts       []RdsAccount `json:"accounts"`
	BackupList     []BackupList `json:"backupList"`
	PGLogEnabled   bool         `json:"pgLogEnabled"`
	SlowLogEnabled bool         `json:"slowLogEnabled"`
	ErrLogEnabled  bool         `json:"errLogEnabled"`
}

type RdsAccount struct {
	AccountName        string        `json:"accountName"`
	Status             string        `json:"status"`
	Type               string        `json:"type"`
	AccountType        string        `json:"accountType"`
	DatabasePrivileges []interface{} `json:"databasePrivileges"`
	Desc               string        `json:"desc"`
}

type IPWhiteList struct {
	Etag        string   `json:"etag"`
	SecurityIPS []string `json:"securityIps"`
}

type BackupList struct {
	BackupSize      int64  `json:"backupSize"`
	BackupStatus    string `json:"backupStatus"`
	BackupID        string `json:"backupId"`
	BackupEndTime   string `json:"backupEndTime"`
	DownloadURL     string `json:"downloadUrl"`
	BackupType      string `json:"backupType"`
	BackupStartTime string `json:"backupStartTime"`
	DownloadExpires string `json:"downloadExpires"`
}

type Instance struct {
	InstanceID           string       `json:"instanceId"`
	InstanceName         string       `json:"instanceName"`
	Engine               string       `json:"engine"`
	EngineVersion        string       `json:"engineVersion"`
	RDSMinorVersion      string       `json:"rdsMinorVersion"`
	CharacterSetName     string       `json:"characterSetName"`
	InstanceClass        string       `json:"instanceClass"`
	AllocatedMemoryInMB  int64        `json:"allocatedMemoryInMB"`
	AllocatedMemoryInGB  int64        `json:"allocatedMemoryInGB"`
	AllocatedStorageInGB int64        `json:"allocatedStorageInGB"`
	Category             string       `json:"category"`
	InstanceStatus       string       `json:"instanceStatus"`
	CPUCount             int64        `json:"cpuCount"`
	MemoryCapacity       int64        `json:"memoryCapacity"`
	VolumeCapacity       int64        `json:"volumeCapacity"`
	TotalStorageInGB     int64        `json:"totalStorageInGB"`
	NodeAmount           int64        `json:"nodeAmount"`
	UsedStorage          float64      `json:"usedStorage"`
	PublicAccessStatus   string       `json:"publicAccessStatus"`
	InstanceCreateTime   string       `json:"instanceCreateTime"`
	InstanceExpireTime   string       `json:"instanceExpireTime"`
	Endpoint             Endpoint     `json:"endpoint"`
	SyncMode             string       `json:"syncMode"`
	BackupPolicy         BackupPolicy `json:"backupPolicy"`
	Region               string       `json:"region"`
	InstanceType         string       `json:"instanceType"`
	SourceInstanceID     string       `json:"sourceInstanceId"`
	SourceRegion         string       `json:"sourceRegion"`
	ZoneNames            []string     `json:"zoneNames"`
	VpcID                string       `json:"vpcId"`
	Subnets              []Subnet     `json:"subnets"`
	Topology             Topology     `json:"topology"`
	Task                 string       `json:"task"`
	PaymentTiming        string       `json:"paymentTiming"`
	BgwGroupID           string       `json:"bgwGroupId"`
	ReadReplicaNum       int64        `json:"readReplicaNum"`
	ReadReplica          interface{}  `json:"readReplica"`
	LockMode             string       `json:"lockMode"`
	EipStatus            string       `json:"eipStatus"`
	SuperUserFlag        string       `json:"superUserFlag"`
	ReplicationType      string       `json:"replicationType"`
	Azone                string       `json:"azone"`
	ApplicationType      string       `json:"applicationType"`
	OnlineStatus         int64        `json:"onlineStatus"`
	IsSingle             bool         `json:"isSingle"`
	NodeType             string       `json:"nodeType"`
	DiskIoType           string       `json:"diskIoType"`
	GroupID              string       `json:"groupId"`
	GroupName            string       `json:"groupName"`
	DiskType             string       `json:"diskType"`
	CdsType              string       `json:"cdsType"`
	MaintainStartTime    string       `json:"maintainStartTime"`
	MaintainDuration     int64        `json:"maintainDuration"`
	HaStrategy           int64        `json:"haStrategy"`
	VpcName              string       `json:"vpcName"`
	Tags                 []Tag        `json:"tags"`
	ResourceGroupID      string       `json:"resourceGroupId"`
	ResourceGroupName    string       `json:"resourceGroupName"`
	RoGroupList          interface{}  `json:"roGroupList"`
	RoGroupAbnormal      bool         `json:"roGroupAbnormal"`
}

type BackupPolicy struct {
	BackupDays    string `json:"backupDays"`
	BackupTime    string `json:"backupTime"`
	Persistent    bool   `json:"persistent"`
	ExpireInDays  int64  `json:"expireInDays"`
	FreeSpaceInGB int64  `json:"freeSpaceInGb"`
}

type Endpoint struct {
	Address string `json:"address"`
	Port    int64  `json:"port"`
	VnetIP  string `json:"vnetIp"`
	InetIP  string `json:"inetIp"`
}

type Topology struct {
	Rdsproxy    []interface{} `json:"rdsproxy"`
	Master      []interface{} `json:"master"`
	ReadReplica []interface{} `json:"readReplica"`
}

type Subnet struct {
	SubnetID string `json:"subnetId"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.RDSGraph, error) {
	original := &Rds{}
	resource := &model.RDSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "rds", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "rds"
	resource.Name = original.Instance.InstanceName
	resource.OriginalLabels = lo.Map(original.Instance.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.ConnectionAddress = original.Instance.Endpoint.Address
	resource.Engine = original.Instance.Engine
	resource.EngineVersion = original.Instance.EngineVersion
	resource.PublicAllowed = original.Instance.Endpoint.InetIP != ""
	resource.IpWhiteList = lo.Map(original.IPWhiteList.SecurityIPS, func(ip string, _ int) string {
		return provider_utils.FormatCIDR(ip)
	})
	resource.TDEEnabled = nil
	resource.BackupAvailable = len(original.BackupList) > 0
	if strings.EqualFold(original.Instance.InstanceType, "readreplica") {
		resource.BackupMethod = "na"
	} else {
		_, foundAuto := lo.Find(original.BackupList, func(backup BackupList) bool {
			return strings.EqualFold(backup.BackupType, "automated")
		})
		resource.BackupMethod = lo.Ternary(foundAuto, "auto", "manual")
	}

	backuptimeStr := lo.MaxBy(original.BackupList, func(a BackupList, b BackupList) bool {
		return a.BackupEndTime > b.BackupEndTime
	})
	backuptime, err := time.Parse(time.RFC3339, backuptimeStr.BackupEndTime)
	if err != nil {
		resource.LastBackupTime = 0
	} else {
		resource.LastBackupTime = backuptime.UnixMilli()
	}

	resource.LogFileExists = original.PGLogEnabled || original.SlowLogEnabled || original.ErrLogEnabled

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet, lo.Map(original.Instance.Subnets, func(subnet Subnet, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", subnet.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})...)

	resource.Accounts = lo.Map(original.Accounts, func(account RdsAccount, _ int) *model.RDSAccountGraph {
		return &model.RDSAccountGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "rds-account", account.AccountName),
				TargetUID: resource.UID,
			},
			Name:        account.AccountName,
			Enabled:     strings.EqualFold(account.Status, "available"),
			Class:       lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
			Description: account.Desc,
		}
	})

	return resource, nil
}
