package edge_ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var snapshotLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "snapshot"})

func NewSnapshotService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bec_snapshot_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSnapshot,
		UpdateResources: updateSnapshots,
	}
}

func transformSnapshot(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSnapshot(assetMsg)
		if err != nil {
			snapshotLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["snapshot_list"] = append(resourceData["snapshot_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
		resourceData["volume_list"] = append(resourceData["volume_list"], utils.GenParamsFromStructSlice(resource.Volume)...)
	}
	return resourceData, nil
}

func updateSnapshots(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, snapshotSchema, resourceData["snapshot_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, snapShotVolumeSchema, resourceData["ebs_list"], map[string]any{"last_updated": "test"})
}

type EdgeSnapshot struct {
	Snapshot EdgeSnapshotClass `json:"snapshot"`
}

type EdgeSnapshotClass struct {
	SnapshotID    string `json:"snapshotId"`
	Name          string `json:"name"`
	CdsID         string `json:"cdsId"`
	CdsName       string `json:"cdsName"`
	CdsStatus     string `json:"cdsStatus"`
	SizeInGB      int64  `json:"sizeInGB"`
	Status        string `json:"status"`
	CreateMethod  string `json:"createMethod"`
	Type          string `json:"type"`
	CustomImageID string `json:"customImageId"`
	CreateTime    string `json:"createTime"`
	RegionID      string `json:"regionId"`
	InstanceID    string `json:"instanceId"`
	Tags          []Tag  `json:"tags"`
}

func parseSnapshot(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &EdgeSnapshot{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Snapshot.SnapshotID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.SnapshotID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Snapshot.Name
	resource.OriginalLabels = lo.Map(original.Snapshot.Tags, func(e Tag, _ int) *model.KVGraph {
		return utils.NewLabel(e.TagKey, e.TagValue, resource.UID)
	})

	resource.Class = "edge-snapshot"
	resource.Status = lo.Ternary(strings.EqualFold(original.Snapshot.Status, "created"), "available", "unavailable")
	resource.Encrypted = false
	resource.Volume = append(resource.Volume, &model.EBSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.CdsID),
			TargetUID: resource.UID,
		},
	})

	return resource, nil
}
