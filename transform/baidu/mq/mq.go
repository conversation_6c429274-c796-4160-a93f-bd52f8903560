package mq

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "mq_kafka_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["mq_list"] = append(resourceData["mq_list"], utils.GenParamsFromStruct(resource))

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.Eip)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, mqSchema, resourceData["mq_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
}

type Kafka struct {
	ClusterID   string      `json:"clusterId"`
	ClusterSid  string      `json:"clusterSid"`
	Name        string      `json:"name"`
	Region      string      `json:"region"`
	Type        string      `json:"type"`
	Mode        string      `json:"mode"`
	State       string      `json:"state"`
	Provisioned Provisioned `json:"provisioned"`
	Tags        []Tag       `json:"tags"`
	CreateTime  string      `json:"createTime"`
}

type Provisioned struct {
	KafkaVersion               string               `json:"kafkaVersion"`
	Billing                    KafkaBilling         `json:"billing"`
	Vpc                        KafkaVpc             `json:"vpc"`
	Subnets                    []KafkaSubnet        `json:"subnets"`
	LogicalZones               []string             `json:"logicalZones"`
	SecurityGroup              []KafkaSecurityGroup `json:"securityGroup"`
	SecurityGroups             []KafkaSecurityGroup `json:"securityGroups"`
	VpcID                      interface{}          `json:"vpcId"`
	SubnetIDS                  interface{}          `json:"subnetIds"`
	SecurityGroupIDS           interface{}          `json:"securityGroupIds"`
	PublicIPEnabled            bool                 `json:"publicIpEnabled"`
	PublicIPBandwidth          int64                `json:"publicIpBandwidth"`
	PublicIPMode               interface{}          `json:"publicIpMode"`
	Eips                       interface{}          `json:"eips"`
	IntranetIPEnabled          bool                 `json:"intranetIpEnabled"`
	Authentications            []Authentication     `json:"authentications"`
	ACLEnabled                 bool                 `json:"aclEnabled"`
	NumberOfBrokerNodes        int64                `json:"numberOfBrokerNodes"`
	NumberOfBrokerNodesPerZone int64                `json:"numberOfBrokerNodesPerZone"`
	NodeType                   string               `json:"nodeType"`
	StorageMeta                StorageMeta          `json:"storageMeta"`
	StoragePolicyEnabled       bool                 `json:"storagePolicyEnabled"`
	StoragePolicy              StoragePolicy        `json:"storagePolicy"`
	RemoteStorageEnabled       bool                 `json:"remoteStorageEnabled"`
	ConfigMeta                 ConfigMeta           `json:"configMeta"`
	DeploySetEnabled           bool                 `json:"deploySetEnabled"`
}

type Authentication struct {
	Mode    string      `json:"mode"`
	Context interface{} `json:"context"`
}

type KafkaBilling struct {
	Payment             string      `json:"payment"`
	TimeLength          int64       `json:"timeLength"`
	TimeUnit            string      `json:"timeUnit"`
	ExpireTime          string      `json:"expireTime"`
	AutoRenewEnabled    bool        `json:"autoRenewEnabled"`
	AutoRenewTimeLength int64       `json:"autoRenewTimeLength"`
	AutoRenewTimeUnit   string      `json:"autoRenewTimeUnit"`
	CouponIDS           interface{} `json:"couponIds"`
	IsAutoPay           interface{} `json:"isAutoPay"`
}

type ConfigMeta struct {
	ConfigID   string  `json:"configId"`
	RevisionID int64   `json:"revisionId"`
	Context    Context `json:"context"`
}

type Context struct {
	NumNetworkThreads  string `json:"num.network.threads"`
	JVMHeapSize        string `json:"jvm.heap.size"`
	NumIoThreads       string `json:"num.io.threads"`
	NumReplicaFetchers string `json:"num.replica.fetchers"`
}

type KafkaSecurityGroup struct {
	SecurityGroupID   string `json:"securityGroupId"`
	SecurityGroupUUID string `json:"securityGroupUuid"`
	Name              string `json:"name"`
	VpcID             string `json:"vpcId"`
}

type StorageMeta struct {
	StorageType  string `json:"storageType"`
	StorageSize  int64  `json:"storageSize"`
	NumberOfDisk int64  `json:"numberOfDisk"`
}

type StoragePolicy struct {
	Type             string      `json:"type"`
	AutoDelete       interface{} `json:"autoDelete"`
	AutoExpand       AutoExpand  `json:"autoExpand"`
	DynamicRetention interface{} `json:"dynamicRetention"`
}

type AutoExpand struct {
	DiskUsedThresholdPercent int64 `json:"diskUsedThresholdPercent"`
	StepForwardPercent       int64 `json:"stepForwardPercent"`
	StepForwardSize          int64 `json:"stepForwardSize"`
	MaxStorageSize           int64 `json:"maxStorageSize"`
}

type KafkaSubnet struct {
	SubnetID   string `json:"subnetId"`
	Name       string `json:"name"`
	SubnetType string `json:"subnetType"`
	Zone       string `json:"zone"`
	VpcID      string `json:"vpcId"`
	CIDR       string `json:"cidr"`
}

type KafkaVpc struct {
	VpcID   string `json:"vpcId"`
	VpcUUID string `json:"vpcUuid"`
	Name    string `json:"name"`
	CIDR    string `json:"cidr"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.KafkaGraph, error) {
	original := &Kafka{}
	resource := &model.KafkaGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.ClusterID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "mq", original.ClusterID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "mq"
	resource.Name = original.Name
	resource.OriginalLabels = lo.Map(original.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.Version = original.Provisioned.KafkaVersion
	resource.Status = lo.Ternary(strings.EqualFold(original.State, "active"), "running", "stopped")
	resource.NodeNum = int(original.Provisioned.NumberOfBrokerNodes)
	resource.NodeSpec = original.Provisioned.NodeType
	resource.PublicIPEnabled = original.Provisioned.PublicIPEnabled
	resource.Authentications = lo.Map(original.Provisioned.Authentications, func(auth Authentication, _ int) string {
		return strings.ToLower(auth.Mode)
	})

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Provisioned.Vpc.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet, lo.Map(original.Provisioned.Subnets, func(subnet KafkaSubnet, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", subnet.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})...)

	var sgIds []string
	sgIds = append(sgIds, lo.Map(original.Provisioned.SecurityGroup, func(sg KafkaSecurityGroup, _ int) string {
		return sg.SecurityGroupID
	})...)
	sgIds = append(sgIds, lo.Map(original.Provisioned.SecurityGroups, func(sg KafkaSecurityGroup, _ int) string {
		return sg.SecurityGroupID
	})...)

	resource.SG = append(resource.SG, lo.Map(lo.Uniq(sgIds), func(sgId string, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "sg", sgId),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
