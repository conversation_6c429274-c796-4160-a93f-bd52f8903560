package ecs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_bccInstance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type ECS struct {
	Instance EcsInstance `json:"instance"`
}

type EcsInstance struct {
	ID                     string         `json:"id"`
	SerialNumber           string         `json:"serialNumber"`
	Name                   string         `json:"name"`
	Hostname               string         `json:"hostname"`
	InstanceType           string         `json:"instanceType"`
	Spec                   string         `json:"spec"`
	Desc                   string         `json:"desc"`
	Status                 string         `json:"status"`
	PaymentTiming          string         `json:"paymentTiming"`
	CreateTime             string         `json:"createTime"`
	ExpireTime             string         `json:"expireTime"`
	ReleaseTime            string         `json:"releaseTime"`
	PublicIP               string         `json:"publicIp"`
	InternalIP             string         `json:"internalIp"`
	CPUCount               int64          `json:"cpuCount"`
	IsomerismCard          string         `json:"isomerismCard"`
	NPUVideoMemory         string         `json:"npuVideoMemory"`
	GPUCard                string         `json:"gpuCard"`
	FPGACard               string         `json:"fpgaCard"`
	CardCount              string         `json:"cardCount"`
	MemoryCapacityInGB     int64          `json:"memoryCapacityInGB"`
	LocalDiskSizeInGB      int64          `json:"localDiskSizeInGB"`
	ImageID                string         `json:"imageId"`
	NetworkCapacityInMbps  int64          `json:"networkCapacityInMbps"`
	PlacementPolicy        string         `json:"placementPolicy"`
	ZoneName               string         `json:"zoneName"`
	FlavorSubType          string         `json:"flavorSubType"`
	SubnetID               string         `json:"subnetId"`
	VpcID                  string         `json:"vpcId"`
	AutoRenew              bool           `json:"autoRenew"`
	KeypairID              string         `json:"keypairId"`
	KeypairName            string         `json:"keypairName"`
	DedicatedHostID        string         `json:"dedicatedHostId"`
	Tags                   []Tag          `json:"tags"`
	Ipv6                   string         `json:"ipv6"`
	Ipv6Addresses          interface{}    `json:"Ipv6Addresses"`
	EniQuota               int64          `json:"eniQuota"`
	EriQuota               int64          `json:"eriQuota"`
	RdmaType               string         `json:"rdmaType"`
	RdmaTypeAPI            string         `json:"rdmaTypeApi"`
	SwitchID               string         `json:"switchId"`
	HostID                 string         `json:"hostId"`
	DeploysetID            string         `json:"deploysetId"`
	RackID                 string         `json:"rackId"`
	NicInfo                NicInfo        `json:"nicInfo"`
	EniNum                 string         `json:"eniNum"`
	DeploysetList          []interface{}  `json:"deploysetList"`
	DeletionProtection     int64          `json:"deletionProtection"`
	NetEthQueueCount       string         `json:"netEthQueueCount"`
	Volumes                interface{}    `json:"volumes"`
	EnableJumboFrame       bool           `json:"enableJumboFrame"`
	IsEipAutoRelatedDelete bool           `json:"isEipAutoRelatedDelete"`
	ResGroupInfos          []ResGroupInfo `json:"resGroupInfos"`
	EhcClusterID           string         `json:"ehcClusterId"`
	RoleName               string         `json:"roleName"`
	CreatedFrom            string         `json:"createdFrom"`
	HosteyeType            string         `json:"hosteyeType"`
	RepairStatus           string         `json:"repairStatus"`
	OSVersion              string         `json:"osVersion"`
	OSArch                 string         `json:"osArch"`
	OSName                 string         `json:"osName"`
	ImageName              string         `json:"imageName"`
	ImageType              string         `json:"imageType"`
}

type NicInfo struct {
	Status                   string        `json:"status"`
	MACAddress               string        `json:"macAddress"`
	DeviceID                 string        `json:"deviceId"`
	VpcID                    string        `json:"vpcId"`
	EniID                    string        `json:"eniId"`
	Name                     string        `json:"name"`
	Type                     string        `json:"type"`
	CreatedTime              string        `json:"createdTime"`
	SubnetType               string        `json:"subnetType"`
	SubnetID                 string        `json:"subnetId"`
	EniNum                   int64         `json:"eniNum"`
	Az                       string        `json:"az"`
	EniUUID                  string        `json:"eniUuid"`
	Description              string        `json:"description"`
	IPS                      []IP          `json:"ips"`
	SecurityGroups           []string      `json:"securityGroups"`
	EnterpriseSecurityGroups []string      `json:"enterpriseSecurityGroups"`
	EriNum                   int64         `json:"eriNum"`
	EriInfos                 []interface{} `json:"eriInfos"`
	Ipv6S                    []interface{} `json:"ipv6s"`
}

type IP struct {
	Eip             string `json:"eip"`
	EipStatus       string `json:"eipStatus"`
	EipSize         string `json:"eipSize"`
	EipID           string `json:"eipId"`
	Primary         string `json:"primary"`
	PrivateIP       string `json:"privateIp"`
	EipAllocationID string `json:"eipAllocationId"`
	EipType         string `json:"eipType"`
	EipGroupID      string `json:"eipGroupId"`
}

type ResGroupInfo struct {
	GroupID   string `json:"groupId"`
	GroupName string `json:"groupName"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &ECS{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.Instance.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ecs"
	resource.Name = original.Instance.Name
	resource.Description = original.Instance.Desc
	resource.OriginalLabels = lo.Map(original.Instance.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Hostname = original.Instance.Hostname
	resource.Class = "cvm"
	resource.OSName = original.Instance.OSName
	osType := strings.ToLower(original.Instance.OSName)
	resource.OSType = lo.Ternary(strings.Contains(osType, "windows"), "windows", "linux")
	resource.Spec = original.Instance.Spec
	resource.Status = lo.Ternary(strings.EqualFold(original.Instance.Status, "running"), "running", "stopped")
	resource.DeleteProtection = lo.ToPtr(original.Instance.DeletionProtection == 1)

	resource.PrimaryPrivateIP = original.Instance.InternalIP
	resource.PrimaryPublicIP = original.Instance.PublicIP

	resource.PrivateIPList = lo.Uniq(lo.Map(original.Instance.NicInfo.IPS, func(e IP, _ int) string { return e.PrivateIP }))
	resource.PublicIPList = lo.Uniq(lo.FilterMap(original.Instance.NicInfo.IPS, func(e IP, _ int) (string, bool) { return e.Eip, e.Eip != "null" }))

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.NicInfo.SubnetID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG,
		lo.Map(original.Instance.NicInfo.SecurityGroups, func(e string, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.SG = append(resource.SG,
		lo.Map(original.Instance.NicInfo.EnterpriseSecurityGroups, func(e string, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.ENI = append(resource.ENI,
		&model.ENIGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eni", original.Instance.NicInfo.EniID),
				TargetUID: resource.UID,
			},
		},
	)

	return resource, nil
}
