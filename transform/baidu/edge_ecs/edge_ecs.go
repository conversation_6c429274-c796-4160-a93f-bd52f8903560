package edge_ecs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bec_bccInstance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type Bec struct {
	Bec BecClass `json:"bec"`
}

type BecClass struct {
	PublicIP         string             `json:"publicIp"`
	Ipv6PublicIP     string             `json:"ipv6PublicIp"`
	InternalIP       string             `json:"internalIp"`
	MultiplePublicIP []MultiplePublicIP `json:"multiplePublicIp"`
	ServiceProvider  string             `json:"serviceProvider"`
	VMID             string             `json:"vmId"`
	UUID             string             `json:"uuid"`
	VMName           string             `json:"vmName"`
	Status           string             `json:"status"`
	Spec             string             `json:"spec"`
	CPU              int64              `json:"cpu"`
	Mem              int64              `json:"mem"`
	GPU              int64              `json:"gpu"`
	Region           string             `json:"region"`
	City             string             `json:"city"`
	RegionID         string             `json:"regionId"`
	NeedPublicIP     bool               `json:"needPublicIp"`
	NeedIpv6PublicIP bool               `json:"needIpv6PublicIp"`
	Bandwidth        string             `json:"bandwidth"`
	OSImage          OSImage            `json:"osImage"`
	ServiceID        string             `json:"serviceId"`
	CreateTime       string             `json:"createTime"`
	SecurityGroups   []SecurityGroup    `json:"securityGroups"`
	Vpc              BecVpc             `json:"vpc"`
	Hostname         string             `json:"hostname"`
	DNS              string             `json:"dns"`
	RootDiskSize     int64              `json:"rootDiskSize"`
	DataStorage      int64              `json:"dataStorage"`
	DataVolumeList   []SystemVolume     `json:"dataVolumeList"`
	SystemVolume     SystemVolume       `json:"systemVolume"`
	BccKeyPairList   interface{}        `json:"bccKeyPairList"`
	SwitchID         string             `json:"switchId"`
	PrivateIPS       []string           `json:"privateIps"`
}

type MultiplePublicIP struct {
	ServiceProvider string `json:"serviceProvider"`
	IP              string `json:"ip"`
	Ipv6            string `json:"ipv6"`
}

type SystemVolume struct {
	Name       string `json:"name"`
	VolumeType string `json:"volumeType"`
	SizeInGB   int64  `json:"sizeInGB"`
	PvcName    string `json:"pvcName"`
}

type OSImage struct {
	ID                  string `json:"id"`
	ImageID             string `json:"imageId"`
	Name                string `json:"name"`
	NameFri             string `json:"nameFri"`
	ImageType           string `json:"imageType"`
	SnapshotID          string `json:"snapshotId"`
	CPU                 int64  `json:"cpu"`
	Memory              int64  `json:"memory"`
	OSType              string `json:"osType"`
	OSVersion           string `json:"osVersion"`
	OSName              string `json:"osName"`
	OSBuild             string `json:"osBuild"`
	OSLang              string `json:"osLang"`
	DiskSize            int64  `json:"diskSize"`
	CreateTime          string `json:"createTime"`
	Status              string `json:"status"`
	MinMem              int64  `json:"minMem"`
	MinCPU              int64  `json:"minCpu"`
	MinDiskGB           int64  `json:"minDiskGb"`
	Desc                string `json:"desc"`
	OSArch              string `json:"osArch"`
	EphemeralSize       int64  `json:"ephemeralSize"`
	ImageDescription    string `json:"imageDescription"`
	ShareToUserNumLimit int64  `json:"shareToUserNumLimit"`
	SharedToUserNum     int64  `json:"sharedToUserNum"`
	FPGAType            string `json:"fpgaType"`
}

type SecurityGroup struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Desc string `json:"desc"`
}

type BecVpc struct {
	VpcID       string `json:"vpcId"`
	Name        string `json:"name"`
	CIDR        string `json:"cidr"`
	Description string `json:"description"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &Bec{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Bec.VMID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.Bec.VMID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ecs"
	resource.Name = original.Bec.VMName

	resource.Class = "edge"
	resource.Hostname = original.Bec.Hostname
	resource.OSName = original.Bec.OSImage.OSName
	osType := strings.ToLower(original.Bec.OSImage.OSName)
	resource.OSType = lo.Ternary(strings.Contains(osType, "windows"), "windows", "linux")
	resource.Spec = original.Bec.Spec
	resource.Status = lo.Ternary(strings.EqualFold(original.Bec.Status, "running"), "running", "stopped")
	resource.DeleteProtection = lo.ToPtr(false)

	resource.PrimaryPrivateIP = original.Bec.InternalIP
	resource.PrimaryPublicIP = original.Bec.PublicIP

	resource.PrivateIPList = lo.Filter(
		lo.Uniq(append(original.Bec.PrivateIPS, original.Bec.InternalIP)),
		func(e string, _ int) bool { return e != "" },
	)
	resource.PublicIPList = lo.Filter(
		lo.Uniq(
			append(lo.Map(original.Bec.MultiplePublicIP, func(e MultiplePublicIP, _ int) string { return e.IP }),
				original.Bec.PublicIP),
		),
		func(e string, _ int) bool { return e != "" },
	)

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Bec.Vpc.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG,
		lo.Map(original.Bec.SecurityGroups, func(e SecurityGroup, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e.ID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	return resource, nil
}
