package elasticsearch

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "elasticsearch"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "es_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["es_list"] = append(resourceData["es_list"], utils.GenParamsFromStruct(resource))

		resourceData["es_node_list"] = append(resourceData["es_node_list"],
			utils.GenParamsFromStructSlice(resource.Node)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, esSchema, resourceData["es_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, esNodeSchema, resourceData["es_node_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
}

type Es struct {
	Instance EsInstance      `json:"instance"`
	Sg       SGSecurityGroup `json:"sg"`
	Vpc      VpcClass        `json:"vpc"`
}
type SGSecurityGroup struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	VpcID       string `json:"vpcId"`
	Rules       []Rule `json:"rules"`
	Tags        []Tag  `json:"tags"`
	CreatedTime string `json:"createdTime"`
}
type Rule struct {
	Remark              string `json:"remark"`
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	SourceGroupID       string `json:"sourceGroupId"`
	SourceIP            string `json:"sourceIp"`
	DestGroupID         string `json:"destGroupId"`
	DestIP              string `json:"destIp"`
	SecurityGroupID     string `json:"securityGroupId"`
	SecurityGroupRuleID string `json:"securityGroupRuleId"`
}
type VpcClass struct {
	VpcID         string   `json:"vpcId"`
	Name          string   `json:"name"`
	CIDR          string   `json:"cidr"`
	CreatedTime   string   `json:"createdTime"`
	Description   string   `json:"description"`
	IsDefault     bool     `json:"isDefault"`
	SecondaryCIDR []string `json:"secondaryCidr"`
	Tags          []Tag    `json:"tags"`
}

type EsInstance struct {
	ActualStatus    string            `json:"actualStatus"`
	AdminUsername   string            `json:"adminUsername"`
	AvailableZone   string            `json:"availableZone"`
	Billing         Billing           `json:"billing"`
	ClusterHealth   ClusterHealth     `json:"clusterHealth"`
	ClusterID       string            `json:"clusterId"`
	ClusterName     string            `json:"clusterName"`
	DesireStatus    string            `json:"desireStatus"`
	EnableEsSSL     bool              `json:"enableEsSSL"`
	EnableKibanaSSL bool              `json:"enableKibanaSSL"`
	EsEip           string            `json:"esEip"`
	EsURL           string            `json:"esUrl"`
	ExpireTime      string            `json:"expireTime"`
	ExporterURL     string            `json:"exporterUrl"`
	GrafanaURL      interface{}       `json:"grafanaUrl"`
	Instances       []InstanceElement `json:"instances"`
	KibanaEip       string            `json:"kibanaEip"`
	KibanaURL       string            `json:"kibanaUrl"`
	Log             Log               `json:"log"`
	Modules         []Module          `json:"modules"`
	Network         []Network         `json:"network"`
	Region          string            `json:"region"`
	ResGroupList    []ResGroupList    `json:"resGroupList"`
	SecurityGroup   string            `json:"securityGroup"`
	Subnet          string            `json:"subnet"`
	Tags            []Tag             `json:"tags"`
	Vpc             string            `json:"vpc"`
	VpcID           string            `json:"vpcId"`
}

type Billing struct {
	PaymentType string `json:"paymentType"`
}

type ClusterHealth struct {
	IndexDiskSize       string              `json:"indexDiskSize"`
	NodeDiskMaxSizeInGB NodeDiskMaxSizeInGB `json:"nodeDiskMaxSizeInGB"`
	Status              string              `json:"status"`
	TotalDiskSize       string              `json:"totalDiskSize"`
	UsedDiskSize        string              `json:"usedDiskSize"`
}

type NodeDiskMaxSizeInGB struct {
	EsColdTierNode int64   `json:"es_cold_tier_node"`
	EsNode         float64 `json:"es_node"`
}

type InstanceElement struct {
	AvailableZone string `json:"availableZone"`
	HostIP        string `json:"hostIp"`
	InstanceID    string `json:"instanceId"`
	ModuleType    string `json:"moduleType"`
	ModuleVersion string `json:"moduleVersion"`
	Status        string `json:"status"`
}

type Log struct {
	AuditLog      bool `json:"auditLog"`
	GcLog         bool `json:"gcLog"`
	IndexSlowLog  bool `json:"indexSlowLog"`
	MainLog       bool `json:"mainLog"`
	SearchSlowLog bool `json:"searchSlowLog"`
}

type Module struct {
	ActualInstanceNum int64  `json:"actualInstanceNum"`
	SlotDescription   string `json:"slotDescription"`
	SlotType          string `json:"slotType"`
	Type              string `json:"type"`
	Version           string `json:"version"`
}

type Network struct {
	AvailableZone string `json:"availableZone"`
	Subnet        string `json:"subnet"`
	SubnetID      string `json:"subnetId"`
}

type ResGroupList struct {
	GroupID   string `json:"groupId"`
	GroupName string `json:"groupName"`
}
type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ESGraph, error) {
	original := &Es{}
	resource := &model.ESGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.ClusterID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "es", original.Instance.ClusterID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "es"
	resource.Name = original.Instance.ClusterName
	resource.OriginalLabels = lo.Map(original.Instance.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	if len(original.Instance.Modules) > 0 {
		resource.EngineVersion = original.Instance.Modules[0].Version
	}
	resource.Status = lo.Ternary(strings.EqualFold(original.Instance.ActualStatus, "Running"), "running", "stopped")
	resource.PublicEndpoint = original.Instance.EsEip
	resource.PrivateEndpoint = original.Instance.EsURL
	resource.PublicAllowed = original.Instance.EsEip != ""

	resource.KibanaPublicEndpoint = original.Instance.KibanaEip
	resource.KibanaPrivateEndpoint = original.Instance.KibanaURL
	resource.KibanaPublicAllowed = original.Instance.KibanaEip != ""
	resource.KibanaProtocol = lo.Ternary(original.Instance.EnableKibanaSSL, "https", "http")

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.Subnet),
			TargetUID: resource.UID,
		},
	})
	resource.Node = lo.Map(original.Instance.Instances, func(instance InstanceElement, _ int) *model.ESNodeGraph {
		return &model.ESNodeGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "es-node", instance.InstanceID),
				TargetUID: resource.UID,
			},
			IP:    instance.HostIP,
			Class: lo.Ternary(instance.ModuleType == "kibana", "kibana", "es"),
		}
	})
	if original.Sg.ID != "" {
		resource.SG = append(resource.SG, &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", original.Sg.ID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
