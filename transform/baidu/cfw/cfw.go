package cfw

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cfw"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cfw_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cfw_list"] = append(resourceData["cfw_list"], utils.GenParamsFromStruct(resource))

		for _, rule := range resource.Rules {
			resourceData["cfw_rule_list"] = append(resourceData["cfw_rule_list"], utils.GenParamsFromStruct(rule))
			resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"], utils.GenParamsFromStructSlice(rule.SrcIPRange)...)
			resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"], utils.GenParamsFromStructSlice(rule.DstIPRange)...)
			resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"], utils.GenParamsFromStructSlice(rule.SrcPortRange)...)
			resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"], utils.GenParamsFromStructSlice(rule.DstPortRange)...)
		}

		resourceData["eip_list"] = append(resourceData["eip_list"], utils.GenParamsFromStructSlice(resource.EIP)...)
		resourceData["nat_list"] = append(resourceData["nat_list"], utils.GenParamsFromStructSlice(resource.NAT)...)
		resourceData["vpc_list"] = append(resourceData["vpc_list"], utils.GenParamsFromStructSlice(resource.VPC)...)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cfwSchema, resourceData["cfw_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, cfwRuleSchema, resourceData["cfw_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIPRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIPRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, natSchema, resourceData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
}

type CFW struct {
	Cfw       CfwDetail         `json:"cfw"`
	Instances []RelatedInstance `json:"instances"`
}

type CfwDetail struct {
	CfwID           string    `json:"cfwId"`
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	CreatedTime     string    `json:"createdTime"`
	BindInstanceNum int64     `json:"bindInstanceNum"`
	CfwRules        []CfwRule `json:"cfwRules"`
}

type CfwRule struct {
	IPVersion     int64  `json:"ipVersion"`
	Priority      int    `json:"priority"`
	Protocol      string `json:"protocol"`
	Direction     string `json:"direction"`
	SourceAddress string `json:"sourceAddress"`
	DestAddress   string `json:"destAddress"`
	SourcePort    string `json:"sourcePort"`
	DestPort      string `json:"destPort"`
	Action        string `json:"action"`
	Description   string `json:"description"`
	CfwID         string `json:"cfwId"`
	CfwRuleID     string `json:"cfwRuleId"`
}

type RelatedInstance struct {
	InstanceID      string `json:"instanceId"`
	InstanceName    string `json:"instanceName"`
	Status          string `json:"status"`
	Region          string `json:"region"`
	CfwID           string `json:"cfwId"`
	CfwName         string `json:"cfwName"`
	VpcID           string `json:"vpcId"`
	VpcName         string `json:"vpcName"`
	PublicIP        string `json:"publicIp"`
	Role            string `json:"role"`
	LocalIfID       string `json:"localIfId"`
	LocalIfName     string `json:"localIfName"`
	PeerRegion      string `json:"peerRegion"`
	PeerVpcID       string `json:"peerVpcId"`
	PeerVpcName     string `json:"peerVpcName"`
	MemberID        string `json:"memberId"`
	MemberName      string `json:"memberName"`
	MemberAccountID string `json:"memberAccountId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CFWGraph, error) {
	original := &CFW{}
	resource := &model.CFWGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cfw.CfwID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "cloud-firewall", original.Cfw.CfwID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cloud-firewall"
	resource.Name = original.Cfw.Name
	resource.Description = original.Cfw.Description

	resource.Status = "on"

	for _, cfwRule := range original.Cfw.CfwRules {
		if rule, err := parseCFWRule(cfwRule, resource.UID); err != nil {
			return nil, err
		} else {
			resource.Rules = append(resource.Rules, rule)
		}
	}

	for _, instance := range original.Instances {
		switch {
		case strings.HasPrefix(instance.InstanceID, "ip"):
			resource.EIP = append(resource.EIP, &model.EipGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(provider_utils.ProviderID, "eip", instance.PublicIP),
					TargetUID: resource.UID,
				},
			})
		case strings.HasPrefix(instance.InstanceID, "nat"):
			resource.NAT = append(resource.NAT, &model.NATGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(provider_utils.ProviderID, "nat", instance.InstanceID),
					TargetUID: resource.UID,
				},
			})
		case instance.VpcID != "":
			resource.VPC = append(resource.VPC, &model.VPCGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(provider_utils.ProviderID, "vpc", instance.VpcID),
					TargetUID: resource.UID,
				},
			})
		case instance.MemberID != "":
			resource.VPC = append(resource.VPC, &model.VPCGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(provider_utils.ProviderID, "vpc", instance.MemberID),
					TargetUID: resource.UID,
				},
			})
		default:
			continue
		}
	}

	return resource, nil
}

func parseCFWRule(perm CfwRule, targetUID string) (*model.CFWRuleGraph, error) {
	rule := model.CFWRuleGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(provider_utils.ProviderID, "cfw_rule", perm.CfwRuleID),
			TargetUID: targetUID,
		},
		RuleID:      perm.CfwRuleID,
		Description: perm.Description,
		Protocol:    []string{strings.ToLower(perm.Protocol)},
		Direction:   lo.Ternary(perm.Direction == "in", "ingress", "egress"),
		Policy:      lo.Ternary(perm.Action == "allow", "accept", "drop"),
		IPVersion:   []string{lo.Ternary(perm.IPVersion == 4, "ipv4", "ipv6")},
		Priority:    perm.Priority,
	}
	if ipRange, err := provider_utils.ParseIPRange(perm.SourceAddress, perm.IPVersion == 6, targetUID); err != nil {
		return nil, err
	} else {
		rule.SrcIPRange = []*model.IPRangeGraph{ipRange}
	}
	if ipRange, err := provider_utils.ParseIPRange(perm.DestAddress, perm.IPVersion == 6, targetUID); err != nil {
		return nil, err
	} else {
		rule.DstIPRange = []*model.IPRangeGraph{ipRange}
	}
	if portRange, err := provider_utils.ParsePortRange(perm.SourcePort, targetUID); err != nil {
		return nil, err
	} else {
		rule.SrcPortRange = []*model.PortRangeGraph{portRange}
	}
	if portRange, err := provider_utils.ParsePortRange(perm.DestPort, targetUID); err != nil {
		return nil, err
	} else {
		rule.DstPortRange = []*model.PortRangeGraph{portRange}
	}
	return &rule, nil
}
