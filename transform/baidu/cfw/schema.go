package cfw

import "AssetStandardizer/graph"

var cfwSchema = graph.NodeSchema{
	Label: "CloudFirewall",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"status":             {Name: "status"},
	},
}

var cfwRuleSchema = graph.NodeSchema{
	Label: "CloudFirewallRule",
	Properties: map[string]graph.PropertyRef{
		"uid":         {Name: "uid"},
		"rule_id":     {Name: "rule_id"},
		"description": {Name: "description"},
		"protocol":    {Name: "protocol"},
		"direction":   {Name: "direction"},
		"policy":      {Name: "policy"},
		"ip_version":  {Name: "ip_version"},
		"priority":    {Name: "priority"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewall",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_CFW_RULE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var srcIPRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewallRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_SRC_IP_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dstIPRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewallRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_DST_IP_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var srcPortRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewallRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_SRC_PORT_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dstPortRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewallRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_DST_PORT_RANGE",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var eipSchema = graph.NodeSchema{
	Label: "EIP",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewall",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "PROTECTED_BY",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var natSchema = graph.NodeSchema{
	Label: "NAT",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewall",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "PROTECTED_BY",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "CloudFirewall",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "PROTECTED_BY",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

