package bbc

import "AssetStandardizer/graph"

var ecsSchema = graph.NodeSchema{
	Label: "ECS",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"class":              {Name: "class"},
		"hostname":           {Name: "hostname"},
		"status":             {Name: "status"},
		"primary_private_ip": {Name: "primary_private_ip"},
		"primary_public_ip":  {Name: "primary_public_ip"},
		"private_ip_list":    {Name: "private_ip_list"},
		"public_ip_list":     {Name: "public_ip_list"},
		"spec":               {Name: "spec"},
		"os_name":            {Name: "os_name"},
		"os_type":            {Name: "os_type"},
		"delete_protection":  {Name: "delete_protection"},
	},
}

var labelSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"key":          {Name: "key"},
		"value":        {Name: "value"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ECS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_LABEL",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ECS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "MEMBER_OF",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var subnetSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ECS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "MEMBER_OF",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var sgSchema = graph.NodeSchema{
	Label: "SecurityGroup",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ECS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var eniSchema = graph.NodeSchema{
	Label: "ENI",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ECS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
