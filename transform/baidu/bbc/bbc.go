package bbc

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/baidu/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "bbc_bbcInstance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type Bbc struct {
	Image    BbcImage     `json:"image"`
	Instance BbcInstance  `json:"instance"`
	Networks []BbcNetwork `json:"networks"`
}

type BbcImage struct {
	OSVersion      string `json:"osVersion"`
	OSArch         string `json:"osArch"`
	Status         string `json:"status"`
	Desc           string `json:"desc"`
	ID             string `json:"id"`
	Name           string `json:"name"`
	OSName         string `json:"osName"`
	OSBuild        string `json:"osBuild"`
	CreateTime     string `json:"createTime"`
	Type           string `json:"type"`
	OSType         string `json:"osType"`
	SpecialVersion string `json:"specialVersion"`
}

type BbcInstance struct {
	ID                    string `json:"id"`
	Name                  string `json:"name"`
	Hostname              string `json:"hostname"`
	UUID                  string `json:"uuid"`
	Desc                  string `json:"desc"`
	Status                string `json:"status"`
	PaymentTiming         string `json:"paymentTiming"`
	CreateTime            string `json:"createTime"`
	ExpireTime            string `json:"expireTime"`
	PublicIP              string `json:"publicIp"`
	InternalIP            string `json:"internalIp"`
	RdmaIP                string `json:"rdmaIp"`
	ImageID               string `json:"imageId"`
	FlavorID              string `json:"flavorId"`
	Zone                  string `json:"zone"`
	Region                string `json:"region"`
	HasAlive              int64  `json:"hasAlive"`
	AutoRenew             bool   `json:"autoRenew"`
	Tags                  []Tag  `json:"tags"`
	SwitchID              string `json:"switchId"`
	HostID                string `json:"hostId"`
	DeploysetID           string `json:"deploysetId"`
	NetworkCapacityInMbps int64  `json:"networkCapacityInMbps"`
	RackID                string `json:"rackId"`
}

type BbcNetwork struct {
	BbcID  string    `json:"bbcId"`
	Vpc    BbcVpc    `json:"vpc"`
	Subnet BbcSubnet `json:"subnet"`
}

type BbcSubnet struct {
	VpcID      string `json:"vpcId"`
	Name       string `json:"name"`
	SubnetType string `json:"subnetType"`
	SubnetID   string `json:"subnetId"`
	CIDR       string `json:"cidr"`
	ZoneName   string `json:"zoneName"`
}

type BbcVpc struct {
	VpcID       string `json:"vpcId"`
	CIDR        string `json:"cidr"`
	Name        string `json:"name"`
	IsDefault   bool   `json:"isDefault"`
	Description string `json:"description"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &Bbc{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.Instance.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ecs"
	resource.Name = original.Instance.Name
	resource.OriginalLabels = lo.Map(original.Instance.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Hostname = original.Instance.Hostname
	resource.Class = "cvm"
	resource.OSName = original.Image.OSName
	osType := strings.ToLower(original.Image.OSType)
	resource.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")
	resource.Spec = original.Instance.FlavorID
	resource.Status = lo.Ternary(strings.EqualFold(original.Instance.Status, "running"), "running", "stopped")
	resource.DeleteProtection = lo.ToPtr(false)

	resource.PrimaryPrivateIP = original.Instance.InternalIP
	resource.PrivateIPList = lo.Ternary(original.Instance.InternalIP != "", []string{original.Instance.InternalIP}, []string{})

	resource.PrimaryPublicIP = original.Instance.PublicIP
	resource.PublicIPList = lo.Ternary(original.Instance.PublicIP != "", []string{original.Instance.PublicIP}, []string{})

	resource.VPC = append(resource.VPC, lo.Map(original.Networks, func(e BbcNetwork, _ int) *model.VPCGraph {
		return &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", e.Vpc.VpcID),
				TargetUID: resource.UID,
			},
		}
	})...)
	resource.Subnet = append(resource.Subnet, lo.Map(original.Networks, func(e BbcNetwork, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", e.Subnet.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})...)

	return resource, nil
}
