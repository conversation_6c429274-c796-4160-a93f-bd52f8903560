package lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var lbLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "lb"})

func NewLBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "network_lb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformLB,
		UpdateResources: updateLBResources,
	}
}

func transformLB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseLB(assetMsg)
		if err != nil {
			lbLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateLBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type LB struct {
	IPConfigurations []IPConfiguration `json:"ipConfigurations"`
	LB               LBClass           `json:"lb"`
}

type IPConfiguration struct {
	Etag       string                    `json:"etag"`
	ID         string                    `json:"id"`
	Location   string                    `json:"location"`
	Name       string                    `json:"name"`
	Properties IPConfigurationProperties `json:"properties"`
	Tags       IPConfigurationTags       `json:"tags"`
	Type       string                    `json:"type"`
}

type IPConfigurationProperties struct {
	IdleTimeoutInMinutes     int64                  `json:"idleTimeoutInMinutes"`
	IPAddress                string                 `json:"ipAddress"`
	IPConfiguration          PublicIPAddressElement `json:"ipConfiguration"`
	IPTags                   []interface{}          `json:"ipTags"`
	ProvisioningState        string                 `json:"provisioningState"`
	PublicIPAddressVersion   string                 `json:"publicIPAddressVersion"`
	PublicIPAllocationMethod string                 `json:"publicIPAllocationMethod"`
	ResourceGUID             string                 `json:"resourceGuid"`
	DdosSettings             *DdosSettings          `json:"ddosSettings,omitempty"`
}

type DdosSettings struct {
	ProtectionMode string `json:"protectionMode"`
}

type PublicIPAddressElement struct {
	ID string `json:"id"`
}

type IPConfigurationTags struct {
	AksManagedClusterName string `json:"aks-managed-cluster-name,omitempty"`
	AksManagedClusterRg   string `json:"aks-managed-cluster-rg,omitempty"`
	AksManagedType        string `json:"aks-managed-type,omitempty"`
	K8SAzureClusterName   string `json:"k8s-azure-cluster-name,omitempty"`
	K8SAzureService       string `json:"k8s-azure-service,omitempty"`
}

type LBClass struct {
	Etag       string       `json:"etag"`
	ID         string       `json:"id"`
	Location   string       `json:"location"`
	Name       string       `json:"name"`
	Properties LBProperties `json:"properties"`
	Tags       LBTags       `json:"tags"`
	Type       string       `json:"type"`
}

type LBProperties struct {
	BackendAddressPools      []BackendAddressPool      `json:"backendAddressPools"`
	FrontendIPConfigurations []FrontendIPConfiguration `json:"frontendIPConfigurations"`
	InboundNatPools          []interface{}             `json:"inboundNatPools"`
	InboundNatRules          []interface{}             `json:"inboundNatRules"`
	LoadBalancingRules       []LoadBalancingRule       `json:"loadBalancingRules"`
	OutboundRules            []OutboundRule            `json:"outboundRules"`
	Probes                   []Probe                   `json:"probes"`
	ProvisioningState        string                    `json:"provisioningState"`
	ResourceGUID             string                    `json:"resourceGuid"`
}

type BackendAddressPool struct {
	Etag       string                       `json:"etag"`
	ID         string                       `json:"id"`
	Name       string                       `json:"name"`
	Properties BackendAddressPoolProperties `json:"properties"`
	Type       string                       `json:"type"`
}

type BackendAddressPoolProperties struct {
	BackendIPConfigurations      []PublicIPAddressElement     `json:"backendIPConfigurations"`
	LoadBalancerBackendAddresses []LoadBalancerBackendAddress `json:"loadBalancerBackendAddresses"`
	OutboundRules                []PublicIPAddressElement     `json:"outboundRules,omitempty"`
	ProvisioningState            string                       `json:"provisioningState"`
	LoadBalancingRules           []PublicIPAddressElement     `json:"loadBalancingRules,omitempty"`
}

type LoadBalancerBackendAddress struct {
	Name       string                               `json:"name"`
	Properties LoadBalancerBackendAddressProperties `json:"properties"`
}

type LoadBalancerBackendAddressProperties struct {
	NetworkInterfaceIPConfiguration PublicIPAddressElement `json:"networkInterfaceIPConfiguration"`
}

type FrontendIPConfiguration struct {
	Etag       string                            `json:"etag"`
	ID         string                            `json:"id"`
	Name       string                            `json:"name"`
	Properties FrontendIPConfigurationProperties `json:"properties"`
	Type       string                            `json:"type"`
}

type FrontendIPConfigurationProperties struct {
	OutboundRules             []PublicIPAddressElement `json:"outboundRules,omitempty"`
	PrivateIPAllocationMethod string                   `json:"privateIPAllocationMethod"`
	ProvisioningState         string                   `json:"provisioningState"`
	PublicIPAddress           PublicIPAddressElement   `json:"publicIPAddress"`
	LoadBalancingRules        []PublicIPAddressElement `json:"loadBalancingRules,omitempty"`
}

type LoadBalancingRule struct {
	Etag       string                      `json:"etag"`
	ID         string                      `json:"id"`
	Name       string                      `json:"name"`
	Properties LoadBalancingRuleProperties `json:"properties"`
	Type       string                      `json:"type"`
}

type LoadBalancingRuleProperties struct {
	BackendAddressPool      PublicIPAddressElement   `json:"backendAddressPool"`
	BackendAddressPools     []PublicIPAddressElement `json:"backendAddressPools"`
	BackendPort             int64                    `json:"backendPort"`
	DisableOutboundSnat     bool                     `json:"disableOutboundSnat"`
	EnableFloatingIP        bool                     `json:"enableFloatingIP"`
	EnableTCPReset          bool                     `json:"enableTcpReset"`
	FrontendIPConfiguration PublicIPAddressElement   `json:"frontendIPConfiguration"`
	FrontendPort            int64                    `json:"frontendPort"`
	IdleTimeoutInMinutes    int64                    `json:"idleTimeoutInMinutes"`
	LoadDistribution        string                   `json:"loadDistribution"`
	Probe                   PublicIPAddressElement   `json:"probe"`
	Protocol                string                   `json:"protocol"`
	ProvisioningState       string                   `json:"provisioningState"`
}

type OutboundRule struct {
	Etag       string                 `json:"etag"`
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Properties OutboundRuleProperties `json:"properties"`
	Type       string                 `json:"type"`
}

type OutboundRuleProperties struct {
	AllocatedOutboundPorts   int64                    `json:"allocatedOutboundPorts"`
	BackendAddressPool       PublicIPAddressElement   `json:"backendAddressPool"`
	EnableTCPReset           bool                     `json:"enableTcpReset"`
	FrontendIPConfigurations []PublicIPAddressElement `json:"frontendIPConfigurations"`
	IdleTimeoutInMinutes     int64                    `json:"idleTimeoutInMinutes"`
	Protocol                 string                   `json:"protocol"`
	ProvisioningState        string                   `json:"provisioningState"`
}

type Probe struct {
	Etag       string          `json:"etag"`
	ID         string          `json:"id"`
	Name       string          `json:"name"`
	Properties ProbeProperties `json:"properties"`
	Type       string          `json:"type"`
}

type ProbeProperties struct {
	IntervalInSeconds  int64                    `json:"intervalInSeconds"`
	LoadBalancingRules []PublicIPAddressElement `json:"loadBalancingRules"`
	NumberOfProbes     int64                    `json:"numberOfProbes"`
	Port               int64                    `json:"port"`
	ProbeThreshold     int64                    `json:"probeThreshold"`
	Protocol           string                   `json:"protocol"`
	ProvisioningState  string                   `json:"provisioningState"`
}

type LBTags struct {
	AksManagedClusterName string `json:"aks-managed-cluster-name"`
	AksManagedClusterRg   string `json:"aks-managed-cluster-rg"`
}

func parseLB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &LB{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.LB.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.LB.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.LB.Name

	resource.Class = "network"
	resource.Status = "active"
	resource.PublicIPList = lo.FilterMap(original.IPConfigurations, func(ip IPConfiguration, _ int) (string, bool) {
		return ip.Properties.IPAddress, ip.Properties.IPAddress != ""
	})

	return resource, nil
}
