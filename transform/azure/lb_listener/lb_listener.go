package lb_listener

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var lbListenerLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "lb-listener"})

func NewLBListenerService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "network_lbListener_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformLBListener,
		UpdateResources: updateLBListeners,
	}
}

func transformLBListener(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseLBListener(assetMsg)
		if err != nil {
			lbListenerLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_listener_list"] = append(resourceData["lb_listener_list"], utils.GenParamsFromStruct(resource))
		resourceData["port_range_list"] = append(resourceData["port_range_list"],
			utils.GenParamsFromStructSlice(resource.PortRange)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateLBListeners(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbListenerSchema, resourceData["lb_listener_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, portRangeSchema, resourceData["port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})

}

type LbListener struct {
	LBID       string     `json:"lb_id"`
	LBListener LBListener `json:"lb-listener"`
}

type LBListener struct {
	Etag       string     `json:"etag"`
	ID         string     `json:"id"`
	Name       string     `json:"name"`
	Properties Properties `json:"properties"`
	Type       string     `json:"type"`
}

type Properties struct {
	BackendPort          int                `json:"backendPort"`
	DisableOutboundSnat  bool               `json:"disableOutboundSnat"`
	EnableFloatingIP     bool               `json:"enableFloatingIP"`
	EnableTCPReset       bool               `json:"enableTcpReset"`
	FrontendPort         int                `json:"frontendPort"`
	IdleTimeoutInMinutes int                `json:"idleTimeoutInMinutes"`
	LoadDistribution     string             `json:"loadDistribution"`
	Probe                BackendAddressPool `json:"probe"`
	Protocol             string             `json:"protocol"`
	ProvisioningState    string             `json:"provisioningState"`
}

type BackendAddressPool struct {
	Etag string `json:"etag"`
	ID   string `json:"id"`
}

func parseLBListener(assetMsg *model.AssetMessage) (*model.LbListenerGraph, error) {
	original := &LbListener{}
	resource := &model.LbListenerGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.LBListener.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb-listener", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb-listener"
	resource.Name = original.LBListener.Name

	resource.AclStatus = "off"
	resource.Protocol = strings.ToLower(original.LBListener.Properties.Protocol)
	resource.Status = "active"
	resource.HealthCheckEnabled = lo.ToPtr(original.LBListener.Properties.Probe.ID != "")

	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.LBID),
			TargetUID: resource.UID,
		},
	})
	resource.PortRange = append(resource.PortRange, utils.NewPortRange(original.LBListener.Properties.FrontendPort, original.LBListener.Properties.FrontendPort, resource.UID))

	return resource, nil
}
