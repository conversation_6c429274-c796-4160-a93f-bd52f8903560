package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var snapshotLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "snapshot"})

func NewSnapshotService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "compute_snapshot_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSnapshot,
		UpdateResources: updateSnapshots,
	}
}

func transformSnapshot(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSnapshot(assetMsg)
		if err != nil {
			snapshotLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["snapshot_list"] = append(resourceData["snapshot_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateSnapshots(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, snapshotSchema, resourceData["snapshot_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type EbsSnapshot struct {
	Snapshot SnapshotClass `json:"snapshot"`
}

type SnapshotClass struct {
	Properties SnapshotProperties `json:"properties"`
	Type       string             `json:"type"`
	Location   string             `json:"location"`
	Tags       Tags               `json:"tags"`
	ID         string             `json:"id"`
	Name       string             `json:"name"`
}

type Tags map[string]string

type SnapshotProperties struct {
	OSType                       string                       `json:"osType"`
	CreationData                 CreationData                 `json:"creationData"`
	DiskSizeGB                   int64                        `json:"diskSizeGB"`
	EncryptionSettingsCollection EncryptionSettingsCollection `json:"encryptionSettingsCollection"`
	Encryption                   Encryption                   `json:"encryption"`
	TimeCreated                  string                       `json:"timeCreated"`
	ProvisioningState            string                       `json:"provisioningState"`
}

type CreationData struct {
	CreateOption     string         `json:"createOption"`
	SourceResourceID string         `json:"sourceResourceId"`
	ImageReference   ImageReference `json:"imageReference"`
}

type ImageReference struct {
	Publisher               string `json:"publisher"`
	Offer                   string `json:"offer"`
	Sku                     string `json:"sku"`
	Version                 string `json:"version"`
	ExactVersion            string `json:"exactVersion"`
	SharedGalleryImageID    string `json:"sharedGalleryImageId"`
	CommunityGalleryImageID string `json:"communityGalleryImageId"`
	ID                      string `json:"id"`
}

type Encryption struct {
	Type string `json:"type"`
}

type SnapshotEncryption struct {
	Type string `json:"type"`
}

type EncryptionSettingsCollection struct {
	Enabled bool `json:"enabled"`
}

type SourceVault struct {
	ID string `json:"id"`
}

func parseSnapshot(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &EbsSnapshot{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Snapshot.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Snapshot.Name
	for k, v := range original.Snapshot.Tags {
		resource.OriginalLabels = append(resource.OriginalLabels, utils.NewLabel(k, v, resource.UID))
	}

	resource.Class = "snapshot"
	resource.Status = lo.Ternary(strings.EqualFold(original.Snapshot.Properties.ProvisioningState, "Succeeded"), "available", "unavailable")
	resource.Encrypted = original.Snapshot.Properties.EncryptionSettingsCollection.Enabled
	resource.Volume = append(resource.Volume, &model.EBSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ebs", original.Snapshot.Properties.CreationData.ImageReference.ID),
			TargetUID: resource.UID,
		},
	})

	return resource, nil
}
