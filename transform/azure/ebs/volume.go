package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"slices"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var volumeLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "volume"})

func NewVolumeService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "compute_disk_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformVolume,
		UpdateResources: updateVolumes,
	}
}

func transformVolume(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseVolume(assetMsg)
		if err != nil {
			volumeLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["volume_list"] = append(resourceData["volume_list"], utils.GenParamsFromStruct(resource))

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["image_list"] = append(resourceData["image_list"],
			utils.GenParamsFromStructSlice(resource.Image)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateVolumes(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, volumeSchema, resourceData["volume_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsVolumeSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
}

type EbsDisk struct {
	Disk DiskClass `json:"disk"`
}

type DiskClass struct {
	ID         string         `json:"id"`
	Location   string         `json:"location"`
	ManagedBy  string         `json:"managedBy"`
	Name       string         `json:"name"`
	Properties DiskProperties `json:"properties"`
	Type       string         `json:"type"`
}

type DiskProperties struct {
	LastOwnershipUpdateTime string                `json:"LastOwnershipUpdateTime"`
	CreationData            CreationData          `json:"creationData"`
	DiskIOPSReadWrite       int64                 `json:"diskIOPSReadWrite"`
	DiskMBpsReadWrite       int64                 `json:"diskMBpsReadWrite"`
	DiskSizeBytes           int64                 `json:"diskSizeBytes"`
	DiskSizeGB              int64                 `json:"diskSizeGB"`
	DiskState               string                `json:"diskState"`
	Encryption              Encryption            `json:"encryption"`
	HyperVGeneration        string                `json:"hyperVGeneration"`
	NetworkAccessPolicy     string                `json:"networkAccessPolicy"`
	OSType                  string                `json:"osType"`
	ProvisioningState       string                `json:"provisioningState"`
	PublicNetworkAccess     string                `json:"publicNetworkAccess"`
	SupportedCapabilities   SupportedCapabilities `json:"supportedCapabilities"`
	SupportsHibernation     bool                  `json:"supportsHibernation"`
	TimeCreated             string                `json:"timeCreated"`
	UniqueID                string                `json:"uniqueId"`
}

type SupportedCapabilities struct {
	AcceleratedNetwork  bool   `json:"acceleratedNetwork"`
	Architecture        string `json:"architecture"`
	DiskControllerTypes string `json:"diskControllerTypes"`
}

func parseVolume(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &EbsDisk{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Disk.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Disk.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Disk.Name

	resource.Class = "volume"
	resource.Status = lo.Ternary(slices.Contains([]string{"ActiveSAS", "FronzenSAS", "Attached", "Frozen"}, original.Disk.Properties.DiskState), "inuse", "available")
	resource.Encrypted = original.Disk.Properties.Encryption.Type != ""
	resource.ECS = append(resource.ECS, &model.ECSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ecs", original.Disk.ManagedBy),
			TargetUID: resource.UID,
		},
	})
	resource.Image = append(resource.Image, &model.EBSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ebs", original.Disk.Properties.CreationData.ImageReference.ID),
			TargetUID: resource.UID,
		},
	})

	return resource, nil
}
