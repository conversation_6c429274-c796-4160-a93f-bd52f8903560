package k8s

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "k8s"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "k8s_cluster_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["k8s_list"] = append(resourceData["k8s_list"], utils.GenParamsFromStruct(resource))

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, k8sSchema, resourceData["k8s_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
}

type K8S struct {
	Cluster Cluster `json:"cluster"`
}

type Cluster struct {
	ID string `json:"id"`
	// Identity   Identity      `json:"identity"`
	Location   string        `json:"location"`
	Name       string        `json:"name"`
	Properties K8SProperties `json:"properties"`
	// Sku        Sku           `json:"sku"`
	Type string `json:"type"`
}

type K8SProperties struct {
	AddonProfiles             AddonProfiles           `json:"addonProfiles"`
	AgentPoolProfiles         []AgentPoolProfile      `json:"agentPoolProfiles"`
	AutoUpgradeProfile        AutoUpgradeProfile      `json:"autoUpgradeProfile"`
	AzurePortalFQDN           string                  `json:"azurePortalFQDN"`
	CurrentKubernetesVersion  string                  `json:"currentKubernetesVersion"`
	DisableLocalAccounts      bool                    `json:"disableLocalAccounts"`
	DNSPrefix                 string                  `json:"dnsPrefix"`
	EnableRBAC                bool                    `json:"enableRBAC"`
	FQDN                      string                  `json:"fqdn"`
	PrivateFQDN               string                  `json:"privateFQDN"`
	IdentityProfile           IdentityProfile         `json:"identityProfile"`
	KubernetesVersion         string                  `json:"kubernetesVersion"`
	LinuxProfile              LinuxProfile            `json:"linuxProfile"`
	MaxAgentPools             int64                   `json:"maxAgentPools"`
	MetricsProfile            MetricsProfile          `json:"metricsProfile"`
	NetworkProfile            K8SNetworkProfile       `json:"networkProfile"`
	NodeResourceGroup         string                  `json:"nodeResourceGroup"`
	OidcIssuerProfile         OidcIssuerProfile       `json:"oidcIssuerProfile"`
	PowerState                PowerState              `json:"powerState"`
	ProvisioningState         string                  `json:"provisioningState"`
	ResourceUID               string                  `json:"resourceUID"`
	SecurityProfile           SecurityProfile         `json:"securityProfile"`
	ServicePrincipalProfile   ServicePrincipalProfile `json:"servicePrincipalProfile"`
	StorageProfile            K8SStorageProfile       `json:"storageProfile"`
	SupportPlan               string                  `json:"supportPlan"`
	WorkloadAutoScalerProfile SecurityProfile         `json:"workloadAutoScalerProfile"`
}

type AddonProfiles struct {
	Omsagent Omsagent `json:"omsagent"`
}

type Omsagent struct {
	Config  Config `json:"config"`
	Enabled bool   `json:"enabled"`
}

type Config struct {
	LogAnalyticsWorkspaceResourceID string `json:"logAnalyticsWorkspaceResourceID"`
	UseAADAuth                      string `json:"useAADAuth"`
}

type AgentPoolProfile struct {
	Count                      int64           `json:"count"`
	CurrentOrchestratorVersion string          `json:"currentOrchestratorVersion"`
	EnableAutoScaling          bool            `json:"enableAutoScaling"`
	EnableEncryptionAtHost     bool            `json:"enableEncryptionAtHost"`
	EnableFIPS                 bool            `json:"enableFIPS"`
	EnableNodePublicIP         bool            `json:"enableNodePublicIP"`
	EnableUltraSSD             bool            `json:"enableUltraSSD"`
	KubeletDiskType            string          `json:"kubeletDiskType"`
	MaxPods                    int64           `json:"maxPods"`
	Mode                       string          `json:"mode"`
	Name                       string          `json:"name"`
	NodeImageVersion           string          `json:"nodeImageVersion"`
	OrchestratorVersion        string          `json:"orchestratorVersion"`
	OSDiskSizeGB               int64           `json:"osDiskSizeGB"`
	OSDiskType                 string          `json:"osDiskType"`
	OSSKU                      string          `json:"osSKU"`
	OSType                     string          `json:"osType"`
	PowerState                 PowerState      `json:"powerState"`
	ProvisioningState          string          `json:"provisioningState"`
	Type                       string          `json:"type"`
	UpgradeSettings            SecurityProfile `json:"upgradeSettings"`
	VMSize                     string          `json:"vmSize"`
}

type PowerState struct {
	Code string `json:"code"`
}

type SecurityProfile struct {
}

type AutoUpgradeProfile struct {
	NodeOSUpgradeChannel string `json:"nodeOSUpgradeChannel"`
}

type IdentityProfile struct {
	Kubeletidentity Kubeletidentity `json:"kubeletidentity"`
}

type Kubeletidentity struct {
	ClientID   string `json:"clientId"`
	ObjectID   string `json:"objectId"`
	ResourceID string `json:"resourceId"`
}

type LinuxProfile struct {
	AdminUsername string `json:"adminUsername"`
	// SSH           SSH    `json:"ssh"`
}

type MetricsProfile struct {
	CostAnalysis OidcIssuerProfile `json:"costAnalysis"`
}

type OidcIssuerProfile struct {
	Enabled bool `json:"enabled"`
}

type K8SNetworkProfile struct {
	DNSServiceIP        string              `json:"dnsServiceIP"`
	IPFamilies          []string            `json:"ipFamilies"`
	LoadBalancerProfile LoadBalancerProfile `json:"loadBalancerProfile"`
	LoadBalancerSku     string              `json:"loadBalancerSku"`
	NetworkPlugin       string              `json:"networkPlugin"`
	NetworkPolicy       string              `json:"networkPolicy"`
	OutboundType        string              `json:"outboundType"`
	PodCIDR             string              `json:"podCidr"`
	PodCidrs            []string            `json:"podCidrs"`
	ServiceCIDR         string              `json:"serviceCidr"`
	ServiceCidrs        []string            `json:"serviceCidrs"`
}

type LoadBalancerProfile struct {
	BackendPoolType      string                `json:"backendPoolType"`
	EffectiveOutboundIPS []EffectiveOutboundIP `json:"effectiveOutboundIPs"`
	ManagedOutboundIPS   ManagedOutboundIPS    `json:"managedOutboundIPs"`
}

type EffectiveOutboundIP struct {
	ID string `json:"id"`
}

type ManagedOutboundIPS struct {
	Count int64 `json:"count"`
}

type ServicePrincipalProfile struct {
	ClientID string `json:"clientId"`
}

type K8SStorageProfile struct {
	DiskCSIDriver      OidcIssuerProfile `json:"diskCSIDriver"`
	FileCSIDriver      OidcIssuerProfile `json:"fileCSIDriver"`
	SnapshotController OidcIssuerProfile `json:"snapshotController"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.K8SGraph, error) {
	original := &K8S{}
	resource := &model.K8SGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cluster.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "kubernetes", original.Cluster.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "kubernetes"
	resource.Name = original.Cluster.Name

	resource.Status = "running"
	resource.EngineVersion = original.Cluster.Properties.KubernetesVersion
	if len(original.Cluster.Properties.FQDN) > 0 {
		resource.PublicEndpoint = original.Cluster.Properties.FQDN
	}
	if len(original.Cluster.Properties.PrivateFQDN) > 0 {
		resource.PrivateEndpoint = original.Cluster.Properties.PrivateFQDN
	}

	resource.PodCIDRs = append(resource.PodCIDRs, original.Cluster.Properties.NetworkProfile.PodCIDR)
	resource.PodCIDRs = append(resource.PodCIDRs, original.Cluster.Properties.NetworkProfile.PodCidrs...)
	resource.ServiceCIDRs = append(resource.ServiceCIDRs, original.Cluster.Properties.NetworkProfile.ServiceCIDR)
	resource.ServiceCIDRs = append(resource.ServiceCIDRs, original.Cluster.Properties.NetworkProfile.ServiceCidrs...)

	return resource, nil
}
