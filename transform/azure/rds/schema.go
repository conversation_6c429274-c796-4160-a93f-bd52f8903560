package rds

import "AssetStandardizer/graph"

var rdsSchema = graph.NodeSchema{
	Label: "RDS",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"engine":             {Name: "engine"},
		"engine_version":     {Name: "engine_version"},
		"connection_address": {Name: "connection_address"},
		"public_allowed":     {Name: "public_allowed"},
		"ip_white_list":      {Name: "ip_white_list"},
		"delete_protection":  {Name: "delete_protection"},
		"tde_enabled":        {Name: "tde_enabled"},
		"backup_available":   {Name: "backup_available"},
		"backup_method":      {Name: "backup_method"},
		"last_backup_time":   {Name: "last_backup_time"},
		"log_file_exists":    {Name: "log_file_exists"},
	},
}

var rdsAccountSchema = graph.NodeSchema{
	Label: "RDSAccount",
	Properties: map[string]graph.PropertyRef{
		"uid":         {Name: "uid"},
		"name":        {Name: "name"},
		"enabled":     {Name: "enabled"},
		"class":       {Name: "class"},
		"host":        {Name: "host"},
		"description": {Name: "description"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "RDS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_ACCOUNT",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var sgSchema = graph.NodeSchema{
	Label: "SecurityGroup",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "RDS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ATTACHED_TO",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "RDS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var subnetSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "RDS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
