package rds

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"net"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "rds_sql_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["rds_list"] = append(resourceData["rds_list"], utils.GenParamsFromStruct(resource))

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, rdsSchema, resourceData["rds_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, rdsAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type RDS struct {
	EncryptionProtector []EncryptionProtector `json:"encryptionProtector"`
	Ipv4FirewallRule    []Ipv4FirewallRule    `json:"ipv4FirewallRule"`
	Ipv6FirewallRule    []Ipv4FirewallRule    `json:"ipv6FirewallRule"`
	PrivateEndpoints    []PrivateEndpoint     `json:"privateEndpoints"`
	Server              Server                `json:"server"`
}

type EncryptionProtector struct {
	ID         string                        `json:"id"`
	Kind       string                        `json:"kind"`
	Name       string                        `json:"name"`
	Properties EncryptionProtectorProperties `json:"properties"`
	Type       string                        `json:"type"`
}

type EncryptionProtectorProperties struct {
	AutoRotationEnabled bool   `json:"autoRotationEnabled"`
	ServerKeyName       string `json:"serverKeyName"`
	ServerKeyType       string `json:"serverKeyType"`
}

type Ipv4FirewallRule struct {
	ID         string                     `json:"id"`
	Name       string                     `json:"name"`
	Properties Ipv4FirewallRuleProperties `json:"properties"`
	Type       string                     `json:"type"`
}

type Ipv4FirewallRuleProperties struct {
	EndIPAddress   string `json:"endIpAddress"`
	StartIPAddress string `json:"startIpAddress"`
}

type PrivateEndpoint struct {
	Etag       string                    `json:"etag"`
	ID         string                    `json:"id"`
	Location   string                    `json:"location"`
	Name       string                    `json:"name"`
	Properties PrivateEndpointProperties `json:"properties"`
	Tags       Tags                      `json:"tags"`
	Type       string                    `json:"type"`
}

type PrivateEndpointProperties struct {
	CustomDNSConfigs                    []interface{}                  `json:"customDnsConfigs"`
	CustomNetworkInterfaceName          string                         `json:"customNetworkInterfaceName"`
	IPConfigurations                    []interface{}                  `json:"ipConfigurations"`
	ManualPrivateLinkServiceConnections []interface{}                  `json:"manualPrivateLinkServiceConnections"`
	NetworkInterfaces                   []Subnet                       `json:"networkInterfaces"`
	PrivateLinkServiceConnections       []PrivateLinkServiceConnection `json:"privateLinkServiceConnections"`
	ProvisioningState                   string                         `json:"provisioningState"`
	Subnet                              Subnet                         `json:"subnet"`
}

type Tags map[string]string

type Subnet struct {
	ID string `json:"id"`
}

type PrivateLinkServiceConnection struct {
	Etag       string                                 `json:"etag"`
	ID         string                                 `json:"id"`
	Name       string                                 `json:"name"`
	Properties PrivateLinkServiceConnectionProperties `json:"properties"`
	Type       string                                 `json:"type"`
}

type PrivateLinkServiceConnectionProperties struct {
	GroupIDS                          []string                          `json:"groupIds"`
	PrivateLinkServiceConnectionState PrivateLinkServiceConnectionState `json:"privateLinkServiceConnectionState"`
	PrivateLinkServiceID              string                            `json:"privateLinkServiceId"`
	ProvisioningState                 string                            `json:"provisioningState"`
}

type PrivateLinkServiceConnectionState struct {
	ActionsRequired string `json:"actionsRequired"`
	Description     string `json:"description"`
	Status          string `json:"status"`
}

type Server struct {
	ID         string           `json:"id"`
	Kind       string           `json:"kind"`
	Location   string           `json:"location"`
	Name       string           `json:"name"`
	Properties ServerProperties `json:"properties"`
	Tags       Tags             `json:"tags"`
	Type       string           `json:"type"`
}

type ServerProperties struct {
	AdministratorLogin            string                      `json:"administratorLogin"`
	Administrators                Administrators              `json:"administrators"`
	FullyQualifiedDomainName      string                      `json:"fullyQualifiedDomainName"`
	MinimalTLSVersion             string                      `json:"minimalTlsVersion"`
	PrivateEndpointConnections    []PrivateEndpointConnection `json:"privateEndpointConnections"`
	PublicNetworkAccess           string                      `json:"publicNetworkAccess"`
	RestrictOutboundNetworkAccess string                      `json:"restrictOutboundNetworkAccess"`
	State                         string                      `json:"state"`
	Version                       string                      `json:"version"`
}

type Administrators struct {
	AdministratorType         string `json:"administratorType"`
	AzureADOnlyAuthentication bool   `json:"azureADOnlyAuthentication"`
	Login                     string `json:"login"`
	PrincipalType             string `json:"principalType"`
	Sid                       string `json:"sid"`
	TenantID                  string `json:"tenantId"`
}

type PrivateEndpointConnection struct {
	ID         string                              `json:"id"`
	Properties PrivateEndpointConnectionProperties `json:"properties"`
}

type PrivateEndpointConnectionProperties struct {
	PrivateEndpoint                   Subnet                            `json:"privateEndpoint"`
	PrivateLinkServiceConnectionState PrivateLinkServiceConnectionState `json:"privateLinkServiceConnectionState"`
	ProvisioningState                 string                            `json:"provisioningState"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.RDSGraph, error) {
	original := &RDS{}
	resource := &model.RDSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Server.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "rds", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "rds"
	resource.Name = original.Server.Name
	for k, v := range original.Server.Tags {
		resource.OriginalLabels = append(resource.OriginalLabels, utils.NewLabel(k, v, resource.UID))
	}

	resource.ConnectionAddress = original.Server.Properties.FullyQualifiedDomainName
	resource.Engine = "Microsoft SQL Server"
	resource.EngineVersion = original.Server.Properties.Version
	resource.PublicAllowed = original.Server.Properties.PublicNetworkAccess == "Enabled"
	original.Ipv4FirewallRule = append(original.Ipv4FirewallRule, original.Ipv6FirewallRule...)
	resource.IpWhiteList = lo.Map(original.Ipv4FirewallRule, func(ip Ipv4FirewallRule, _ int) string {
		if ip.Properties.StartIPAddress == "0.0.0.0" && ip.Properties.EndIPAddress == "0.0.0.0" {
			return "InternalIP"
		}

		startIP, ones, err := toCidr(ip.Properties.StartIPAddress, ip.Properties.EndIPAddress)
		if err != nil || ones == 0 {
			return "0.0.0.0/0"
		}
		return fmt.Sprintf("%s/%d", startIP, ones)
	})

	return resource, nil
}

func toCidr(startIP, endIP string) (string, uint8, error) {
	start := net.ParseIP(startIP)
	end := net.ParseIP(endIP)

	if !((start.To4() != nil && end.To4() != nil) || (start.To16() != nil && end.To16() != nil)) {
		return ``, 0, fmt.Errorf(`invalid IP address(es)`)
	}

	if start.To4() != nil {
		start = start.To4()
	} else {
		start = start.To16()
	}

	if end.To4() != nil {
		end = end.To4()
	} else {
		end = end.To16()
	}

	mask := make([]byte, len(start))

	for idx := range start {
		mask[idx] = 255 - (start[idx] ^ end[idx])
	}

	var n uint8
	for _, v := range mask {
		if v == 0xff {
			n += 8
			continue
		}
		// found non-ff byte
		// count 1 bits
		for v&0x80 != 0 {
			n++
			v <<= 1
		}
	}

	return start.String(), uint8(n), nil
}
