package user

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "user"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "iam_user_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["user_list"] = append(resourceData["user_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, userSchema, userData["user_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
}

type User struct {
	RoleAssignments []RoleAssignment `json:"roleAssignments"`
	User            UserClass        `json:"user"`
	Credentials     []Credential     `json:"credentials"`
	LastLogin       string           `json:"lastLogin"`
}

type RoleAssignment struct {
	ID         string         `json:"id"`
	Name       string         `json:"name"`
	Properties UserProperties `json:"properties"`
	Type       string         `json:"type"`
}

type UserProperties struct {
	CreatedBy        string `json:"createdBy"`
	CreatedOn        string `json:"createdOn"`
	PrincipalID      string `json:"principalId"`
	PrincipalType    string `json:"principalType"`
	RoleDefinitionID string `json:"roleDefinitionId"`
	Scope            string `json:"scope"`
	UpdatedBy        string `json:"updatedBy"`
	UpdatedOn        string `json:"updatedOn"`
}

type UserClass struct {
	ID                string `json:"id"`
	AppID             string `json:"appId"`
	AccountEnabled    bool   `json:"accountEnabled"`
	CreatedDateTime   string `json:"createdDateTime"`
	DisplayName       string `json:"displayName"`
	UserPrincipalName string `json:"userPrincipalName"`
	UserType          string `json:"userType"`
	MFAState          string `json:"mfa_state"`
}

type Credential struct {
	DisplayName   string `json:"displayName"`
	EndDateTime   string `json:"endDateTime"`
	StartDateTime string `json:"startDateTime"`
	KeyID         string `json:"keyId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.UserGraph, error) {
	original := &User{}
	resource := &model.UserGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.User.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "user", original.User.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "user"
	resource.Name = original.User.UserPrincipalName
	if original.User.UserPrincipalName != "" {
		if index := strings.Index(original.User.UserPrincipalName, "@"); index != -1 {
			resource.Name = original.User.UserPrincipalName[:index]
		} else {
			resource.Name = original.User.UserPrincipalName
		}
	} else {
		resource.Name = original.User.DisplayName
	}

	resource.DisplayName = original.User.DisplayName
	resource.Enabled = original.User.AccountEnabled
	t, _ := time.Parse(time.RFC3339, original.User.CreatedDateTime)
	resource.CreatedAt = t.UnixMilli()
	resource.MFAEnabled = original.User.MFAState == "Enabled"
	if original.User.UserType == "User" {
		resource.LoginAllowed = true
		resource.MFAEnabled = original.User.MFAState != "disabled"
	} else {
		// userType == "ServicePrincipal"
		resource.LoginAllowed = false
		resource.MFAEnabled = false
	}

	t, err := time.Parse(time.RFC3339, original.LastLogin)
	if err != nil {
		// 2024-11-04 12:00:00.000+08
		resource.LastLoginAt = *************
	} else {
		resource.LastLoginAt = t.UnixMilli()
	}

	resource.Policy = lo.Map(original.RoleAssignments, func(ra RoleAssignment, _ int) *model.PolicyGraph {
		rdId := ra.Properties.RoleDefinitionID
		roleName := rdId[strings.LastIndex(rdId, "/")+1:]

		return &model.PolicyGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "policy", roleName),
				TargetUID: resource.UID,
			},
		}
	})
	resource.AccessKey = lo.Map(original.Credentials, func(key Credential, _ int) *model.AkGraph {
		return &model.AkGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ak", key.KeyID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
