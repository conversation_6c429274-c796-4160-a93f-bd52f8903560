package utils

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/utils"
	"fmt"
	"net"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
)

const ProviderID = "azure"

func ParseIPRange(cidr string, targetUID string) (*model.IPRangeGraph, error) {
	if len(cidr) == 0 {
		return nil, fmt.Errorf("invalid cidr: %s", cidr)
	} else if cidr == "*" {
		return utils.NewAnyIPRange(targetUID, false), nil
	} else if !strings.Contains(cidr, "/") {
		return &model.IPRangeGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID("", "ip_range", fmt.Sprintf("%s-%s", cidr, cidr)),
				TargetUID: targetUID,
			},
			Start: cidr,
			End:   cidr,
		}, nil
	}

	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, err
	}

	startIP := ipnet.IP

	mask := ipnet.Mask
	endIP := make(net.IP, len(startIP))
	for i := range startIP {
		endIP[i] = startIP[i] | ^mask[i]
	}

	return &model.IPRangeGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID("", "ip_range", fmt.Sprintf("%s-%s", startIP.String(), endIP.String())),
			TargetUID: targetUID,
		},
		Start: startIP.String(),
		End:   endIP.String(),
	}, nil
}

func ParsePortRange(portRange string, targetUID string) (*model.PortRangeGraph, error) {
	if portRange == "*" {
		return utils.NewPortRange(0, 65535, targetUID), nil
	}

	ports := strings.Split(portRange, "-")
	if len(ports) == 2 {
		portStart, _ := strconv.Atoi(ports[0])
		portStart = lo.Ternary(portStart < 0, 0, portStart)
		portEnd, _ := strconv.Atoi(ports[1])
		portEnd = lo.Ternary(portEnd < 0, 65535, portEnd)
		return utils.NewPortRange(portStart, portEnd, targetUID), nil
	} else if len(ports) == 1 {
		port, _ := strconv.Atoi(ports[0])
		return utils.NewPortRange(port, port, targetUID), nil
	} else {
		return nil, fmt.Errorf("invalid port range: %s", portRange)
	}
}

type PolicyDocument struct {
	Statement []Statement `json:"Statement"`
	Version   string      `json:"Version"`
}

type Statement struct {
	Action    any    `json:"Action"`
	Effect    string `json:"Effect"`
	Resource  any    `json:"Resource"`
	Condition any    `json:"Condition"`
	Principal any    `json:"Principal"`
}

func ParsePolicyDocument(policyDocument string, targetUID string) []*model.PolicyStatementGraph {
	if policyDocument == "" {
		return nil
	}

	parsedDocument := PolicyDocument{}
	sonic.UnmarshalString(policyDocument, &parsedDocument)

	return lo.Map(parsedDocument.Statement, func(statement Statement, _ int) *model.PolicyStatementGraph {
		pd := &model.PolicyStatementGraph{}
		pd.Effect = strings.ToLower(statement.Effect)

		statementStr, _ := sonic.MarshalString(statement)
		pd.UID = utils.GenerateUID(ProviderID, "policy_statement", statementStr)
		pd.TargetUID = targetUID

		v, ok := statement.Action.(string)
		vSlice, _ := statement.Action.([]any)
		pd.Action = lo.Ternary(ok, []string{v}, lo.FilterMap(vSlice, func(x any, _ int) (string, bool) {
			s, ok := x.(string)
			return s, ok
		}))
		v, ok = statement.Resource.(string)
		vSlice, _ = statement.Resource.([]any)
		pd.Resource = lo.Ternary(ok, []string{v}, lo.FilterMap(vSlice, func(x any, _ int) (string, bool) {
			s, ok := x.(string)
			return s, ok
		}))

		pd.Principal = utils.ToJsonStr(statement.Principal)
		pd.Condition = utils.ToJsonStr(statement.Condition)
		return pd
	})
}

func FormatCIDR(cidr string) string {
	cidr = strings.TrimSpace(cidr)
	if strings.Contains(cidr, "/") {
		return cidr
	}
	return cidr + "/32"
}

func GetNameFromTags(tags []*model.KVGraph) string {
	name, found := lo.Find(tags, func(e *model.KVGraph) bool { return strings.EqualFold(e.Key, "Name") })
	if found {
		return name.Value
	}
	return ""
}
