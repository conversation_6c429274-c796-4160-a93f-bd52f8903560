package azure

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/azure/ak"
	"AssetStandardizer/transform/azure/ebs"
	"AssetStandardizer/transform/azure/ecs"
	"AssetStandardizer/transform/azure/k8s"
	"AssetStandardizer/transform/azure/lb"
	"AssetStandardizer/transform/azure/lb_listener"
	"AssetStandardizer/transform/azure/peer_connection"
	"AssetStandardizer/transform/azure/policy"
	"AssetStandardizer/transform/azure/rds"
	"AssetStandardizer/transform/azure/security_group"
	"AssetStandardizer/transform/azure/user"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

const ProviderID = "azure"

var AssetMessageChan = make(chan *model.AssetMessage, 100)
var AssetTypeMap = map[string]chan *model.AssetMessage{}

func init() {
	resourceServices := []model.ResourceService{
		ak.NewResourceService(),
		user.NewResourceService(),
		policy.NewResourceService(),
		ecs.NewResourceService(),
		security_group.NewResourceService(),
		lb.NewLBService(),
		lb_listener.NewLBListenerService(),
		peer_connection.NewResourceService(),
		ebs.NewVolumeService(),
		ebs.NewImageService(),
		ebs.NewSnapshotService(),
		rds.NewResourceService(),
		k8s.NewResourceService(),
	}

	for _, resourceService := range resourceServices {
		if err := resourceService.Validate(); err != nil {
			logger.Errorf("validate resource service failed: %s", err)
			continue
		}
		resourceService.Start()
		AssetTypeMap[resourceService.GetMessageType()] = resourceService.GetAssetMsgCh()
	}
	go dispatch()
}

func dispatch() {
	for assetMsg := range AssetMessageChan {
		if assetMsgChan, ok := AssetTypeMap[assetMsg.Type]; ok {
			assetMsgChan <- assetMsg
		} else {
			logger.Errorf("unknown type: %s", assetMsg.Type)
			continue
		}
	}
}
