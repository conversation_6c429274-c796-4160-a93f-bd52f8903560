package policy

import "AssetStandardizer/graph"

var policySchema = graph.NodeSchema{
	Label: "IAMPolicy",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"class":              {Name: "class"},
	},
}

var policyStatementSchema = graph.NodeSchema{
	Label: "IAMPolicyStatement",
	Properties: map[string]graph.PropertyRef{
		"uid":       {Name: "uid"},
		"effect":    {Name: "effect"},
		"action":    {Name: "action"},
		"resource":  {Name: "resource"},
		"principal": {Name: "principal"},
		"condition": {Name: "condition"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "IAMPolicy",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": graph.PropertyRef{Name: "target_uid"},
			},
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "STATEMENT",
		},
	},
}
