package policy

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "iam_role_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   10 * time.Second,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resources := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		policy, err := parseOne(assetMsg)
		if err != nil {
			return nil, err
		} else {
			policy.TransformedObject, _ = sonic.MarshalString(policy)
		}

		resources["policy_list"] = append(resources["policy_list"], utils.GenParamsFromStruct(policy))
		for _, statement := range policy.PolicyStatement {
			resources["policy_statement_list"] = append(resources["policy_statement_list"],
				utils.GenParamsFromStruct(statement),
			)
		}
	}
	return resources, nil
}

func updateResources(resources map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, policySchema, resources["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policyStatementSchema, resources["policy_statement_list"], map[string]any{"last_updated": "test"})
}

type Role struct {
	Role RoleClass `json:"role"`
}

type RoleClass struct {
	ID         string         `json:"id"`
	Name       string         `json:"name"`
	Properties RoleProperties `json:"properties"`
	Type       string         `json:"type"`
}

type RoleProperties struct {
	AssignableScopes []string     `json:"assignableScopes"`
	CreatedBy        string       `json:"createdBy"`
	CreatedOn        string       `json:"createdOn"`
	Description      string       `json:"description"`
	Permissions      []Permission `json:"permissions"`
	RoleName         string       `json:"roleName"`
	Type             string       `json:"type"`
	UpdatedBy        string       `json:"updatedBy"`
	UpdatedOn        string       `json:"updatedOn"`
}

type Permission struct {
	Actions        []string `json:"actions"`
	DataActions    []string `json:"dataActions"`
	NotActions     []string `json:"notActions"`
	NotDataActions []string `json:"notDataActions"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.PolicyGraph, error) {
	original := &Role{}
	resource := &model.PolicyGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.Region = assetMsg.Region
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.OriginalID = original.Role.ID
	// same role would have different in different subscription, so use name as uid
	resource.UID = utils.GenerateUID(resource.Provider, "policy", original.Role.Name)
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "policy"
	resource.Name = original.Role.Name
	resource.Description = original.Role.Properties.Description

	resource.Class = lo.Ternary(original.Role.Properties.Type == "BuiltInRole", "managed", "custom")
	resource.PolicyStatement = lo.Flatten(
		lo.Map(original.Role.Properties.Permissions, func(permission Permission, _ int) []*model.PolicyStatementGraph {
			docs := []*model.PolicyStatementGraph{}
			if len(permission.Actions) != 0 || len(permission.DataActions) != 0 {
				doc := &model.PolicyStatementGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "policy_statement", original.Role.ID),
						TargetUID: resource.UID,
					},
					Effect:   "allow",
					Resource: original.Role.Properties.AssignableScopes,
				}
				doc.Action = append(doc.Action, permission.Actions...)
				doc.Action = append(doc.Action, permission.DataActions...)

				docJson, _ := sonic.MarshalString(doc)
				doc.UID = utils.GenerateUID(resource.Provider, "policy_statement", docJson)

				docs = append(docs, doc)
			}
			if len(permission.NotActions) != 0 || len(permission.NotDataActions) != 0 {
				doc := &model.PolicyStatementGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "policy_statement", original.Role.ID),
						TargetUID: resource.UID,
					},
					Effect:   "deny",
					Resource: original.Role.Properties.AssignableScopes,
				}
				doc.Action = append(doc.Action, permission.NotActions...)
				doc.Action = append(doc.Action, permission.NotDataActions...)

				docJson, _ := sonic.MarshalString(doc)
				doc.UID = utils.GenerateUID(resource.Provider, "policy_statement", docJson)

				docs = append(docs, doc)
			}
			return docs
		}),
	)

	return resource, nil
}
