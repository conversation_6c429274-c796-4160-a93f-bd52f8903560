package peer_connection

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "peer-connection"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "network_peerConnection_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["peer_connection_list"] = append(resourceData["peer_connection_list"],
			utils.GenParamsFromStruct(resource),
		)
		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.FromVPC)...,
		)
		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.ToVPC)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, peerConnectionSchema, resourceData["peer_connection_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
}

type PeerConnection struct {
	PeerVpc         PeerVpc         `json:"peerVpc"`
	VirturalNetwork VirturalNetwork `json:"virturalNetwork"`
}

type PeerVpc struct {
	ID         string            `json:"id"`
	Name       string            `json:"name"`
	Properties PeerVpcProperties `json:"properties"`
}

type PeerVpcProperties struct {
	AllowVirtualNetworkAccess        bool                 `json:"allowVirtualNetworkAccess"`
	AllowForwardedTraffic            bool                 `json:"allowForwardedTraffic"`
	AllowGatewayTransit              bool                 `json:"allowGatewayTransit"`
	UseRemoteGateways                bool                 `json:"useRemoteGateways"`
	RemoteVirtualNetwork             RemoteVirtualNetwork `json:"remoteVirtualNetwork"`
	RemoteAddressSpace               AddressSpace         `json:"remoteAddressSpace"`
	RemoteVirtualNetworkAddressSpace AddressSpace         `json:"remoteVirtualNetworkAddressSpace"`
	RemoteBGPCommunities             RemoteBGPCommunities `json:"remoteBgpCommunities"`
	PeeringState                     string               `json:"peeringState"`
	PeeringSyncLevel                 string               `json:"peeringSyncLevel"`
	ProvisioningState                string               `json:"provisioningState"`
}

type AddressSpace struct {
	AddressPrefixes []string `json:"addressPrefixes"`
}

type RemoteBGPCommunities struct {
	VirtualNetworkCommunity string `json:"virtualNetworkCommunity"`
	RegionalCommunity       string `json:"regionalCommunity"`
}

type RemoteVirtualNetwork struct {
	ID string `json:"id"`
}

type VirturalNetwork struct {
	Etag       string                    `json:"etag"`
	ID         string                    `json:"id"`
	Location   string                    `json:"location"`
	Name       string                    `json:"name"`
	Properties VirturalNetworkProperties `json:"properties"`
	Type       string                    `json:"type"`
}

type VirturalNetworkProperties struct {
	AddressSpace           AddressSpace `json:"addressSpace"`
	EnableDdosProtection   bool         `json:"enableDdosProtection"`
	ProvisioningState      string       `json:"provisioningState"`
	ResourceGUID           string       `json:"resourceGuid"`
	Subnets                []PeerSubnet `json:"subnets"`
	VirtualNetworkPeerings []PeerVpc    `json:"virtualNetworkPeerings"`
}

type PeerSubnet struct {
	Etag       string           `json:"etag"`
	ID         string           `json:"id"`
	Name       string           `json:"name"`
	Properties SubnetProperties `json:"properties"`
	Type       string           `json:"type"`
}

type SubnetProperties struct {
	AddressPrefix                     string                 `json:"addressPrefix"`
	Delegations                       []interface{}          `json:"delegations"`
	IPConfigurations                  []RemoteVirtualNetwork `json:"ipConfigurations"`
	PrivateEndpointNetworkPolicies    string                 `json:"privateEndpointNetworkPolicies"`
	PrivateLinkServiceNetworkPolicies string                 `json:"privateLinkServiceNetworkPolicies"`
	ProvisioningState                 string                 `json:"provisioningState"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.PeerConnectionGraph, error) {
	original := &PeerConnection{}
	resource := &model.PeerConnectionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = fmt.Sprintf("%s->%s", original.VirturalNetwork.ID, original.PeerVpc.ID)
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "peer-connection", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "peer-connection"
	resource.Name = fmt.Sprintf("%s->%s", original.VirturalNetwork.Name, original.PeerVpc.Name)

	resource.Status = lo.Ternary(strings.ToLower(original.PeerVpc.Properties.PeeringState) == "connected", "running", "stopped")
	resource.FromVPC = append(resource.FromVPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.VirturalNetwork.ID),
			TargetUID: resource.UID,
		},
	})
	resource.ToVPC = append(resource.ToVPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.PeerVpc.ID),
			TargetUID: resource.UID,
		},
	})
	return resource, nil
}
