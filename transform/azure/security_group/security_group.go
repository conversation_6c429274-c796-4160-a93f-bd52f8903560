package security_group

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "security_group"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "network_sg_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["sg_list"] = append(resourceData["sg_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_rule_list"] = append(resourceData["sg_rule_list"],
			utils.GenParamsFromStructSlice(resource.Rules)...,
		)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstPortRange)
			})...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgRuleSchema, resourceData["sg_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIPRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIPRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type SG struct {
	Etag       string       `json:"etag"`
	ID         string       `json:"id"`
	Location   string       `json:"location"`
	Name       string       `json:"name"`
	Properties SGProperties `json:"properties"`
	Tags       Tags         `json:"tags"`
	Type       string       `json:"type"`
}

type Tags map[string]string

type SGProperties struct {
	DefaultSecurityRules []SecurityRule       `json:"defaultSecurityRules"`
	NetworkInterfaces    []SGNetworkInterface `json:"networkInterfaces"`
	ProvisioningState    string               `json:"provisioningState"`
	ResourceGUID         string               `json:"resourceGuid"`
	SecurityRules        []SecurityRule       `json:"securityRules"`
	Subnets              []SGNetworkInterface `json:"subnets"`
}

type SecurityRule struct {
	Etag       string                 `json:"etag"`
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Properties SecurityRuleProperties `json:"properties"`
	Type       string                 `json:"type"`
}

type SecurityRuleProperties struct {
	Access                     string   `json:"access"`
	Description                string   `json:"description"`
	DestinationAddressPrefix   string   `json:"destinationAddressPrefix"`
	DestinationAddressPrefixes []string `json:"destinationAddressPrefixes"`
	DestinationPortRange       string   `json:"destinationPortRange"`
	DestinationPortRanges      []string `json:"destinationPortRanges"`
	Direction                  string   `json:"direction"`
	Priority                   int      `json:"priority"`
	Protocol                   string   `json:"protocol"`
	ProvisioningState          string   `json:"provisioningState"`
	SourceAddressPrefix        string   `json:"sourceAddressPrefix"`
	SourceAddressPrefixes      []string `json:"sourceAddressPrefixes"`
	SourcePortRange            string   `json:"sourcePortRange"`
	SourcePortRanges           []string `json:"sourcePortRanges"`
}

type SGNetworkInterface struct {
	ID string `json:"id"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.SGGraph, error) {
	original := &SG{}
	resource := &model.SGGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "security-group", original.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "security-group"
	resource.Name = original.Name
	for k, v := range original.Tags {
		resource.OriginalLabels = append(resource.OriginalLabels, utils.NewLabel(k, v, resource.UID))
	}

	resource.IsDefault = false
	subnetIds := lo.Uniq(
		lo.FilterMap(original.Properties.Subnets, func(e SGNetworkInterface, _ int) (string, bool) {
			return e.ID, e.ID != ""
		}),
	)
	resource.VPC = append(resource.VPC, lo.FilterMap(subnetIds, func(e string, _ int) (*model.VPCGraph, bool) {
		if i := strings.Index(e, "/subnets/"); i <= 0 {
			return nil, false
		} else {
			return &model.VPCGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "vpc", e[0:i]),
					TargetUID: resource.UID,
				},
			}, true
		}
	})...)

	rules, err := parseSGRule(original.Properties.SecurityRules, resource.UID)
	if err != nil {
		return nil, fmt.Errorf("parseSGRule failed, perm: %v, err: %v", original.Properties.SecurityRules, err)
	}
	resource.Rules = append(resource.Rules, rules...)

	return resource, nil
}

func parseSGRule(perms []SecurityRule, targetUID string) ([]*model.SGRuleGraph, error) {
	results := []*model.SGRuleGraph{}
	for _, perm := range perms {
		rule := &model.SGRuleGraph{}
		ruleJson, err := sonic.MarshalString(perm)
		if err != nil {
			return nil, fmt.Errorf("marshal perm error: %s, perm: %v", err, perm)
		}
		rule.UID = utils.GenerateUID(provider_utils.ProviderID, "sg_rule", ruleJson)
		rule.TargetUID = targetUID
		rule.Description = perm.Properties.Description
		rule.Protocol = lo.Ternary(perm.Properties.Protocol == "*", "all", strings.ToLower(perm.Properties.Protocol))
		rule.Policy = lo.Ternary(perm.Properties.Access == "Allow", "accept", "drop")
		rule.Priority = perm.Properties.Priority
		rule.Direction = lo.Ternary(perm.Properties.Direction == "Inbound", "ingress", "egress")

		defaultCIDR := "0.0.0.0/0"
		if strings.Contains(perm.Properties.SourceAddressPrefix, ":") || strings.Contains(perm.Properties.DestinationAddressPrefix, ":") {
			defaultCIDR = "::/0"
		}

		cidrs := append(perm.Properties.SourceAddressPrefixes, perm.Properties.SourceAddressPrefix)
		if len(cidrs) == 0 {
			cidrs = append(cidrs, defaultCIDR)
		}
		for _, cidr := range cidrs {
			if ipRange, err := provider_utils.ParseIPRange(cidr, rule.UID); err != nil {
				return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, cidr)
			} else {
				rule.SrcIPRange = append(rule.SrcIPRange, ipRange)
			}
		}
		portRanges := append(perm.Properties.SourcePortRanges, perm.Properties.SourcePortRange)
		for _, portRange := range portRanges {
			if p, err := provider_utils.ParsePortRange(portRange, rule.UID); err != nil {
				return nil, fmt.Errorf("parse port range error: %v, port: %s", err, portRange)
			} else {
				rule.SrcPortRange = append(rule.SrcPortRange, p)
			}
		}

		cidrs = append(perm.Properties.DestinationAddressPrefixes, perm.Properties.DestinationAddressPrefix)
		if len(cidrs) == 0 {
			cidrs = append(cidrs, defaultCIDR)
		}
		for _, cidr := range cidrs {
			if ipRange, err := provider_utils.ParseIPRange(cidr, rule.UID); err != nil {
				return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, cidr)
			} else {
				rule.DstIPRange = append(rule.DstIPRange, ipRange)
			}
		}
		portRanges = append(perm.Properties.DestinationPortRanges, perm.Properties.DestinationPortRange)
		for _, portRange := range portRanges {
			if p, err := provider_utils.ParsePortRange(portRange, rule.UID); err != nil {
				return nil, fmt.Errorf("parse port range error: %v, port: %s", err, portRange)
			} else {
				rule.DstPortRange = append(rule.DstPortRange, p)
			}

		}

		results = append(results, rule)
	}
	return results, nil
}
