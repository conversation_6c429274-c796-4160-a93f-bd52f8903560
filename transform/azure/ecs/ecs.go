package ecs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/azure/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "compute_vm_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type ECS struct {
	Nics []NIC `json:"nics"`
	VM   VM    `json:"vm"`
}

type NIC struct {
	Etag       string        `json:"etag"`
	ID         string        `json:"id"`
	Location   string        `json:"location"`
	Name       string        `json:"name"`
	Properties NICProperties `json:"properties"`
	Type       string        `json:"type"`
}

type NICProperties struct {
	AuxiliaryMode               string                  `json:"auxiliaryMode"`
	AuxiliarySku                string                  `json:"auxiliarySku"`
	DisableTCPStateTracking     bool                    `json:"disableTcpStateTracking"`
	DNSSettings                 PurpleDNSSettings       `json:"dnsSettings"`
	EnableAcceleratedNetworking bool                    `json:"enableAcceleratedNetworking"`
	EnableIPForwarding          bool                    `json:"enableIPForwarding"`
	HostedWorkloads             []interface{}           `json:"hostedWorkloads"`
	IPConfigurations            []PurpleIPConfiguration `json:"ipConfigurations"`
	MACAddress                  string                  `json:"macAddress"`
	NetworkSecurityGroup        AvailabilitySet         `json:"networkSecurityGroup,omitempty"`
	NICType                     string                  `json:"nicType"`
	Primary                     bool                    `json:"primary"`
	ProvisioningState           string                  `json:"provisioningState"`
	ResourceGUID                string                  `json:"resourceGuid"`
	TapConfigurations           []interface{}           `json:"tapConfigurations"`
	VirtualMachine              AvailabilitySet         `json:"virtualMachine"`
	VnetEncryptionSupported     bool                    `json:"vnetEncryptionSupported"`
}

type PurpleDNSSettings struct {
	AppliedDNSServers        []interface{} `json:"appliedDnsServers"`
	DNSServers               []interface{} `json:"dnsServers"`
	InternalDomainNameSuffix string        `json:"internalDomainNameSuffix"`
}

type PurpleIPConfiguration struct {
	Etag       string           `json:"etag"`
	ID         string           `json:"id"`
	Name       string           `json:"name"`
	Properties PurpleProperties `json:"properties"`
	Type       string           `json:"type"`
}

type PurpleProperties struct {
	Primary                   bool            `json:"primary"`
	PrivateIPAddress          string          `json:"privateIPAddress"`
	PrivateIPAddressVersion   string          `json:"privateIPAddressVersion"`
	PrivateIPAllocationMethod string          `json:"privateIPAllocationMethod"`
	ProvisioningState         string          `json:"provisioningState"`
	Subnet                    AvailabilitySet `json:"subnet"`
	PublicIPAddress           PublicIPAddress `json:"publicIPAddress,omitempty"`
}

type PublicIPAddress struct {
	Etag       string                    `json:"etag"`
	ID         string                    `json:"id"`
	Location   string                    `json:"location"`
	Name       string                    `json:"name"`
	Properties PublicIPAddressProperties `json:"properties"`
	Sku        Sku                       `json:"sku"`
	Type       string                    `json:"type"`
	Zones      []string                  `json:"zones"`
}

type PublicIPAddressProperties struct {
	IdleTimeoutInMinutes     int64           `json:"idleTimeoutInMinutes"`
	IPAddress                string          `json:"ipAddress"`
	IPConfiguration          AvailabilitySet `json:"ipConfiguration"`
	IPTags                   []interface{}   `json:"ipTags"`
	ProvisioningState        string          `json:"provisioningState"`
	PublicIPAddressVersion   string          `json:"publicIPAddressVersion"`
	PublicIPAllocationMethod string          `json:"publicIPAllocationMethod"`
	ResourceGUID             string          `json:"resourceGuid"`
}

type AvailabilitySet struct {
	ID string `json:"id"`
}

type Sku struct {
	Name string `json:"name"`
	Tier string `json:"tier"`
}

type VM struct {
	Properties       VMProperties     `json:"properties"`
	Type             string           `json:"type"`
	Location         string           `json:"location"`
	Tags             Tags             `json:"tags"`
	ID               string           `json:"id"`
	Name             string           `json:"name"`
	Plan             Plan             `json:"plan"`
	Resources        []Resource       `json:"resources"`
	Identity         Identity         `json:"identity"`
	Zones            []string         `json:"zones"`
	ExtendedLocation ExtendedLocation `json:"extendedLocation"`
}

type ExtendedLocation struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

type Identity struct {
	PrincipalID            string                 `json:"principalId"`
	TenantID               string                 `json:"tenantId"`
	Type                   string                 `json:"type"`
	UserAssignedIdentities UserAssignedIdentities `json:"userAssignedIdentities"`
}

type UserAssignedIdentities map[string]UserAssignedIdentity

type UserAssignedIdentity struct {
	PrincipalID string `json:"principalId"`
	ClientID    string `json:"clientId"`
}

type Plan struct {
	Name          string `json:"name"`
	Publisher     string `json:"publisher"`
	Product       string `json:"product"`
	PromotionCode string `json:"promotionCode"`
}

type VMProperties struct {
	VMID                    string                    `json:"vmId"`
	AvailabilitySet         AvailabilitySet           `json:"availabilitySet"`
	HardwareProfile         HardwareProfile           `json:"hardwareProfile"`
	StorageProfile          StorageProfile            `json:"storageProfile"`
	OSProfile               OSProfile                 `json:"osProfile"`
	NetworkProfile          NetworkProfile            `json:"networkProfile"`
	InstanceView            InstanceView              `json:"instanceView"`
	ProvisioningState       string                    `json:"provisioningState"`
	AdditionalCapabilities  AdditionalCapabilities    `json:"additionalCapabilities"`
	SecurityProfile         PropertiesSecurityProfile `json:"securityProfile"`
	DiagnosticsProfile      DiagnosticsProfile        `json:"diagnosticsProfile"`
	VirtualMachineScaleSet  AvailabilitySet           `json:"virtualMachineScaleSet"`
	ProximityPlacementGroup AvailabilitySet           `json:"proximityPlacementGroup"`
	Priority                string                    `json:"priority"`
	EvictionPolicy          string                    `json:"evictionPolicy"`
	BillingProfile          BillingProfile            `json:"billingProfile"`
	Host                    AvailabilitySet           `json:"host"`
	HostGroup               AvailabilitySet           `json:"hostGroup"`
	LicenseType             string                    `json:"licenseType"`
	ExtensionsTimeBudget    string                    `json:"extensionsTimeBudget"`
	PlatformFaultDomain     int64                     `json:"platformFaultDomain"`
	ScheduledEventsProfile  ScheduledEventsProfile    `json:"scheduledEventsProfile"`
	UserData                string                    `json:"userData"`
	CapacityReservation     CapacityReservation       `json:"capacityReservation"`
	ApplicationProfile      ApplicationProfile        `json:"applicationProfile"`
	TimeCreated             string                    `json:"timeCreated"`
}

type AdditionalCapabilities struct {
	UltraSSDEnabled    bool `json:"ultraSSDEnabled"`
	HibernationEnabled bool `json:"hibernationEnabled"`
}

type ApplicationProfile struct {
	GalleryApplications []GalleryApplication `json:"galleryApplications"`
}

type GalleryApplication struct {
	Tags                   string `json:"tags"`
	Order                  int64  `json:"order"`
	PackageReferenceID     string `json:"packageReferenceId"`
	ConfigurationReference string `json:"configurationReference"`
}

type BillingProfile struct {
	MaxPrice int64 `json:"maxPrice"`
}

type CapacityReservation struct {
	CapacityReservationGroup AvailabilitySet `json:"capacityReservationGroup"`
}

type DiagnosticsProfile struct {
	BootDiagnostics BootDiagnostics `json:"bootDiagnostics"`
}

type BootDiagnostics struct {
	Enabled    bool   `json:"enabled"`
	StorageURI string `json:"storageUri"`
}

type HardwareProfile struct {
	VMSize           string           `json:"vmSize"`
	VMSizeProperties VMSizeProperties `json:"vmSizeProperties"`
}

type VMSizeProperties struct {
	VCPUsAvailable int64 `json:"vCPUsAvailable"`
	VCPUsPerCore   int64 `json:"vCPUsPerCore"`
}
type InstanceView struct {
	BootDiagnostics  BootDiagnosticsClass `json:"bootDiagnostics"`
	ComputerName     string               `json:"computerName"`
	Disks            []Disk               `json:"disks"`
	HyperVGeneration string               `json:"hyperVGeneration"`
	OSName           string               `json:"osName"`
	OSVersion        string               `json:"osVersion"`
	Statuses         []InstanceViewStatus `json:"statuses"`
	VMAgent          VMAgent              `json:"vmAgent"`
}

type BootDiagnosticsClass struct {
}

type Disk struct {
	Name     string       `json:"name"`
	Statuses []DiskStatus `json:"statuses"`
}

type DiskStatus struct {
	Code          string `json:"code"`
	DisplayStatus string `json:"displayStatus"`
	Level         string `json:"level"`
	Time          string `json:"time"`
}

type InstanceViewStatus struct {
	Code          string  `json:"code"`
	DisplayStatus string  `json:"displayStatus"`
	Level         string  `json:"level"`
	Time          *string `json:"time,omitempty"`
}

type VMAgent struct {
	ExtensionHandlers []interface{}   `json:"extensionHandlers"`
	Statuses          []VMAgentStatus `json:"statuses"`
	VMAgentVersion    string          `json:"vmAgentVersion"`
}

type VMAgentStatus struct {
	Code          string `json:"code"`
	DisplayStatus string `json:"displayStatus"`
	Level         string `json:"level"`
	Message       string `json:"message"`
	Time          string `json:"time"`
}

type NetworkProfile struct {
	NetworkInterfaces              []NetworkInterface              `json:"networkInterfaces"`
	NetworkAPIVersion              string                          `json:"networkApiVersion"`
	NetworkInterfaceConfigurations []NetworkInterfaceConfiguration `json:"networkInterfaceConfigurations"`
}

type NetworkInterfaceConfiguration struct {
	Name       string                                  `json:"name"`
	Properties NetworkInterfaceConfigurationProperties `json:"properties"`
}

type NetworkInterfaceConfigurationProperties struct {
	Primary                     bool                    `json:"primary"`
	DeleteOption                string                  `json:"deleteOption"`
	EnableAcceleratedNetworking bool                    `json:"enableAcceleratedNetworking"`
	DisableTCPStateTracking     bool                    `json:"disableTcpStateTracking"`
	EnableFPGA                  bool                    `json:"enableFpga"`
	EnableIPForwarding          bool                    `json:"enableIPForwarding"`
	NetworkSecurityGroup        AvailabilitySet         `json:"networkSecurityGroup"`
	DNSSettings                 FluffyDNSSettings       `json:"dnsSettings"`
	IPConfigurations            []FluffyIPConfiguration `json:"ipConfigurations"`
	DscpConfiguration           AvailabilitySet         `json:"dscpConfiguration"`
}

type FluffyDNSSettings struct {
	DNSServers []string `json:"dnsServers"`
}

type FluffyIPConfiguration struct {
	Name       string           `json:"name"`
	Properties FluffyProperties `json:"properties"`
}

type FluffyProperties struct {
	Subnet                                AvailabilitySet              `json:"subnet"`
	Primary                               bool                         `json:"primary"`
	PublicIPAddressConfiguration          PublicIPAddressConfiguration `json:"publicIPAddressConfiguration"`
	PrivateIPAddressVersion               string                       `json:"privateIPAddressVersion"`
	ApplicationSecurityGroups             []AvailabilitySet            `json:"applicationSecurityGroups"`
	ApplicationGatewayBackendAddressPools []AvailabilitySet            `json:"applicationGatewayBackendAddressPools"`
	LoadBalancerBackendAddressPools       []AvailabilitySet            `json:"loadBalancerBackendAddressPools"`
}

type PublicIPAddressConfiguration struct {
	Name       string                                 `json:"name"`
	Properties PublicIPAddressConfigurationProperties `json:"properties"`
	Sku        Sku                                    `json:"sku"`
}

type PublicIPAddressConfigurationProperties struct {
	IdleTimeoutInMinutes     int64                `json:"idleTimeoutInMinutes"`
	DeleteOption             string               `json:"deleteOption"`
	DNSSettings              TentacledDNSSettings `json:"dnsSettings"`
	IPTags                   []IPTag              `json:"ipTags"`
	PublicIPPrefix           AvailabilitySet      `json:"publicIPPrefix"`
	PublicIPAddressVersion   string               `json:"publicIPAddressVersion"`
	PublicIPAllocationMethod string               `json:"publicIPAllocationMethod"`
}

type TentacledDNSSettings struct {
	DomainNameLabel      string `json:"domainNameLabel"`
	DomainNameLabelScope string `json:"domainNameLabelScope"`
}

type IPTag struct {
	IPTagType string `json:"ipTagType"`
	Tag       string `json:"tag"`
}

type NetworkInterface struct {
	ID         string                     `json:"id"`
	Properties NetworkInterfaceProperties `json:"properties"`
}

type NetworkInterfaceProperties struct {
	Primary      bool   `json:"primary"`
	DeleteOption string `json:"deleteOption"`
}

type OSProfile struct {
	ComputerName                string               `json:"computerName"`
	AdminUsername               string               `json:"adminUsername"`
	WindowsConfiguration        WindowsConfiguration `json:"windowsConfiguration"`
	Secrets                     []interface{}        `json:"secrets"`
	AllowExtensionOperations    bool                 `json:"allowExtensionOperations"`
	CustomData                  string               `json:"customData"`
	LinuxConfiguration          LinuxConfiguration   `json:"linuxConfiguration"`
	RequireGuestProvisionSignal bool                 `json:"requireGuestProvisionSignal"`
}

type LinuxConfiguration struct {
	DisablePasswordAuthentication bool                            `json:"disablePasswordAuthentication"`
	SSH                           SSH                             `json:"ssh"`
	ProvisionVMAgent              bool                            `json:"provisionVMAgent"`
	PatchSettings                 LinuxConfigurationPatchSettings `json:"patchSettings"`
}

type LinuxConfigurationPatchSettings struct {
	PatchMode      string `json:"patchMode"`
	AssessmentMode string `json:"assessmentMode"`
}

type SSH struct {
	PublicKeys []PublicKey `json:"publicKeys"`
}

type PublicKey struct {
	Path    string `json:"path"`
	KeyData string `json:"keyData"`
}

type WindowsConfiguration struct {
	ProvisionVMAgent          bool                              `json:"provisionVMAgent"`
	EnableAutomaticUpdates    bool                              `json:"enableAutomaticUpdates"`
	TimeZone                  string                            `json:"timeZone"`
	AdditionalUnattendContent []AdditionalUnattendContent       `json:"additionalUnattendContent"`
	PatchSettings             WindowsConfigurationPatchSettings `json:"patchSettings"`
	WinRM                     WinRM                             `json:"winRM"`
}

type AdditionalUnattendContent struct {
	PassName      string `json:"passName"`
	ComponentName string `json:"componentName"`
	SettingName   string `json:"settingName"`
	Content       string `json:"content"`
}

type WindowsConfigurationPatchSettings struct {
	PatchMode         string `json:"patchMode"`
	EnableHotpatching bool   `json:"enableHotpatching"`
	AssessmentMode    string `json:"assessmentMode"`
}

type WinRM struct {
	Listeners []Listener `json:"listeners"`
}

type Listener struct {
	Protocol       string `json:"protocol"`
	CertificateURL string `json:"certificateUrl"`
}

type ScheduledEventsProfile struct {
	TerminateNotificationProfile ENotificationProfile `json:"terminateNotificationProfile"`
	OSImageNotificationProfile   ENotificationProfile `json:"osImageNotificationProfile"`
}

type ENotificationProfile struct {
	NotBeforeTimeout string `json:"notBeforeTimeout"`
	Enable           bool   `json:"enable"`
}

type PropertiesSecurityProfile struct {
	UEFISettings     UEFISettings `json:"uefiSettings"`
	EncryptionAtHost bool         `json:"encryptionAtHost"`
	SecurityType     string       `json:"securityType"`
}

type UEFISettings struct {
	SecureBootEnabled bool `json:"secureBootEnabled"`
	VTPMEnabled       bool `json:"vTpmEnabled"`
}

type StorageProfile struct {
	ImageReference ImageReference `json:"imageReference"`
	OSDisk         OSDisk         `json:"osDisk"`
	DataDisks      []interface{}  `json:"dataDisks"`
}

type ImageReference struct {
	Publisher               string `json:"publisher"`
	Offer                   string `json:"offer"`
	Sku                     string `json:"sku"`
	Version                 string `json:"version"`
	ExactVersion            string `json:"exactVersion"`
	SharedGalleryImageID    string `json:"sharedGalleryImageId"`
	CommunityGalleryImageID string `json:"communityGalleryImageId"`
	ID                      string `json:"id"`
}

type OSDisk struct {
	OSType                  string             `json:"osType"`
	Name                    string             `json:"name"`
	CreateOption            string             `json:"createOption"`
	Vhd                     Image              `json:"vhd"`
	Caching                 string             `json:"caching"`
	DiskSizeGB              int64              `json:"diskSizeGB"`
	EncryptionSettings      EncryptionSettings `json:"encryptionSettings"`
	Image                   Image              `json:"image"`
	WriteAcceleratorEnabled bool               `json:"writeAcceleratorEnabled"`
	DiffDiskSettings        DiffDiskSettings   `json:"diffDiskSettings"`
	ManagedDisk             ManagedDisk        `json:"managedDisk"`
	DeleteOption            string             `json:"deleteOption"`
}

type DiffDiskSettings struct {
	Option    string `json:"option"`
	Placement string `json:"placement"`
}

type EncryptionSettings struct {
	DiskEncryptionKey DiskEncryptionKey `json:"diskEncryptionKey"`
	KeyEncryptionKey  KeyEncryptionKey  `json:"keyEncryptionKey"`
	Enabled           bool              `json:"enabled"`
}

type DiskEncryptionKey struct {
	SecretURL   string          `json:"secretUrl"`
	SourceVault AvailabilitySet `json:"sourceVault"`
}

type KeyEncryptionKey struct {
	KeyURL      string          `json:"keyUrl"`
	SourceVault AvailabilitySet `json:"sourceVault"`
}

type Image struct {
	URI string `json:"uri"`
}

type ManagedDisk struct {
	StorageAccountType string                     `json:"storageAccountType"`
	DiskEncryptionSet  AvailabilitySet            `json:"diskEncryptionSet"`
	SecurityProfile    ManagedDiskSecurityProfile `json:"securityProfile"`
	ID                 string                     `json:"id"`
}

type ManagedDiskSecurityProfile struct {
	SecurityEncryptionType string          `json:"securityEncryptionType"`
	DiskEncryptionSet      AvailabilitySet `json:"diskEncryptionSet"`
}

type Resource struct {
	Properties ResourceProperties `json:"properties"`
	ID         string             `json:"id"`
	Name       string             `json:"name"`
	Type       string             `json:"type"`
	Location   string             `json:"location"`
	Tags       ResourceTags       `json:"tags"`
}

type ResourceProperties struct {
	ForceUpdateTag                string                        `json:"forceUpdateTag"`
	Publisher                     string                        `json:"publisher"`
	Type                          string                        `json:"type"`
	TypeHandlerVersion            string                        `json:"typeHandlerVersion"`
	AutoUpgradeMinorVersion       bool                          `json:"autoUpgradeMinorVersion"`
	EnableAutomaticUpgrade        bool                          `json:"enableAutomaticUpgrade"`
	Settings                      Tags                          `json:"settings"`
	ProtectedSettings             Tags                          `json:"protectedSettings"`
	ProvisioningState             string                        `json:"provisioningState"`
	SuppressFailures              bool                          `json:"suppressFailures"`
	ProtectedSettingsFromKeyVault ProtectedSettingsFromKeyVault `json:"protectedSettingsFromKeyVault"`
}

type Tags map[string]string

type ProtectedSettingsFromKeyVault struct {
	SourceVault AvailabilitySet `json:"sourceVault"`
	SecretURL   string          `json:"secretUrl"`
}

type ResourceTags map[string]string

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &ECS{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.VM.ID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.VM.ID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ecs"
	resource.Name = original.VM.Name
	for k, v := range original.VM.Tags {
		resource.OriginalLabels = append(resource.OriginalLabels, utils.NewLabel(k, v, resource.UID))
	}

	resource.Hostname = original.VM.Properties.OSProfile.ComputerName
	resource.Class = "cvm"
	resource.OSName = fmt.Sprintf("%s %s", original.VM.Properties.StorageProfile.ImageReference.Offer, original.VM.Properties.StorageProfile.ImageReference.ExactVersion)
	osType := strings.ToLower(original.VM.Properties.StorageProfile.OSDisk.OSType)
	resource.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")
	resource.Spec = original.VM.Properties.HardwareProfile.VMSize
	status, _ := lo.Find(original.VM.Properties.InstanceView.Statuses, func(e InstanceViewStatus) bool {
		return strings.HasPrefix(e.Code, "PowerState/")
	})
	resource.Status = lo.Ternary(status.Code == "PowerState/running", "running", "stopped") // FIXME: 需要从实例视图获取状态

	allIPs := lo.Flatten(
		lo.Map(original.Nics, func(e NIC, _ int) []PurpleIPConfiguration {
			return e.Properties.IPConfigurations
		}),
	)

	ipConf, _ := lo.Find(allIPs, func(e PurpleIPConfiguration) bool {
		return e.Properties.Primary
	})
	resource.PrimaryPrivateIP = ipConf.Properties.PrivateIPAddress
	ipConf, _ = lo.Find(allIPs, func(e PurpleIPConfiguration) bool {
		return e.Properties.Primary && e.Properties.PublicIPAddress.ID != ""
	})
	resource.PrimaryPublicIP = ipConf.Properties.PublicIPAddress.Properties.IPAddress

	resource.PrivateIPList = lo.Map(allIPs, func(e PurpleIPConfiguration, _ int) string { return e.Properties.PrivateIPAddress })
	resource.PublicIPList = lo.FilterMap(allIPs, func(e PurpleIPConfiguration, _ int) (string, bool) {
		return e.Properties.PublicIPAddress.Properties.IPAddress, e.Properties.PublicIPAddress.ID != ""
	})

	subnetIds := lo.Uniq(
		lo.Map(allIPs, func(e PurpleIPConfiguration, _ int) string {
			return e.Properties.Subnet.ID
		}),
	)
	resource.Subnet = append(resource.Subnet, lo.Map(subnetIds, func(e string, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", e),
				TargetUID: resource.UID,
			},
		}
	})...)
	resource.VPC = append(resource.VPC, lo.FilterMap(subnetIds, func(e string, _ int) (*model.VPCGraph, bool) {
		if i := strings.Index(e, "/subnets/"); i <= 0 {
			return nil, false
		} else {
			return &model.VPCGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "vpc", e[0:i]),
					TargetUID: resource.UID,
				},
			}, true
		}
	})...)

	resource.SG = append(resource.SG,
		lo.FilterMap(original.Nics, func(e NIC, _ int) (*model.SGGraph, bool) {
			if e.Properties.NetworkSecurityGroup.ID == "" {
				return nil, false
			}
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e.Properties.NetworkSecurityGroup.ID),
					TargetUID: resource.UID,
				},
			}, true
		})...,
	)
	resource.ENI = append(resource.ENI,
		lo.Map(original.Nics, func(e NIC, _ int) *model.ENIGraph {
			return &model.ENIGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "eni", e.ID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	return resource, nil
}
