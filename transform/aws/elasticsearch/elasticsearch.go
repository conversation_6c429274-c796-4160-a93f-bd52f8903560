package elasticsearch

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "elasticsearch"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "es_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["es_list"] = append(resourceData["es_list"], utils.GenParamsFromStruct(resource))

		resourceData["es_node_list"] = append(resourceData["es_node_list"],
			utils.GenParamsFromStructSlice(resource.Node)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, esSchema, resourceData["es_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, esNodeSchema, resourceData["es_node_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
}

type Es struct {
	Instance EsInstance `json:"instance"`
	Nodes    []Node     `json:"nodes"`
}

type EsInstance struct {
	Arn                         string                  `json:"ARN"`
	AccessPolicies              string                  `json:"AccessPolicies"`
	AdvancedOptions             AdvancedOptions         `json:"AdvancedOptions"`
	AdvancedSecurityOptions     AdvancedSecurityOptions `json:"AdvancedSecurityOptions"`
	AutoTuneOptions             AutoTuneOptions         `json:"AutoTuneOptions"`
	ChangeProgressDetails       ChangeProgressDetails   `json:"ChangeProgressDetails"`
	ClusterConfig               ClusterConfig           `json:"ClusterConfig"`
	CognitoOptions              CognitoOptions          `json:"CognitoOptions"`
	Created                     bool                    `json:"Created"`
	Deleted                     bool                    `json:"Deleted"`
	DomainEndpointOptions       DomainEndpointOptions   `json:"DomainEndpointOptions"`
	DomainID                    string                  `json:"DomainId"`
	DomainName                  string                  `json:"DomainName"`
	DomainProcessingStatus      string                  `json:"DomainProcessingStatus"`
	EBSOptions                  EBSOptions              `json:"EBSOptions"`
	EncryptionAtRESTOptions     EncryptionAtRESTOptions `json:"EncryptionAtRestOptions"`
	Endpoint                    string                  `json:"Endpoint"`
	Endpoints                   Endpoints               `json:"Endpoints"`
	EngineVersion               string                  `json:"EngineVersion"`
	IPAddressType               string                  `json:"IPAddressType"`
	LogPublishingOptions        interface{}             `json:"LogPublishingOptions"`
	ModifyingProperties         []interface{}           `json:"ModifyingProperties"`
	NodeToNodeEncryptionOptions Options                 `json:"NodeToNodeEncryptionOptions"`
	OffPeakWindowOptions        OffPeakWindowOptions    `json:"OffPeakWindowOptions"`
	Processing                  bool                    `json:"Processing"`
	ServiceSoftwareOptions      ServiceSoftwareOptions  `json:"ServiceSoftwareOptions"`
	SnapshotOptions             SnapshotOptions         `json:"SnapshotOptions"`
	SoftwareUpdateOptions       SoftwareUpdateOptions   `json:"SoftwareUpdateOptions"`
	UpgradeProcessing           bool                    `json:"UpgradeProcessing"`
	VPCOptions                  VPCOptions              `json:"VPCOptions"`
}

type AdvancedOptions struct {
	IndicesFielddataCacheSize         string `json:"indices.fielddata.cache.size"`
	IndicesQueryBoolMaxClauseCount    string `json:"indices.query.bool.max_clause_count"`
	OverrideMainResponseVersion       string `json:"override_main_response_version"`
	RESTActionMultiAllowExplicitIndex string `json:"rest.action.multi.allow_explicit_index"`
}

type AdvancedSecurityOptions struct {
	AnonymousAuthDisableDate    interface{} `json:"AnonymousAuthDisableDate"`
	AnonymousAuthEnabled        bool        `json:"AnonymousAuthEnabled"`
	Enabled                     bool        `json:"Enabled"`
	InternalUserDatabaseEnabled bool        `json:"InternalUserDatabaseEnabled"`
	SAMLOptions                 interface{} `json:"SAMLOptions"`
}

type AutoTuneOptions struct {
	ErrorMessage     interface{} `json:"ErrorMessage"`
	State            string      `json:"State"`
	UseOffPeakWindow bool        `json:"UseOffPeakWindow"`
}

type ChangeProgressDetails struct {
	ChangeID        string      `json:"ChangeId"`
	LastUpdatedTime float64     `json:"LastUpdatedTime"`
	Message         interface{} `json:"Message"`
	StartTime       float64     `json:"StartTime"`
}

type ClusterConfig struct {
	ColdStorageOptions     Options             `json:"ColdStorageOptions"`
	DedicatedMasterCount   interface{}         `json:"DedicatedMasterCount"`
	DedicatedMasterEnabled bool                `json:"DedicatedMasterEnabled"`
	DedicatedMasterType    interface{}         `json:"DedicatedMasterType"`
	InstanceCount          int64               `json:"InstanceCount"`
	InstanceType           string              `json:"InstanceType"`
	WarmCount              interface{}         `json:"WarmCount"`
	WarmEnabled            bool                `json:"WarmEnabled"`
	WarmStorage            interface{}         `json:"WarmStorage"`
	WarmType               interface{}         `json:"WarmType"`
	ZoneAwarenessConfig    ZoneAwarenessConfig `json:"ZoneAwarenessConfig"`
	ZoneAwarenessEnabled   bool                `json:"ZoneAwarenessEnabled"`
}

type Options struct {
	Enabled bool `json:"Enabled"`
}

type ZoneAwarenessConfig struct {
	AvailabilityZoneCount int64 `json:"AvailabilityZoneCount"`
}

type CognitoOptions struct {
	Enabled        bool        `json:"Enabled"`
	IdentityPoolID interface{} `json:"IdentityPoolId"`
	RoleArn        interface{} `json:"RoleArn"`
	UserPoolID     interface{} `json:"UserPoolId"`
}

type DomainEndpointOptions struct {
	CustomEndpoint               interface{} `json:"CustomEndpoint"`
	CustomEndpointCertificateArn interface{} `json:"CustomEndpointCertificateArn"`
	CustomEndpointEnabled        bool        `json:"CustomEndpointEnabled"`
	EnforceHTTPS                 bool        `json:"EnforceHTTPS"`
	TLSSecurityPolicy            string      `json:"TLSSecurityPolicy"`
}

type EBSOptions struct {
	EBSEnabled bool        `json:"EBSEnabled"`
	Iops       interface{} `json:"Iops"`
	Throughput interface{} `json:"Throughput"`
	VolumeSize int64       `json:"VolumeSize"`
	VolumeType string      `json:"VolumeType"`
}

type EncryptionAtRESTOptions struct {
	Enabled  bool   `json:"Enabled"`
	KmsKeyID string `json:"KmsKeyId"`
}

type Endpoints struct {
	String string `json:"string"`
}

type OffPeakWindowOptions struct {
	Enabled       bool          `json:"Enabled"`
	OffPeakWindow OffPeakWindow `json:"OffPeakWindow"`
}

type OffPeakWindow struct {
	WindowStartTime WindowStartTime `json:"WindowStartTime"`
}

type WindowStartTime struct {
	Hours   int64 `json:"Hours"`
	Minutes int64 `json:"Minutes"`
}

type ServiceSoftwareOptions struct {
	AutomatedUpdateDate int64  `json:"AutomatedUpdateDate"`
	Cancellable         bool   `json:"Cancellable"`
	CurrentVersion      string `json:"CurrentVersion"`
	Description         string `json:"Description"`
	NewVersion          string `json:"NewVersion"`
	OptionalDeployment  bool   `json:"OptionalDeployment"`
	UpdateAvailable     bool   `json:"UpdateAvailable"`
	UpdateStatus        string `json:"UpdateStatus"`
}

type SnapshotOptions struct {
	AutomatedSnapshotStartHour int64 `json:"AutomatedSnapshotStartHour"`
}

type SoftwareUpdateOptions struct {
	AutoSoftwareUpdateEnabled bool `json:"AutoSoftwareUpdateEnabled"`
}

type VPCOptions struct {
	AvailabilityZones []string `json:"AvailabilityZones"`
	SecurityGroupIDS  []string `json:"SecurityGroupIds"`
	SubnetIDS         []string `json:"SubnetIds"`
	VPCID             string   `json:"VPCId"`
}

type Node struct {
	AvailabilityZone  string `json:"AvailabilityZone"`
	InstanceType      string `json:"InstanceType"`
	NodeID            string `json:"NodeId"`
	NodeStatus        string `json:"NodeStatus"`
	NodeType          string `json:"NodeType"`
	StorageSize       string `json:"StorageSize"`
	StorageType       string `json:"StorageType"`
	StorageVolumeType string `json:"StorageVolumeType"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ESGraph, error) {
	original := &Es{}
	resource := &model.ESGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.Arn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "es", original.Instance.Arn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "es"
	resource.Name = original.Instance.DomainName

	resource.Status = lo.Ternary(strings.EqualFold(original.Instance.DomainProcessingStatus, "active"), "running", "stopped")
	resource.PrivateEndpoint = original.Instance.Endpoint
	resource.EngineVersion = original.Instance.EngineVersion
	// public prefix "search-", vpc prefix "vpc-",
	resource.PublicAllowed = strings.HasPrefix(original.Instance.Endpoint, "search-")

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VPCOptions.VPCID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet,
		lo.Map(original.Instance.VPCOptions.SubnetIDS, func(subnetID string, _ int) *model.SubnetGraph {
			return &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", subnetID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.SG = append(resource.SG,
		lo.Map(original.Instance.VPCOptions.SecurityGroupIDS, func(securityGroupID string, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "sg", securityGroupID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	resource.Node = lo.Map(original.Nodes, func(node Node, _ int) *model.ESNodeGraph {
		return &model.ESNodeGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "es-node", node.NodeID),
				TargetUID: resource.UID,
			},
			// aws has no kibana node
			Class: "es",
		}
	})

	return resource, nil
}
