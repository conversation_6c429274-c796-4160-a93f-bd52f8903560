package function

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "function"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lambda_function_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["function_list"] = append(resourceData["function_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, functionSchema, resourceData["function_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Function struct {
	Function FunctionClass `json:"function"`
}

type FunctionClass struct {
	Architectures              []string         `json:"Architectures"`
	CodeSha256                 string           `json:"CodeSha256"`
	CodeSize                   int64            `json:"CodeSize"`
	DeadLetterConfig           interface{}      `json:"DeadLetterConfig"`
	Description                string           `json:"Description"`
	Environment                interface{}      `json:"Environment"`
	EphemeralStorage           EphemeralStorage `json:"EphemeralStorage"`
	FileSystemConfigs          interface{}      `json:"FileSystemConfigs"`
	FunctionArn                string           `json:"FunctionArn"`
	FunctionName               string           `json:"FunctionName"`
	Handler                    string           `json:"Handler"`
	ImageConfigResponse        interface{}      `json:"ImageConfigResponse"`
	KMSKeyArn                  interface{}      `json:"KMSKeyArn"`
	LastModified               string           `json:"LastModified"`
	LastUpdateStatus           string           `json:"LastUpdateStatus"`
	LastUpdateStatusReason     interface{}      `json:"LastUpdateStatusReason"`
	LastUpdateStatusReasonCode string           `json:"LastUpdateStatusReasonCode"`
	Layers                     interface{}      `json:"Layers"`
	LoggingConfig              LoggingConfig    `json:"LoggingConfig"`
	MasterArn                  interface{}      `json:"MasterArn"`
	MemorySize                 int64            `json:"MemorySize"`
	PackageType                string           `json:"PackageType"`
	RevisionID                 string           `json:"RevisionId"`
	Role                       string           `json:"Role"`
	Runtime                    string           `json:"Runtime"`
	RuntimeVersionConfig       interface{}      `json:"RuntimeVersionConfig"`
	SigningJobArn              interface{}      `json:"SigningJobArn"`
	SigningProfileVersionArn   interface{}      `json:"SigningProfileVersionArn"`
	SnapStart                  SnapStart        `json:"SnapStart"`
	State                      string           `json:"State"`
	StateReason                interface{}      `json:"StateReason"`
	StateReasonCode            string           `json:"StateReasonCode"`
	Timeout                    int64            `json:"Timeout"`
	TracingConfig              TracingConfig    `json:"TracingConfig"`
	Version                    string           `json:"Version"`
	VpcConfig                  VpcConfig        `json:"VpcConfig"`
}

type EphemeralStorage struct {
	Size int64 `json:"Size"`
}

type LoggingConfig struct {
	ApplicationLogLevel string `json:"ApplicationLogLevel"`
	LogFormat           string `json:"LogFormat"`
	LogGroup            string `json:"LogGroup"`
	SystemLogLevel      string `json:"SystemLogLevel"`
}

type SnapStart struct {
	ApplyOn            string `json:"ApplyOn"`
	OptimizationStatus string `json:"OptimizationStatus"`
}

type TracingConfig struct {
	Mode string `json:"Mode"`
}

type VpcConfig struct {
	Ipv6AllowedForDualStack bool     `json:"Ipv6AllowedForDualStack"`
	SecurityGroupIDS        []string `json:"SecurityGroupIds"`
	SubnetIDS               []string `json:"SubnetIds"`
	VpcID                   string   `json:"VpcId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.FunctionGraph, error) {
	original := &Function{}
	resource := &model.FunctionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Function.FunctionArn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "function", original.Function.FunctionArn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "function"
	resource.Name = original.Function.FunctionName
	resource.Description = original.Function.Description

	resource.LogEnabled = original.Function.LoggingConfig.LogGroup != ""
	resource.LogPath = original.Function.LoggingConfig.LogGroup
	resource.Runtime = strings.ToLower(original.Function.Runtime)

	if original.Function.VpcConfig.VpcID != "" {
		resource.VPC = append(resource.VPC, &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", original.Function.VpcConfig.VpcID),
				TargetUID: resource.UID,
			},
		})
		resource.Subnet = append(resource.Subnet,
			lo.Map(original.Function.VpcConfig.SubnetIDS, func(e string, _ int) *model.SubnetGraph {
				return &model.SubnetGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "subnet", e),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
		resource.SG = append(resource.SG,
			lo.Map(original.Function.VpcConfig.SecurityGroupIDS, func(e string, _ int) *model.SGGraph {
				return &model.SGGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "security-group", e),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
	}

	return resource, nil
}
