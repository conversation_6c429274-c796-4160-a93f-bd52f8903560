package eip

import graph "AssetStandardizer/graph"

var (
	eipSchema = graph.NodeSchema{
		Label: "EIP",
		Properties: graph.NodeProperties{
			"uid":                {Name: "uid"},
			"provider":           {Name: "provider"},
			"original_id":        {Name: "original_id"},
			"transformed_object": {Name: "transformed_object"},
			"region":             {Name: "region"},
			"last_seen":          {Name: "last_seen"},
			"description":        {Name: "description"},
			"kind":               {Name: "kind"},
			"name":               {Name: "name"},
			"ip":                 {Name: "ip"},
			"status":             {Name: "status"},
			"bandwidth":          {Name: "bandwidth"},
			"isp":                {Name: "isp"},
		},
	}

	nodeEipProperties = graph.NodeProperties{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	}
	nodeEipRelationships = graph.OtherRelationships{
		graph.RelSchema{
			TargetNodeLabel: "EIP",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "ASSIGNED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	}

	eniEipSchema = graph.NodeSchema{
		Label:              "ENI",
		Properties:         nodeEipProperties,
		OtherRelationships: nodeEipRelationships,
	}

	natEipSchema = graph.NodeSchema{
		Label:              "NAT",
		Properties:         nodeEipProperties,
		OtherRelationships: nodeEipRelationships,
	}

	ecsEipSchema = graph.NodeSchema{
		Label:              "ECS",
		Properties:         nodeEipProperties,
		OtherRelationships: nodeEipRelationships,
	}

	lbEipSchema = graph.NodeSchema{
		Label:              "LB",
		Properties:         nodeEipProperties,
		OtherRelationships: nodeEipRelationships,
	}

	labelEipSchema = graph.NodeSchema{
		Label: "Label",
		Properties: graph.NodeProperties{
			"uid":          {Name: "uid"},
			"key":          {Name: "key"},
			"value":        {Name: "value"},
			"last_updated": {Name: "last_updated", SetInKwargs: true},
		},
		OtherRelationships: graph.OtherRelationships{
			graph.RelSchema{
				TargetNodeLabel: "EIP",
				TargetNodeMatcher: graph.TargetNodeMatcher{
					"uid": {Name: "target_uid"},
				},
				Direction: graph.INWARD,
				RelLabel:  "HAS_LABEL",
				Properties: graph.RelProperties{
					"last_updated": {Name: "last_updated", SetInKwargs: true},
				},
			},
		},
	}
)
