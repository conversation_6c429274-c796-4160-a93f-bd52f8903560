package eip

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eip"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_eip_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eip_list"] = append(resourceData["eip_list"], utils.GenParamsFromStruct(resource))
		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)
		resourceData["nat_list"] = append(resourceData["nat_list"],
			utils.GenParamsFromStructSlice(resource.NAT)...,
		)
		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}

	return resourceData, nil
}

func updateResources(eipData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eipSchema, eipData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniEipSchema, eipData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, natEipSchema, eipData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEipSchema, eipData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbEipSchema, eipData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEipSchema, eipData["label_list"], map[string]any{"last_updated": "test"})

}

type Eip struct {
	Eip EipClass `json:"eip"`
}

type EipClass struct {
	AllocationID            string      `json:"AllocationId"`
	AssociationID           string      `json:"AssociationId"`
	CarrierIP               interface{} `json:"CarrierIp"`
	CustomerOwnedIP         interface{} `json:"CustomerOwnedIp"`
	CustomerOwnedIpv4Pool   interface{} `json:"CustomerOwnedIpv4Pool"`
	Domain                  string      `json:"Domain"`
	InstanceID              string      `json:"InstanceId"`
	NetworkBorderGroup      string      `json:"NetworkBorderGroup"`
	NetworkInterfaceID      string      `json:"NetworkInterfaceId"`
	NetworkInterfaceOwnerID string      `json:"NetworkInterfaceOwnerId"`
	PrivateIPAddress        string      `json:"PrivateIpAddress"`
	PublicIP                string      `json:"PublicIp"`
	PublicIpv4Pool          string      `json:"PublicIpv4Pool"`
	Tags                    []Tag       `json:"Tags"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.EipGraph, error) {
	original := &Eip{}
	resource := &model.EipGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Eip.AllocationID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eip", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "eip"
	resource.OriginalLabels = lo.Map(original.Eip.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.IP = original.Eip.PublicIP
	resource.Status = lo.Ternary(original.Eip.AssociationID != "", "binded", "available")
	if original.Eip.InstanceID != "" {
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	}
	if original.Eip.NetworkInterfaceID != "" {
		resource.ENI = append(resource.ENI, &model.ENIGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eni", original.Eip.NetworkInterfaceID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
