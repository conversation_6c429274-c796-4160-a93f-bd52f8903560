package kms

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "kms"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "kms_key_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["kms_list"] = append(resourceData["kms_list"], utils.GenParamsFromStruct(resource))
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, kmsSchema, resourceData["kms_list"], map[string]any{"last_updated": "test"})
}

type KMS struct {
	KmsKey KmsKey `json:"kmsKey"`
}

type KmsKey struct {
	KeyID                       string      `json:"KeyId"`
	AWSAccountID                string      `json:"AWSAccountId"`
	Arn                         string      `json:"Arn"`
	CloudHSMClusterID           interface{} `json:"CloudHsmClusterId"`
	CreationDate                string      `json:"CreationDate"`
	CustomKeyStoreID            interface{} `json:"CustomKeyStoreId"`
	CustomerMasterKeySpec       string      `json:"CustomerMasterKeySpec"`
	DeletionDate                string      `json:"DeletionDate"`
	Description                 string      `json:"Description"`
	Enabled                     bool        `json:"Enabled"`
	EncryptionAlgorithms        []string    `json:"EncryptionAlgorithms"`
	ExpirationModel             string      `json:"ExpirationModel"`
	KeyAgreementAlgorithms      interface{} `json:"KeyAgreementAlgorithms"`
	KeyManager                  string      `json:"KeyManager"`
	KeySpec                     string      `json:"KeySpec"`
	KeyState                    string      `json:"KeyState"`
	KeyUsage                    string      `json:"KeyUsage"`
	MACAlgorithms               interface{} `json:"MacAlgorithms"`
	MultiRegion                 bool        `json:"MultiRegion"`
	MultiRegionConfiguration    interface{} `json:"MultiRegionConfiguration"`
	Origin                      string      `json:"Origin"`
	PendingDeletionWindowInDays *int        `json:"PendingDeletionWindowInDays"`
	SigningAlgorithms           interface{} `json:"SigningAlgorithms"`
	ValidTo                     *int        `json:"ValidTo"`
	XksKeyConfiguration         interface{} `json:"XksKeyConfiguration"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.KMSGraph, error) {
	original := &KMS{}
	resource := &model.KMSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.KmsKey.Arn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "kms", original.KmsKey.Arn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "kms"
	resource.Name = original.KmsKey.KeyID
	resource.Description = original.KmsKey.Description

	resource.Enabled = original.KmsKey.Enabled

	var t time.Time
	t, _ = time.Parse(time.RFC3339, original.KmsKey.CreationDate)
	resource.CreatedAt = lo.Ternary(t.UnixMilli() > 0, t.UnixMilli(), -1)
	t, _ = time.Parse(time.RFC3339, original.KmsKey.DeletionDate)
	resource.DeletedAt = lo.Ternary(t.UnixMilli() > 0, t.UnixMilli(), -1)
	if original.KmsKey.PendingDeletionWindowInDays != nil {
		resource.PendingDeletionWindowInDays = *original.KmsKey.PendingDeletionWindowInDays
	} else {
		resource.PendingDeletionWindowInDays = -1
	}

	return resource, nil
}
