package kms

import "AssetStandardizer/graph"

var kmsSchema = graph.NodeSchema{
	Label: "KMS",
	Properties: map[string]graph.PropertyRef{
		"uid":                             {Name: "uid"},
		"provider":                        {Name: "provider"},
		"original_id":                     {Name: "original_id"},
		"transformed_object":              {Name: "transformed_object"},
		"region":                          {Name: "region"},
		"last_seen":                       {Name: "last_seen"},
		"description":                     {Name: "description"},
		"kind":                            {Name: "kind"},
		"name":                            {Name: "name"},
		"enabled":                         {Name: "enabled"},
		"created_at":                      {Name: "created_at"},
		"deleted_at":                      {Name: "deleted_at"},
		"pending_deletion_window_in_days": {Name: "pending_deletion_window_in_days"},
	},
}
