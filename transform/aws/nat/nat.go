package nat

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nat"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "nat_gateway_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nat_list"] = append(resourceData["nat_list"], utils.GenParamsFromStruct(resource))

		resourceData["snat_rule_list"] = append(resourceData["snat_rule_list"],
			utils.GenParamsFromStructSlice(resource.SNATRules)...,
		)
		resourceData["ip_range_list"] = append(resourceData["ip_range_list"],
			lo.FlatMap(resource.SNATRules, func(snatRule *model.SNATRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(snatRule.IPRange)
			})...,
		)

		resourceData["dnat_rule_list"] = append(resourceData["dnat_rule_list"],
			utils.GenParamsFromStructSlice(resource.DNATRules)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, natSchema, resourceData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, snatRuleSchema, resourceData["snat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dnatRuleSchema, resourceData["dnat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipRangeSchema, resourceData["ip_range_list"], map[string]any{"last_updated": "test"})
}

type NAT struct {
	NatGateway NatGateway `json:"natGateway"`
}

type NatGateway struct {
	ConnectivityType     string              `json:"ConnectivityType"`
	CreateTime           string              `json:"CreateTime"`
	DeleteTime           interface{}         `json:"DeleteTime"`
	FailureCode          interface{}         `json:"FailureCode"`
	FailureMessage       interface{}         `json:"FailureMessage"`
	NatGatewayAddresses  []NatGatewayAddress `json:"NatGatewayAddresses"`
	NatGatewayID         string              `json:"NatGatewayId"`
	ProvisionedBandwidth interface{}         `json:"ProvisionedBandwidth"`
	State                string              `json:"State"`
	SubnetID             string              `json:"SubnetId"`
	Tags                 []Tags              `json:"Tags"`
	VpcID                string              `json:"VpcId"`
}

type NatGatewayAddress struct {
	AllocationID       string      `json:"AllocationId"`
	AssociationID      *string     `json:"AssociationId"`
	FailureMessage     interface{} `json:"FailureMessage"`
	IsPrimary          bool        `json:"IsPrimary"`
	NetworkInterfaceID string      `json:"NetworkInterfaceId"`
	PrivateIP          string      `json:"PrivateIp"`
	PublicIP           string      `json:"PublicIp"`
	Status             string      `json:"Status"`
}

type Tags struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NATGraph, error) {
	original := &NAT{}
	resource := &model.NATGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NatGateway.NatGatewayID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nat", original.NatGateway.NatGatewayID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nat"
	resource.OriginalLabels = lo.Map(original.NatGateway.Tags,
		func(e Tags, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.Status = strings.ToLower(original.NatGateway.State)
	resource.Spec = original.NatGateway.ConnectivityType

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.NatGateway.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.NatGateway.SubnetID),
			TargetUID: resource.UID,
		},
	})
	resource.EIP = append(resource.EIP,
		lo.FilterMap(original.NatGateway.NatGatewayAddresses, func(ip NatGatewayAddress, _ int) (*model.EipGraph, bool) {
			if ip.PublicIP == "" || ip.AllocationID == "" {
				return nil, false
			}
			return &model.EipGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "eip", ip.AllocationID),
					TargetUID: resource.UID,
				},
			}, true
		})...,
	)

	return resource, nil
}
