package cen

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cen"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_tgw_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cen_list"] = append(resourceData["cen_list"], utils.GenParamsFromStruct(resource))

		resourceData["cen_route_entry_list"] = append(resourceData["cen_route_entry_list"],
			utils.GenParamsFromStructSlice(resource.CenRouteEntry)...,
		)

		for _, attachedInstance := range resource.AttachedInstance {
			instanceType := attachedInstance.InstanceType
			resourceData[instanceType] = append(resourceData[instanceType], utils.GenParamsFromStruct(attachedInstance))
		}

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cenSchema, resourceData["cen_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, cenRouteEntrySchema, resourceData["cen_route_entry_list"], map[string]any{"last_updated": "test"})

	for key, instances := range resourceData {
		if strings.HasSuffix(key, "_list") {
			continue
		}

		instanceSchema := cenAttachedInstanceSchema
		instanceSchema.OtherRelationships = append(instanceSchema.OtherRelationships, relToInstance(key)...)
		graph.Run(n4jSession, instanceSchema, instances, map[string]any{"last_updated": "test"})
	}
}

type Cen struct {
	RouteTables []RouteTable `json:"routeTables"`
	Tgw         Tgw          `json:"tgw"`
}

type RouteTable struct {
	RouteTable   RouteTableClass         `json:"route_table"`
	Associations []RouteTableAssociation `json:"associations"`
	Routes       []Route                 `json:"routes"`
}

type RouteTableAssociation struct {
	ResourceID                 string `json:"ResourceId"`
	ResourceType               string `json:"ResourceType"`
	State                      string `json:"State"`
	TransitGatewayAttachmentID string `json:"TransitGatewayAttachmentId"`
}

type RouteTableClass struct {
	CreationTime                 string `json:"CreationTime"`
	DefaultAssociationRouteTable bool   `json:"DefaultAssociationRouteTable"`
	DefaultPropagationRouteTable bool   `json:"DefaultPropagationRouteTable"`
	State                        string `json:"State"`
	Tags                         []Tag  `json:"Tags"`
	TransitGatewayID             string `json:"TransitGatewayId"`
	TransitGatewayRouteTableID   string `json:"TransitGatewayRouteTableId"`
}

type Route struct {
	DestinationCIDRBlock                   string                     `json:"DestinationCidrBlock"`
	PrefixListID                           interface{}                `json:"PrefixListId"`
	State                                  string                     `json:"State"`
	TransitGatewayAttachments              []TransitGatewayAttachment `json:"TransitGatewayAttachments"`
	TransitGatewayRouteTableAnnouncementID string                     `json:"TransitGatewayRouteTableAnnouncementId"`
	Type                                   string                     `json:"Type"`
}

type TransitGatewayAttachment struct {
	ResourceID                 string `json:"ResourceId"`
	ResourceType               string `json:"ResourceType"`
	TransitGatewayAttachmentID string `json:"TransitGatewayAttachmentId"`
}

type Tgw struct {
	CreationTime      string     `json:"CreationTime"`
	Description       string     `json:"Description"`
	Options           TgwOptions `json:"Options"`
	OwnerID           string     `json:"OwnerId"`
	State             string     `json:"State"`
	Tags              []Tag      `json:"Tags"`
	TransitGatewayArn string     `json:"TransitGatewayArn"`
	TransitGatewayID  string     `json:"TransitGatewayId"`
}

type TgwOptions struct {
	AmazonSideAsn                   int64  `json:"AmazonSideAsn"`
	AssociationDefaultRouteTableID  string `json:"AssociationDefaultRouteTableId"`
	AutoAcceptSharedAttachments     string `json:"AutoAcceptSharedAttachments"`
	DefaultRouteTableAssociation    string `json:"DefaultRouteTableAssociation"`
	DefaultRouteTablePropagation    string `json:"DefaultRouteTablePropagation"`
	DNSSupport                      string `json:"DnsSupport"`
	MulticastSupport                string `json:"MulticastSupport"`
	PropagationDefaultRouteTableID  string `json:"PropagationDefaultRouteTableId"`
	SecurityGroupReferencingSupport string `json:"SecurityGroupReferencingSupport"`
	TransitGatewayCIDRBlocks        any    `json:"TransitGatewayCidrBlocks"`
	VPNEcmpSupport                  string `json:"VpnEcmpSupport"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CenGraph, error) {
	original := &Cen{}
	resource := &model.CenGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Tgw.TransitGatewayID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "cen", original.Tgw.TransitGatewayID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cen"
	resource.OriginalLabels = lo.Map(original.Tgw.Tags, func(e Tag, _ int) *model.KVGraph {
		return &model.KVGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, "tag", e.Key+":"+e.Value),
				TargetUID: resource.UID,
			},
			Key:   e.Key,
			Value: e.Value,
		}
	})
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.Status = lo.Ternary(strings.EqualFold(original.Tgw.State, "active"), "running", "stopped")
	resource.AttachedInstance = lo.Flatten(
		lo.FlatMap(original.RouteTables, func(rt RouteTable, _ int) [][]*model.CenAttachedInstanceGraph {
			instanceSlices := lo.Map(rt.Routes, func(r Route, _ int) []*model.CenAttachedInstanceGraph {
				return lo.Map(r.TransitGatewayAttachments, func(attachment TransitGatewayAttachment, _ int) *model.CenAttachedInstanceGraph {
					normalizedType := normalizeInstanceType(attachment.ResourceType)
					return &model.CenAttachedInstanceGraph{
						BaseNode: model.BaseNode{
							UID:       utils.GenerateUID(provider_utils.ProviderID, normalizedType, attachment.ResourceID),
							TargetUID: resource.UID,
						},
						InstanceID:   attachment.ResourceID,
						InstanceType: normalizedType,
					}
				})
			})

			instanceSlices = append(instanceSlices, lo.Map(rt.Associations, func(association RouteTableAssociation, _ int) *model.CenAttachedInstanceGraph {
				normalizedType := normalizeInstanceType(association.ResourceType)
				return &model.CenAttachedInstanceGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(provider_utils.ProviderID, normalizedType, association.ResourceID),
						TargetUID: resource.UID,
					},
					InstanceID:   association.ResourceID,
					InstanceType: strings.ToLower(association.ResourceType),
				}
			}))
			return instanceSlices
		}),
	)
	resource.AttachedInstance = lo.UniqBy(resource.AttachedInstance, func(e *model.CenAttachedInstanceGraph) string { return e.UID })

	resource.CenRouteEntry = lo.Flatten(
		lo.FlatMap(original.RouteTables, func(rt RouteTable, _ int) [][]*model.CenRouteEntryGraph {
			return lo.Map(rt.Routes, func(r Route, _ int) []*model.CenRouteEntryGraph {
				return lo.FlatMap(rt.Associations, func(src RouteTableAssociation, _ int) []*model.CenRouteEntryGraph {
					return lo.Map(r.TransitGatewayAttachments, func(dst TransitGatewayAttachment, _ int) *model.CenRouteEntryGraph {
						return &model.CenRouteEntryGraph{
							BaseNode: model.BaseNode{
								UID:       utils.GenerateUID(provider_utils.ProviderID, "cen-route-entry", fmt.Sprintf("%s-%s-%s", src.ResourceID, dst.ResourceID, r.DestinationCIDRBlock)),
								TargetUID: resource.UID,
							},
							SrcInstanceID:   src.ResourceID,
							SrcInstanceUID:  utils.GenerateUID(provider_utils.ProviderID, normalizeInstanceType(src.ResourceType), src.ResourceID),
							NextInstanceID:  dst.ResourceID,
							NextInstanceUID: utils.GenerateUID(provider_utils.ProviderID, normalizeInstanceType(dst.ResourceType), dst.ResourceID),
							DstCidrBlock:    r.DestinationCIDRBlock,
							Status:          lo.Ternary(strings.EqualFold(r.State, "active"), "active", "inactive"),
						}
					})
				})

			})
		}),
	)
	resource.CenRouteEntry = lo.UniqBy(resource.CenRouteEntry, func(e *model.CenRouteEntryGraph) string { return e.UID })

	return resource, nil
}

func normalizeInstanceType(rawType string) (normalizedType string) {
	normalizedType = strings.ToLower(rawType)
	switch normalizedType {
	case "peering":
		normalizedType = "cen"
	}
	return normalizedType
}
