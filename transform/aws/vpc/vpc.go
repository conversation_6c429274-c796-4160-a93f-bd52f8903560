package vpc

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "vpc"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_vpc_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["vpc_list"] = append(resourceData["vpc_list"], utils.GenParamsFromStruct(resource))

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type Vpc struct {
	Subnets []VpcSubnet `json:"subnets"`
	Vpc     VpcClass    `json:"vpc"`
}

type VpcSubnet struct {
	AssignIpv6AddressOnCreation   bool                          `json:"AssignIpv6AddressOnCreation"`
	AvailabilityZone              string                        `json:"AvailabilityZone"`
	AvailabilityZoneID            string                        `json:"AvailabilityZoneId"`
	AvailableIPAddressCount       int64                         `json:"AvailableIpAddressCount"`
	CIDRBlock                     string                        `json:"CidrBlock"`
	CustomerOwnedIpv4Pool         interface{}                   `json:"CustomerOwnedIpv4Pool"`
	DefaultForAz                  bool                          `json:"DefaultForAz"`
	EnableDns64                   bool                          `json:"EnableDns64"`
	EnableLniAtDeviceIndex        interface{}                   `json:"EnableLniAtDeviceIndex"`
	Ipv6CIDRBlockAssociationSet   []interface{}                 `json:"Ipv6CidrBlockAssociationSet"`
	Ipv6Native                    bool                          `json:"Ipv6Native"`
	MapCustomerOwnedIPOnLaunch    interface{}                   `json:"MapCustomerOwnedIpOnLaunch"`
	MapPublicIPOnLaunch           bool                          `json:"MapPublicIpOnLaunch"`
	OutpostArn                    interface{}                   `json:"OutpostArn"`
	OwnerID                       string                        `json:"OwnerId"`
	PrivateDNSNameOptionsOnLaunch PrivateDNSNameOptionsOnLaunch `json:"PrivateDnsNameOptionsOnLaunch"`
	State                         string                        `json:"State"`
	SubnetArn                     string                        `json:"SubnetArn"`
	SubnetID                      string                        `json:"SubnetId"`
	Tags                          []Tag                         `json:"Tags"`
	VpcID                         string                        `json:"VpcId"`
}

type PrivateDNSNameOptionsOnLaunch struct {
	EnableResourceNameDNSAAAARecord bool   `json:"EnableResourceNameDnsAAAARecord"`
	EnableResourceNameDNSARecord    bool   `json:"EnableResourceNameDnsARecord"`
	HostnameType                    string `json:"HostnameType"`
}

type VpcClass struct {
	CIDRBlock                   string                        `json:"CidrBlock"`
	CIDRBlockAssociationSet     []CIDRBlockAssociationSet     `json:"CidrBlockAssociationSet"`
	DHCPOptionsID               string                        `json:"DhcpOptionsId"`
	InstanceTenancy             string                        `json:"InstanceTenancy"`
	Ipv6CIDRBlockAssociationSet []Ipv6CIDRBlockAssociationSet `json:"Ipv6CidrBlockAssociationSet"`
	IsDefault                   bool                          `json:"IsDefault"`
	OwnerID                     string                        `json:"OwnerId"`
	State                       string                        `json:"State"`
	Tags                        []Tag                         `json:"Tags"`
	VpcID                       string                        `json:"VpcId"`
}

type CIDRBlockAssociationSet struct {
	AssociationID  string         `json:"AssociationId"`
	CIDRBlock      string         `json:"CidrBlock"`
	CIDRBlockState CIDRBlockState `json:"CidrBlockState"`
}

type CIDRBlockState struct {
	State         string      `json:"State"`
	StatusMessage interface{} `json:"StatusMessage"`
}

type Ipv6CIDRBlockAssociationSet struct {
	Ipv6CIDRBlock string `json:"ipv6CidrBlock"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.VPCGraph, error) {
	original := &Vpc{}
	resource := &model.VPCGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Vpc.VpcID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "vpc", original.Vpc.VpcID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "vpc"
	resource.OriginalLabels = lo.Map(original.Vpc.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.CIDR = original.Vpc.CIDRBlock
	resource.IsDefault = original.Vpc.IsDefault
	resource.SecondaryCIDRs = append(resource.SecondaryCIDRs,
		lo.Map(original.Vpc.Ipv6CIDRBlockAssociationSet, func(e Ipv6CIDRBlockAssociationSet, _ int) string { return e.Ipv6CIDRBlock })...,
	)
	resource.SecondaryCIDRs = append(resource.SecondaryCIDRs,
		lo.FilterMap(original.Vpc.CIDRBlockAssociationSet, func(e CIDRBlockAssociationSet, _ int) (string, bool) {
			return e.CIDRBlock, e.CIDRBlock != original.Vpc.CIDRBlock
		})...,
	)
	resource.Subnet = lo.Map(original.Subnets, func(subnet VpcSubnet, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", subnet.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
