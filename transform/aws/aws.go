package aws

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/aws/ak"
	"AssetStandardizer/transform/aws/audit_log"
	"AssetStandardizer/transform/aws/bucket"
	"AssetStandardizer/transform/aws/cen"
	"AssetStandardizer/transform/aws/ebs"
	"AssetStandardizer/transform/aws/ecs"
	"AssetStandardizer/transform/aws/eip"
	"AssetStandardizer/transform/aws/elasticsearch"
	"AssetStandardizer/transform/aws/eni"
	"AssetStandardizer/transform/aws/function"
	"AssetStandardizer/transform/aws/k8s"
	"AssetStandardizer/transform/aws/lb"
	"AssetStandardizer/transform/aws/lb_listener"
	"AssetStandardizer/transform/aws/nas"
	"AssetStandardizer/transform/aws/nat"
	"AssetStandardizer/transform/aws/peer_connection"
	"AssetStandardizer/transform/aws/policy"
	"AssetStandardizer/transform/aws/rds"
	"AssetStandardizer/transform/aws/role"
	"AssetStandardizer/transform/aws/security_group"
	"AssetStandardizer/transform/aws/subnet"
	"AssetStandardizer/transform/aws/user"
	"AssetStandardizer/transform/aws/vpc"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

const ProviderID = "aws"

var AssetMessageChan = make(chan *model.AssetMessage, 100)
var AssetTypeMap = map[string]chan *model.AssetMessage{}

func init() {
	resourceServices := []model.ResourceService{
		eip.NewResourceService(),
		ak.NewResourceService(),
		user.NewResourceService(),
		policy.NewResourceService(),
		role.NewResourceService(),
		ecs.NewResourceService(),
		security_group.NewResourceService(),
		vpc.NewResourceService(),
		subnet.NewResourceService(),
		lb.NewLBService(),
		lb_listener.NewLBListenerService(),
		eni.NewResourceService(),
		nat.NewResourceService(),
		bucket.NewResourceService(),
		peer_connection.NewResourceService(),
		ebs.NewVolumeService(),
		ebs.NewImageService(),
		ebs.NewSnapshotService(),
		cen.NewResourceService(),
		rds.NewResourceService(),
		elasticsearch.NewResourceService(),
		function.NewResourceService(),
		nas.NewResourceService(),
		k8s.NewResourceService(),
		audit_log.NewResourceService(),
	}

	for _, resourceService := range resourceServices {
		if err := resourceService.Validate(); err != nil {
			logger.Errorf("validate resource service failed: %s", err)
			continue
		}
		resourceService.Start()
		AssetTypeMap[resourceService.GetMessageType()] = resourceService.GetAssetMsgCh()
	}
	go dispatch()
}

func dispatch() {
	for assetMsg := range AssetMessageChan {
		if assetMsgChan, ok := AssetTypeMap[assetMsg.Type]; ok {
			assetMsgChan <- assetMsg
		} else {
			logger.Errorf("unknown type: %s", assetMsg.Type)
			continue
		}
	}
}
