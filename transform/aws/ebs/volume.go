package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var volumeLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "volume"})

func NewVolumeService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ec2_disk_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformVolume,
		UpdateResources: updateVolumes,
	}
}

func transformVolume(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseVolume(assetMsg)
		if err != nil {
			volumeLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["volume_list"] = append(resourceData["volume_list"], utils.GenParamsFromStruct(resource))

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["image_list"] = append(resourceData["image_list"],
			utils.GenParamsFromStructSlice(resource.Image)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateVolumes(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, volumeSchema, resourceData["volume_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsVolumeSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
}

type Disk struct {
	Disk DiskClass `json:"disk"`
}

type DiskClass struct {
	Attachments        []Attachment `json:"Attachments"`
	AvailabilityZone   string       `json:"AvailabilityZone"`
	CreateTime         string       `json:"CreateTime"`
	Encrypted          bool         `json:"Encrypted"`
	FastRestored       interface{}  `json:"FastRestored"`
	Iops               int64        `json:"Iops"`
	KmsKeyID           interface{}  `json:"KmsKeyId"`
	MultiAttachEnabled bool         `json:"MultiAttachEnabled"`
	OutpostArn         interface{}  `json:"OutpostArn"`
	Size               int64        `json:"Size"`
	SnapshotID         string       `json:"SnapshotId"`
	SSEType            string       `json:"SseType"`
	State              string       `json:"State"`
	Tags               []Tag        `json:"Tags"`
	Throughput         int64        `json:"Throughput"`
	VolumeID           string       `json:"VolumeId"`
	VolumeType         string       `json:"VolumeType"`
}

type Attachment struct {
	AttachTime          string      `json:"AttachTime"`
	AttachmentID        string      `json:"AttachmentId"`
	InstanceID          string      `json:"InstanceId"`
	DeleteOnTermination bool        `json:"DeleteOnTermination"`
	DeviceIndex         int64       `json:"DeviceIndex"`
	EnaSrdSpecification interface{} `json:"EnaSrdSpecification"`
	NetworkCardIndex    int64       `json:"NetworkCardIndex"`
	Status              string      `json:"Status"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseVolume(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Disk{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Disk.VolumeID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Disk.VolumeID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Disk.VolumeID
	resource.OriginalLabels = lo.Map(original.Disk.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.Class = "volume"
	resource.Status = lo.Ternary(strings.EqualFold(original.Disk.State, "in_use"), "inuse", "available")
	resource.Encrypted = original.Disk.Encrypted
	resource.ECS = lo.Map(original.Disk.Attachments,
		func(e Attachment, _ int) *model.ECSGraph {
			return &model.ECSGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "ecs", e.InstanceID),
					TargetUID: resource.UID,
				},
			}
		},
	)

	return resource, nil
}
