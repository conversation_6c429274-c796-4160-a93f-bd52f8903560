package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var imageLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "image"})

func NewImageService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ec2_image_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformImage,
		UpdateResources: updateImages,
	}
}

func transformImage(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseImage(assetMsg)
		if err != nil {
			imageLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["image_list"] = append(resourceData["image_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateImages(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Image struct {
	Image ImageClass `json:"image"`
}

type ImageClass struct {
	Architecture             string               `json:"Architecture"`
	BlockDeviceMappings      []BlockDeviceMapping `json:"BlockDeviceMappings"`
	BootMode                 string               `json:"BootMode"`
	CreationDate             string               `json:"CreationDate"`
	DeprecationTime          string               `json:"DeprecationTime"`
	DeregistrationProtection interface{}          `json:"DeregistrationProtection"`
	Description              string               `json:"Description"`
	EnaSupport               bool                 `json:"EnaSupport"`
	Hypervisor               string               `json:"Hypervisor"`
	ImageID                  string               `json:"ImageId"`
	ImageLocation            string               `json:"ImageLocation"`
	ImageOwnerAlias          string               `json:"ImageOwnerAlias"`
	ImageType                string               `json:"ImageType"`
	ImdsSupport              string               `json:"ImdsSupport"`
	KernelID                 interface{}          `json:"KernelId"`
	LastLaunchedTime         interface{}          `json:"LastLaunchedTime"`
	Name                     string               `json:"Name"`
	OwnerID                  string               `json:"OwnerId"`
	Platform                 string               `json:"Platform"`
	PlatformDetails          string               `json:"PlatformDetails"`
	ProductCodes             interface{}          `json:"ProductCodes"`
	Public                   bool                 `json:"Public"`
	RamdiskID                interface{}          `json:"RamdiskId"`
	RootDeviceName           string               `json:"RootDeviceName"`
	RootDeviceType           string               `json:"RootDeviceType"`
	SourceInstanceID         interface{}          `json:"SourceInstanceId"`
	SriovNetSupport          string               `json:"SriovNetSupport"`
	State                    string               `json:"State"`
	StateReason              interface{}          `json:"StateReason"`
	Tags                     []Tag                `json:"Tags"`
	TPMSupport               string               `json:"TpmSupport"`
	UsageOperation           string               `json:"UsageOperation"`
	VirtualizationType       string               `json:"VirtualizationType"`
}

type BlockDeviceMapping struct {
	DeviceName  string      `json:"DeviceName"`
	Ebs         EBSEbs      `json:"Ebs"`
	NoDevice    interface{} `json:"NoDevice"`
	VirtualName interface{} `json:"VirtualName"`
}

type EBSEbs struct {
	DeleteOnTermination bool        `json:"DeleteOnTermination"`
	Encrypted           bool        `json:"Encrypted"`
	Iops                interface{} `json:"Iops"`
	KmsKeyID            interface{} `json:"KmsKeyId"`
	OutpostArn          interface{} `json:"OutpostArn"`
	SnapshotID          string      `json:"SnapshotId"`
	Throughput          interface{} `json:"Throughput"`
	VolumeSize          int64       `json:"VolumeSize"`
	VolumeType          string      `json:"VolumeType"`
}

func parseImage(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Image{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Image.ImageID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Image.ImageID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Image.Name
	resource.Description = original.Image.Description
	resource.OriginalLabels = lo.Map(original.Image.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.Class = "image"
	resource.Encrypted = false
	resource.Status = lo.Ternary(strings.EqualFold(original.Image.State, "Available"), "available", "unavailable")
	// encrypted if all block device mappings are encrypted
	notEnc, _ := lo.Find(original.Image.BlockDeviceMappings, func(e BlockDeviceMapping) bool { return !e.Ebs.Encrypted })
	resource.Encrypted = notEnc.DeviceName == ""

	return resource, nil
}
