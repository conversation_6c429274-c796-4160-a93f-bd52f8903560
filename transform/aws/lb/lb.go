package lb

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var lbLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "lb"})

func NewLBService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_lb_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformLB,
		UpdateResources: updateLBResources,
	}
}

func transformLB(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseLB(assetMsg)
		if err != nil {
			lbLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_list"] = append(resourceData["lb_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		for _, server := range resource.Servers {
			serverData := utils.GenParamsFromStruct(server)
			switch server.Class {
			case "ecs":
				resourceData["ecs_list"] = append(resourceData["ecs_list"], serverData)
			case "eni":
				resourceData["eni_list"] = append(resourceData["eni_list"], serverData)
			case "ip":
				resourceData["ip_list"] = append(resourceData["ip_list"], serverData)
			}
		}
	}
	return resourceData, nil
}

func updateLBResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelLbSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcLbSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetLbSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgLbSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsLbSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniLbSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipLbSchema, resourceData["ip_list"], map[string]any{"last_updated": "test"})
}

type LB struct {
	LB         LBClass       `json:"lb"`
	Targets    []Target      `json:"targets"`
	Attributes []LBAttribute `json:"attributes"`
}

type LBClass struct {
	AvailabilityZones                                    []AvailabilityZone `json:"AvailabilityZones"`
	CanonicalHostedZoneID                                interface{}        `json:"CanonicalHostedZoneId"`
	CreatedTime                                          string             `json:"CreatedTime"`
	CustomerOwnedIpv4Pool                                interface{}        `json:"CustomerOwnedIpv4Pool"`
	DNSName                                              interface{}        `json:"DNSName"`
	EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic interface{}        `json:"EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic"`
	IPAddressType                                        string             `json:"IpAddressType"`
	LoadBalancerArn                                      string             `json:"LoadBalancerArn"`
	LoadBalancerName                                     string             `json:"LoadBalancerName"`
	Scheme                                               string             `json:"Scheme"`
	SecurityGroups                                       []string           `json:"SecurityGroups"`
	State                                                LbState            `json:"State"`
	Type                                                 string             `json:"Type"`
	VpcID                                                string             `json:"VpcId"`
}

type LBAttribute struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

type AvailabilityZone struct {
	LoadBalancerAddresses interface{} `json:"LoadBalancerAddresses"`
	OutpostID             interface{} `json:"OutpostId"`
	SubnetID              string      `json:"SubnetId"`
	ZoneName              string      `json:"ZoneName"`
}

type LbState struct {
	Code   string      `json:"Code"`
	Reason interface{} `json:"Reason"`
}

type Target struct {
	ID               string `json:"Id"`
	AvailabilityZone string `json:"AvailabilityZone"`
	Port             int    `json:"Port"`
}

func parseLB(assetMsg *model.AssetMessage) (*model.LBGraph, error) {
	original := &LB{}
	resource := &model.LBGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.LB.LoadBalancerArn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb", original.LB.LoadBalancerArn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb"
	resource.Name = original.LB.LoadBalancerName

	switch original.LB.Type {
	case "application":
		resource.Class = "application"
	case "gateway":
		resource.Class = "network"
	case "network":
		resource.Class = "network"
	case "classic":
		resource.Class = "mixed"
	}
	resource.Status = lo.Ternary(strings.EqualFold(original.LB.State.Code, "active"), "active", "inactive")
	v, found := lo.Find(original.Attributes, func(e LBAttribute) bool {
		return e.Key == "deletion_protection.enabled"
	})
	if found {
		resource.DeleteProtection = lo.ToPtr(strings.EqualFold(v.Value, "true"))
	} else {
		resource.DeleteProtection = lo.ToPtr(false)
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.LB.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet,
		lo.Map(original.LB.AvailabilityZones, func(e AvailabilityZone, _ int) *model.SubnetGraph {
			return &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", e.SubnetID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.SG = append(resource.SG,
		lo.Map(original.LB.SecurityGroups, func(e string, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	resource.Servers = append(resource.Servers,
		lo.FilterMap(original.Targets, func(e Target, _ int) (*model.LbServerGraph, bool) {
			var class, originalID, ip string
			if strings.HasPrefix(e.ID, "i-") {
				class = "ecs"
				originalID = e.ID
			} else if strings.Contains(e.ID, ".") {
				class = "ip"
				ip = e.ID
			} else if strings.HasPrefix(e.ID, "arn:aws-cn:elasticloadbalancing") {
				class = "lb"
				originalID = e.ID
			} else {
				return nil, false
			}

			return &model.LbServerGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, class, lo.CoalesceOrEmpty(originalID, ip)),
					TargetUID: resource.UID,
				},
				Class: class,
				IP:    ip,
				Port:  e.Port,
			}, true
		})...,
	)

	return resource, nil
}
