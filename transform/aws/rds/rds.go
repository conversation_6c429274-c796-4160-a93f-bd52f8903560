package rds

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "rds_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["rds_list"] = append(resourceData["rds_list"], utils.GenParamsFromStruct(resource))

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, rdsSchema, resourceData["rds_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, rdsAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type Rds struct {
	Instance     Instance      `json:"instance"`
	IPWhiteList  []IPWhiteList `json:"ipWhiteList"`
	BackupList   []BackupList  `json:"backupList"`
	LogFileExist bool          `json:"logFileExist"`
}

type Instance struct {
	ActivityStreamEngineNativeAuditFieldsIncluded interface{}             `json:"ActivityStreamEngineNativeAuditFieldsIncluded"`
	ActivityStreamKinesisStreamName               interface{}             `json:"ActivityStreamKinesisStreamName"`
	ActivityStreamKmsKeyID                        interface{}             `json:"ActivityStreamKmsKeyId"`
	ActivityStreamMode                            string                  `json:"ActivityStreamMode"`
	ActivityStreamPolicyStatus                    string                  `json:"ActivityStreamPolicyStatus"`
	ActivityStreamStatus                          string                  `json:"ActivityStreamStatus"`
	AllocatedStorage                              int64                   `json:"AllocatedStorage"`
	AssociatedRoles                               []interface{}           `json:"AssociatedRoles"`
	AutoMinorVersionUpgrade                       bool                    `json:"AutoMinorVersionUpgrade"`
	AutomaticRestartTime                          interface{}             `json:"AutomaticRestartTime"`
	AutomationMode                                string                  `json:"AutomationMode"`
	AvailabilityZone                              string                  `json:"AvailabilityZone"`
	AwsBackupRecoveryPointArn                     interface{}             `json:"AwsBackupRecoveryPointArn"`
	BackupRetentionPeriod                         int64                   `json:"BackupRetentionPeriod"`
	BackupTarget                                  string                  `json:"BackupTarget"`
	CACertificateIdentifier                       string                  `json:"CACertificateIdentifier"`
	CertificateDetails                            CertificateDetails      `json:"CertificateDetails"`
	CharacterSetName                              interface{}             `json:"CharacterSetName"`
	CopyTagsToSnapshot                            bool                    `json:"CopyTagsToSnapshot"`
	CustomIamInstanceProfile                      interface{}             `json:"CustomIamInstanceProfile"`
	CustomerOwnedIPEnabled                        bool                    `json:"CustomerOwnedIpEnabled"`
	DBClusterIdentifier                           string                  `json:"DBClusterIdentifier"`
	DBInstanceArn                                 string                  `json:"DBInstanceArn"`
	DBInstanceAutomatedBackupsReplications        interface{}             `json:"DBInstanceAutomatedBackupsReplications"`
	DBInstanceClass                               string                  `json:"DBInstanceClass"`
	DBInstanceIdentifier                          string                  `json:"DBInstanceIdentifier"`
	DBInstanceStatus                              string                  `json:"DBInstanceStatus"`
	DBName                                        interface{}             `json:"DBName"`
	DBParameterGroups                             []DBParameterGroup      `json:"DBParameterGroups"`
	DBSecurityGroups                              []interface{}           `json:"DBSecurityGroups"`
	DBSubnetGroup                                 DBSubnetGroup           `json:"DBSubnetGroup"`
	DBSystemID                                    interface{}             `json:"DBSystemId"`
	DBInstancePort                                int64                   `json:"DbInstancePort"`
	DbiResourceID                                 string                  `json:"DbiResourceId"`
	DedicatedLogVolume                            bool                    `json:"DedicatedLogVolume"`
	DeletionProtection                            bool                    `json:"DeletionProtection"`
	DomainMemberships                             []interface{}           `json:"DomainMemberships"`
	EnabledCloudwatchLogsExports                  interface{}             `json:"EnabledCloudwatchLogsExports"`
	Endpoint                                      Endpoint                `json:"Endpoint"`
	Engine                                        string                  `json:"Engine"`
	EngineLifecycleSupport                        interface{}             `json:"EngineLifecycleSupport"`
	EngineVersion                                 string                  `json:"EngineVersion"`
	EnhancedMonitoringResourceArn                 interface{}             `json:"EnhancedMonitoringResourceArn"`
	IAMDatabaseAuthenticationEnabled              bool                    `json:"IAMDatabaseAuthenticationEnabled"`
	InstanceCreateTime                            string                  `json:"InstanceCreateTime"`
	Iops                                          interface{}             `json:"Iops"`
	IsStorageConfigUpgradeAvailable               interface{}             `json:"IsStorageConfigUpgradeAvailable"`
	KmsKeyID                                      interface{}             `json:"KmsKeyId"`
	LatestRestorableTime                          interface{}             `json:"LatestRestorableTime"`
	LicenseModel                                  string                  `json:"LicenseModel"`
	ListenerEndpoint                              interface{}             `json:"ListenerEndpoint"`
	MasterUserSecret                              interface{}             `json:"MasterUserSecret"`
	MasterUsername                                string                  `json:"MasterUsername"`
	MaxAllocatedStorage                           interface{}             `json:"MaxAllocatedStorage"`
	MonitoringInterval                            int64                   `json:"MonitoringInterval"`
	MonitoringRoleArn                             interface{}             `json:"MonitoringRoleArn"`
	MultiAZ                                       bool                    `json:"MultiAZ"`
	MultiTenant                                   interface{}             `json:"MultiTenant"`
	NcharCharacterSetName                         interface{}             `json:"NcharCharacterSetName"`
	NetworkType                                   string                  `json:"NetworkType"`
	OptionGroupMemberships                        []OptionGroupMembership `json:"OptionGroupMemberships"`
	PendingModifiedValues                         PendingModifiedValues   `json:"PendingModifiedValues"`
	PercentProgress                               interface{}             `json:"PercentProgress"`
	PerformanceInsightsEnabled                    bool                    `json:"PerformanceInsightsEnabled"`
	PerformanceInsightsKMSKeyID                   string                  `json:"PerformanceInsightsKMSKeyId"`
	PerformanceInsightsRetentionPeriod            int64                   `json:"PerformanceInsightsRetentionPeriod"`
	PreferredBackupWindow                         string                  `json:"PreferredBackupWindow"`
	PreferredMaintenanceWindow                    string                  `json:"PreferredMaintenanceWindow"`
	ProcessorFeatures                             interface{}             `json:"ProcessorFeatures"`
	PromotionTier                                 int64                   `json:"PromotionTier"`
	PubliclyAccessible                            bool                    `json:"PubliclyAccessible"`
	ReadReplicaDBClusterIdentifiers               interface{}             `json:"ReadReplicaDBClusterIdentifiers"`
	ReadReplicaDBInstanceIdentifiers              []interface{}           `json:"ReadReplicaDBInstanceIdentifiers"`
	ReadReplicaSourceDBClusterIdentifier          interface{}             `json:"ReadReplicaSourceDBClusterIdentifier"`
	ReadReplicaSourceDBInstanceIdentifier         interface{}             `json:"ReadReplicaSourceDBInstanceIdentifier"`
	ReplicaMode                                   string                  `json:"ReplicaMode"`
	ResumeFullAutomationModeTime                  interface{}             `json:"ResumeFullAutomationModeTime"`
	SecondaryAvailabilityZone                     interface{}             `json:"SecondaryAvailabilityZone"`
	StatusInfos                                   interface{}             `json:"StatusInfos"`
	StorageEncrypted                              bool                    `json:"StorageEncrypted"`
	StorageThroughput                             int64                   `json:"StorageThroughput"`
	StorageType                                   string                  `json:"StorageType"`
	TagList                                       []TagList               `json:"TagList"`
	TdeCredentialArn                              interface{}             `json:"TdeCredentialArn"`
	Timezone                                      interface{}             `json:"Timezone"`
	VpcSecurityGroups                             []VpcSecurityGroup      `json:"VpcSecurityGroups"`
}

type BackupList struct {
	AllocatedStorage                       int64         `json:"AllocatedStorage"`
	AvailabilityZone                       string        `json:"AvailabilityZone"`
	AwsBackupRecoveryPointArn              interface{}   `json:"AwsBackupRecoveryPointArn"`
	BackupRetentionPeriod                  int64         `json:"BackupRetentionPeriod"`
	BackupTarget                           string        `json:"BackupTarget"`
	DBInstanceArn                          string        `json:"DBInstanceArn"`
	DBInstanceAutomatedBackupsArn          string        `json:"DBInstanceAutomatedBackupsArn"`
	DBInstanceAutomatedBackupsReplications interface{}   `json:"DBInstanceAutomatedBackupsReplications"`
	DBInstanceIdentifier                   string        `json:"DBInstanceIdentifier"`
	DbiResourceID                          string        `json:"DbiResourceId"`
	DedicatedLogVolume                     interface{}   `json:"DedicatedLogVolume"`
	Encrypted                              bool          `json:"Encrypted"`
	Engine                                 string        `json:"Engine"`
	EngineVersion                          string        `json:"EngineVersion"`
	IAMDatabaseAuthenticationEnabled       bool          `json:"IAMDatabaseAuthenticationEnabled"`
	InstanceCreateTime                     string        `json:"InstanceCreateTime"`
	Iops                                   int64         `json:"Iops"`
	KmsKeyID                               string        `json:"KmsKeyId"`
	LicenseModel                           string        `json:"LicenseModel"`
	MasterUsername                         string        `json:"MasterUsername"`
	MultiTenant                            interface{}   `json:"MultiTenant"`
	OptionGroupName                        string        `json:"OptionGroupName"`
	Port                                   int64         `json:"Port"`
	Region                                 string        `json:"Region"`
	RestoreWindow                          RestoreWindow `json:"RestoreWindow"`
	Status                                 string        `json:"Status"`
	StorageThroughput                      int64         `json:"StorageThroughput"`
	StorageType                            string        `json:"StorageType"`
	TdeCredentialArn                       interface{}   `json:"TdeCredentialArn"`
	Timezone                               interface{}   `json:"Timezone"`
	VpcID                                  string        `json:"VpcId"`
}

type RestoreWindow struct {
	EarliestTime string `json:"EarliestTime"`
	LatestTime   string `json:"LatestTime"`
}

type CertificateDetails struct {
	CAIdentifier string `json:"CAIdentifier"`
	ValidTill    string `json:"ValidTill"`
}

type DBParameterGroup struct {
	DBParameterGroupName string `json:"DBParameterGroupName"`
	ParameterApplyStatus string `json:"ParameterApplyStatus"`
}

type DBSubnetGroup struct {
	DBSubnetGroupArn         interface{} `json:"DBSubnetGroupArn"`
	DBSubnetGroupDescription string      `json:"DBSubnetGroupDescription"`
	DBSubnetGroupName        string      `json:"DBSubnetGroupName"`
	SubnetGroupStatus        string      `json:"SubnetGroupStatus"`
	Subnets                  []Subnet    `json:"Subnets"`
	SupportedNetworkTypes    interface{} `json:"SupportedNetworkTypes"`
	VpcID                    string      `json:"VpcId"`
}

type Subnet struct {
	SubnetAvailabilityZone SubnetAvailabilityZone `json:"SubnetAvailabilityZone"`
	SubnetIdentifier       string                 `json:"SubnetIdentifier"`
	SubnetOutpost          SubnetOutpost          `json:"SubnetOutpost"`
	SubnetStatus           string                 `json:"SubnetStatus"`
}

type SubnetAvailabilityZone struct {
	Name string `json:"Name"`
}

type SubnetOutpost struct {
	Arn interface{} `json:"Arn"`
}

type Endpoint struct {
	Address      string `json:"Address"`
	HostedZoneID string `json:"HostedZoneId"`
	Port         int64  `json:"Port"`
}

type OptionGroupMembership struct {
	OptionGroupName string `json:"OptionGroupName"`
	Status          string `json:"Status"`
}

type PendingModifiedValues struct {
	AllocatedStorage                 interface{} `json:"AllocatedStorage"`
	AutomationMode                   string      `json:"AutomationMode"`
	BackupRetentionPeriod            interface{} `json:"BackupRetentionPeriod"`
	CACertificateIdentifier          interface{} `json:"CACertificateIdentifier"`
	DBInstanceClass                  interface{} `json:"DBInstanceClass"`
	DBInstanceIdentifier             interface{} `json:"DBInstanceIdentifier"`
	DBSubnetGroupName                interface{} `json:"DBSubnetGroupName"`
	DedicatedLogVolume               interface{} `json:"DedicatedLogVolume"`
	Engine                           interface{} `json:"Engine"`
	EngineVersion                    interface{} `json:"EngineVersion"`
	IAMDatabaseAuthenticationEnabled interface{} `json:"IAMDatabaseAuthenticationEnabled"`
	Iops                             interface{} `json:"Iops"`
	LicenseModel                     interface{} `json:"LicenseModel"`
	MasterUserPassword               interface{} `json:"MasterUserPassword"`
	MultiAZ                          interface{} `json:"MultiAZ"`
	MultiTenant                      interface{} `json:"MultiTenant"`
	PendingCloudwatchLogsExports     interface{} `json:"PendingCloudwatchLogsExports"`
	Port                             interface{} `json:"Port"`
	ProcessorFeatures                interface{} `json:"ProcessorFeatures"`
	ResumeFullAutomationModeTime     interface{} `json:"ResumeFullAutomationModeTime"`
	StorageThroughput                interface{} `json:"StorageThroughput"`
	StorageType                      interface{} `json:"StorageType"`
}

type TagList struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

type VpcSecurityGroup struct {
	Status             string `json:"Status"`
	VpcSecurityGroupID string `json:"VpcSecurityGroupId"`
}

type IPWhiteList struct {
	IPRanges []IPRange `json:"IPRanges"`
}
type IPRange struct {
	CIDRIP      string `json:"CidrIp"`
	Description string `json:"Description"`
}

var engineMap = map[string]string{
	"mysql":        "MySQL",
	"aurora-mysql": "MySQL",
}

func parseOne(assetMsg *model.AssetMessage) (*model.RDSGraph, error) {
	original := &Rds{}
	resource := &model.RDSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.DBInstanceArn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "rds", original.Instance.DBInstanceArn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "rds"
	resource.Name = original.Instance.DBClusterIdentifier
	resource.OriginalLabels = lo.Map(original.Instance.TagList,
		func(e TagList, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.ConnectionAddress = original.Instance.Endpoint.Address
	v, ok := engineMap[original.Instance.Engine]
	resource.Engine = lo.Ternary(ok, v, original.Instance.Engine)
	resource.EngineVersion = original.Instance.EngineVersion
	resource.PublicAllowed = original.Instance.PubliclyAccessible
	resource.IpWhiteList = lo.Flatten(
		lo.Map(original.IPWhiteList, func(ip IPWhiteList, _ int) []string {
			ips := []string{}
			for _, ipRange := range ip.IPRanges {
				ips = append(ips, ipRange.CIDRIP)
			}
			return ips
		}),
	)
	resource.BackupAvailable = len(original.BackupList) > 0
	resource.BackupMethod = "auto"
	if len(original.BackupList) > 0 && original.BackupList[0].RestoreWindow.LatestTime != "" {
		backuptime, err := time.Parse(time.RFC3339, original.BackupList[0].RestoreWindow.LatestTime)
		if err != nil {
			resource.LastBackupTime = 0
		} else {
			resource.LastBackupTime = backuptime.UnixMilli()
		}
	} else {
		resource.LastBackupTime = 0
	}
	resource.LogFileExists = original.LogFileExist

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.DBSubnetGroup.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet,
		lo.Map(original.Instance.DBSubnetGroup.Subnets, func(subnet Subnet, _ int) *model.SubnetGraph {
			return &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", subnet.SubnetIdentifier),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	resource.SG = append(resource.SG,
		lo.Map(original.Instance.VpcSecurityGroups, func(sg VpcSecurityGroup, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", sg.VpcSecurityGroupID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	return resource, nil
}
