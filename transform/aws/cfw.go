package aws

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"net/netip"
	"strconv"
	"strings"
	"time"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

type CFW struct {
	Firewall   Firewall    `json:"firewall"`
	Policy     CFWPolicy   `json:"policy"`
	RuleGroups []RuleGroup `json:"ruleGroups"`
}

type CFWPolicy struct {
	FirewallPolicy         FirewallPolicy         `json:"FirewallPolicy"`
	FirewallPolicyResponse FirewallPolicyResponse `json:"FirewallPolicyResponse"`
	UpdateToken            string                 `json:"UpdateToken"`
}

type FirewallPolicy struct {
	PolicyVariables                 PolicyVariables               `json:"PolicyVariables"`
	StatefulDefaultActions          []string                      `json:"StatefulDefaultActions"`
	StatefulEngineOptions           StatefulEngineOptions         `json:"StatefulEngineOptions"`
	StatefulRuleGroupReferences     []StatefulRuleGroupReference  `json:"StatefulRuleGroupReferences"`
	StatelessCustomActions          []CustomAction                `json:"StatelessCustomActions"`
	StatelessDefaultActions         []string                      `json:"StatelessDefaultActions"`
	StatelessFragmentDefaultActions []string                      `json:"StatelessFragmentDefaultActions"`
	StatelessRuleGroupReferences    []StatelessRuleGroupReference `json:"StatelessRuleGroupReferences"`
	TLSInspectionConfigurationArn   string                        `json:"TLSInspectionConfigurationArn"`
}

type PolicyVariables struct {
	RuleVariables RuleVariables `json:"RuleVariables"`
}

type RuleVariables struct {
	String RuleVariablesString `json:"string"`
}

type RuleVariablesString struct {
	Definition []string `json:"Definition"`
}

type StatefulEngineOptions struct {
	RuleOrder             string `json:"RuleOrder"`
	StreamExceptionPolicy string `json:"StreamExceptionPolicy"`
}

type StatefulRuleGroupReference struct {
	Override    Override `json:"Override"`
	Priority    int64    `json:"Priority"`
	ResourceArn string   `json:"ResourceArn"`
}

type Override struct {
	Action string `json:"Action"`
}

type CustomAction struct {
	ActionDefinition ActionDefinition `json:"ActionDefinition"`
	ActionName       string           `json:"ActionName"`
}

type ActionDefinition struct {
	PublishMetricAction PublishMetricAction `json:"PublishMetricAction"`
}

type PublishMetricAction struct {
	Dimensions []Dimension `json:"Dimensions"`
}

type Dimension struct {
	Value string `json:"Value"`
}

type StatelessRuleGroupReference struct {
	Priority    int64  `json:"Priority"`
	ResourceArn string `json:"ResourceArn"`
}

type FirewallPolicyResponse struct {
	ConsumedStatefulRuleCapacity  int64                   `json:"ConsumedStatefulRuleCapacity"`
	ConsumedStatelessRuleCapacity int64                   `json:"ConsumedStatelessRuleCapacity"`
	Description                   string                  `json:"Description"`
	EncryptionConfiguration       EncryptionConfiguration `json:"EncryptionConfiguration"`
	FirewallPolicyArn             string                  `json:"FirewallPolicyArn"`
	FirewallPolicyID              string                  `json:"FirewallPolicyId"`
	FirewallPolicyName            string                  `json:"FirewallPolicyName"`
	FirewallPolicyStatus          string                  `json:"FirewallPolicyStatus"`
	LastModifiedTime              int64                   `json:"LastModifiedTime"`
	The100OfAssociations          int64                   `json:"100OfAssociations"`
	Tags                          []Tag                   `json:"Tags"`
}

type EncryptionConfiguration struct {
	KeyID string `json:"KeyId"`
	Type  string `json:"Type"`
}

type Firewall struct {
	Firewall       FirewallClass  `json:"Firewall"`
	FirewallStatus FirewallStatus `json:"FirewallStatus"`
	UpdateToken    string         `json:"UpdateToken"`
}

type FirewallClass struct {
	DeleteProtection               bool                    `json:"DeleteProtection"`
	Description                    string                  `json:"Description"`
	EncryptionConfiguration        EncryptionConfiguration `json:"EncryptionConfiguration"`
	FirewallArn                    string                  `json:"FirewallArn"`
	FirewallID                     string                  `json:"FirewallId"`
	FirewallName                   string                  `json:"FirewallName"`
	FirewallPolicyArn              string                  `json:"FirewallPolicyArn"`
	FirewallPolicyChangeProtection bool                    `json:"FirewallPolicyChangeProtection"`
	SubnetChangeProtection         bool                    `json:"SubnetChangeProtection"`
	SubnetMappings                 []SubnetMapping         `json:"SubnetMappings"`
	Tags                           []Tag                   `json:"Tags"`
	VpcID                          string                  `json:"VpcId"`
}

type SubnetMapping struct {
	IPAddressType string `json:"IPAddressType"`
	SubnetID      string `json:"SubnetId"`
}

type FirewallStatus struct {
	CapacityUsageSummary          CapacityUsageSummary `json:"CapacityUsageSummary"`
	ConfigurationSyncStateSummary string               `json:"ConfigurationSyncStateSummary"`
	Status                        string               `json:"Status"`
	SyncStates                    SyncStates           `json:"SyncStates"`
}

type CapacityUsageSummary struct {
	CIDRs CIDRs `json:"CIDRs"`
}

type CIDRs struct {
	AvailableCIDRCount int64                `json:"AvailableCIDRCount"`
	IPSetReferences    CIDRsIPSetReferences `json:"IPSetReferences"`
	UtilizedCIDRCount  int64                `json:"UtilizedCIDRCount"`
}

type CIDRsIPSetReferences struct {
	String PurpleString `json:"string"`
}

type PurpleString struct {
	ResolvedCIDRCount int64 `json:"ResolvedCIDRCount"`
}

type SyncStates struct {
	String SyncStatesString `json:"string"`
}

type SyncStatesString struct {
	Attachment CFWAttachment `json:"Attachment"`
	Config     Config        `json:"Config"`
}

type CFWAttachment struct {
	EndpointID    string `json:"EndpointId"`
	Status        string `json:"Status"`
	StatusMessage string `json:"StatusMessage"`
	SubnetID      string `json:"SubnetId"`
}

type Config struct {
	String ConfigString `json:"string"`
}

type ConfigString struct {
	SyncStatus  string `json:"SyncStatus"`
	UpdateToken string `json:"UpdateToken"`
}

type RuleGroup struct {
	RuleGroup         RuleGroupClass    `json:"RuleGroup"`
	RuleGroupResponse RuleGroupResponse `json:"RuleGroupResponse"`
	UpdateToken       string            `json:"UpdateToken"`
}

type RuleGroupClass struct {
	ReferenceSets       ReferenceSets          `json:"ReferenceSets"`
	RulesSource         RulesSource            `json:"RulesSource"`
	RuleVariables       RuleGroupRuleVariables `json:"RuleVariables"`
	StatefulRuleOptions StatefulRuleOptions    `json:"StatefulRuleOptions"`
}

type ReferenceSets struct {
	IPSetReferences ReferenceSetsIPSetReferences `json:"IPSetReferences"`
}

type ReferenceSetsIPSetReferences struct {
	String FluffyString `json:"string"`
}

type FluffyString struct {
	ReferenceArn string `json:"ReferenceArn"`
}

type RuleGroupRuleVariables struct {
	IPSets   RuleVariables `json:"IPSets"`
	PortSets RuleVariables `json:"PortSets"`
}

type RulesSource struct {
	RulesSourceList                RulesSourceList                `json:"RulesSourceList"`
	RulesString                    string                         `json:"RulesString"`
	StatefulRules                  []StatefulRule                 `json:"StatefulRules"`
	StatelessRulesAndCustomActions StatelessRulesAndCustomActions `json:"StatelessRulesAndCustomActions"`
}

type RulesSourceList struct {
	GeneratedRulesType string   `json:"GeneratedRulesType"`
	Targets            []string `json:"Targets"`
	TargetTypes        []string `json:"TargetTypes"`
}

type StatefulRule struct {
	Action      string       `json:"Action"`
	Header      Header       `json:"Header"`
	RuleOptions []RuleOption `json:"RuleOptions"`
}

type Header struct {
	Destination     string `json:"Destination"`
	DestinationPort string `json:"DestinationPort"`
	Direction       string `json:"Direction"`
	Protocol        string `json:"Protocol"`
	Source          string `json:"Source"`
	SourcePort      string `json:"SourcePort"`
}

type RuleOption struct {
	Keyword  string   `json:"Keyword"`
	Settings []string `json:"Settings"`
}

type StatelessRulesAndCustomActions struct {
	CustomActions  []CustomAction  `json:"CustomActions"`
	StatelessRules []StatelessRule `json:"StatelessRules"`
}

type StatelessRule struct {
	Priority       int64          `json:"Priority"`
	RuleDefinition RuleDefinition `json:"RuleDefinition"`
}

type RuleDefinition struct {
	Actions         []string        `json:"Actions"`
	MatchAttributes MatchAttributes `json:"MatchAttributes"`
}

type MatchAttributes struct {
	DestinationPorts []Port        `json:"DestinationPorts"`
	Destinations     []Destination `json:"Destinations"`
	Protocols        []int64       `json:"Protocols"`
	SourcePorts      []Port        `json:"SourcePorts"`
	Sources          []Destination `json:"Sources"`
	TCPFlags         []TCPFlag     `json:"TCPFlags"`
}

type Port struct {
	FromPort int `json:"FromPort"`
	ToPort   int `json:"ToPort"`
}

type Destination struct {
	AddressDefinition string `json:"AddressDefinition"`
}

type TCPFlag struct {
	Flags []string `json:"Flags"`
	Masks []string `json:"Masks"`
}

type StatefulRuleOptions struct {
	RuleOrder string `json:"RuleOrder"`
}

type RuleGroupResponse struct {
	AnalysisResults         []AnalysisResult        `json:"AnalysisResults"`
	Capacity                int64                   `json:"Capacity"`
	ConsumedCapacity        int64                   `json:"ConsumedCapacity"`
	Description             string                  `json:"Description"`
	EncryptionConfiguration EncryptionConfiguration `json:"EncryptionConfiguration"`
	LastModifiedTime        int64                   `json:"LastModifiedTime"`
	The100OfAssociations    int64                   `json:"100OfAssociations"`
	RuleGroupArn            string                  `json:"RuleGroupArn"`
	RuleGroupID             string                  `json:"RuleGroupId"`
	RuleGroupName           string                  `json:"RuleGroupName"`
	RuleGroupStatus         string                  `json:"RuleGroupStatus"`
	SnsTopic                string                  `json:"SnsTopic"`
	SourceMetadata          SourceMetadata          `json:"SourceMetadata"`
	Tags                    []Tag                   `json:"Tags"`
	Type                    string                  `json:"Type"`
}

type AnalysisResult struct {
	AnalysisDetail    string   `json:"AnalysisDetail"`
	IdentifiedRuleIDS []string `json:"IdentifiedRuleIds"`
	IdentifiedType    string   `json:"IdentifiedType"`
}

type SourceMetadata struct {
	SourceArn         string `json:"SourceArn"`
	SourceUpdateToken string `json:"SourceUpdateToken"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseCFW(raw map[string]any) model.Asset {
	original := &CFW{}
	meta := &model.ObjectMeta{}
	cfw := &model.CFW{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Firewall.Firewall.FirewallArn
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Firewall.Firewall.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "cloud-firewall", original.Firewall.Firewall.FirewallArn)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "cloud-firewall"
	meta.Name = original.Firewall.Firewall.FirewallName
	meta.Description = original.Firewall.Firewall.Description

	cfw.Status = utils.UnwrapOr("on", original.Firewall.FirewallStatus.Status == "READY", "off")
	cfw.ProtectedResource = []model.ProtectedResource{
		{Class: "vpc", ID: utils.GenerateUID(meta.Provider, "vpc", original.Firewall.Firewall.VpcID)},
	}
	cfw.Rules = utils.Flatten(utils.Map(original.RuleGroups, func(ruleGroup RuleGroup) []model.CFWRule {
		return mapCFWRuleGroup(ruleGroup.RuleGroup)
	}))

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, cfw)
	return asset
}

func mapCFWRuleGroup(ruleGroup RuleGroupClass) []model.CFWRule {
	results := []model.CFWRule{}
	for _, statefuleRule := range ruleGroup.RulesSource.StatefulRules {
		_, sourceCidr := parseCFWCidr(statefuleRule.Header.Source)
		ipVersion, destCidr := parseCFWCidr(statefuleRule.Header.Destination)
		results = append(results, model.CFWRule{
			Policy:          utils.UnwrapOr("accept", statefuleRule.Action == "PASS", strings.ToLower(statefuleRule.Action)),
			Protocol:        []string{strings.ToLower(statefuleRule.Header.Protocol)},
			Direction:       utils.UnwrapOr("both", statefuleRule.Header.Direction == "ANY", "forward"),
			IPVersion:       ipVersion,
			SourceCidr:      []string{sourceCidr},
			SourcePortRange: []model.PortRange{parseCFWPortRange(statefuleRule.Header.SourcePort)},
			DestCidr:        []string{destCidr},
			DestPortRange:   []model.PortRange{parseCFWPortRange(statefuleRule.Header.DestinationPort)},
		})
	}

	for _, statelessRule := range ruleGroup.RulesSource.StatelessRulesAndCustomActions.StatelessRules {
		rule := model.CFWRule{}
		rule.Direction = "both"
		rule.Policy, _ = utils.FindMap(statelessRule.RuleDefinition.Actions, func(e string) (string, bool) {
			action, ok := statelessRuleActionMap[e]
			return action, ok
		})
		rule.Protocol = utils.Map(statelessRule.RuleDefinition.MatchAttributes.Protocols, func(e int64) string { return fmt.Sprintf("%d", e) })
		rule.SourceCidr = utils.Map(statelessRule.RuleDefinition.MatchAttributes.Sources, func(e Destination) string {
			_, cidr := parseCFWCidr(e.AddressDefinition)
			return cidr
		})
		rule.SourcePortRange = utils.Map(statelessRule.RuleDefinition.MatchAttributes.SourcePorts, func(e Port) model.PortRange {
			return model.PortRange{
				PortStart: e.FromPort,
				PortEnd:   e.ToPort,
			}
		})
		rule.DestCidr = utils.Map(statelessRule.RuleDefinition.MatchAttributes.Destinations, func(e Destination) string {
			_, cidr := parseCFWCidr(e.AddressDefinition)
			return cidr
		})
		rule.DestPortRange = utils.Map(statelessRule.RuleDefinition.MatchAttributes.DestinationPorts, func(e Port) model.PortRange {
			return model.PortRange{
				PortStart: e.FromPort,
				PortEnd:   e.ToPort,
			}
		})

		if len(rule.SourceCidr) > 0 {
			rule.IPVersion, _ = parseCFWCidr(rule.SourceCidr[0])
		}

		results = append(results, rule)
	}

	return results
}

func parseCFWCidr(cidr string) (string, string) {
	if cidr == "ANY" {
		return "ipv4/6", "0.0.0.0/0"
	}

	prefix, err := netip.ParsePrefix(cidr)
	if err != nil {
		logger.DefaultLogger().Errorf("parse cidr %s failed: %s", cidr, err.Error())
		return "ipv4/6", "0.0.0.0/0"
	}

	if prefix.Addr().Is6() {
		return "ipv6", prefix.String()
	}
	return "ipv4", prefix.String()
}

func parseCFWPortRange(portRange string) model.PortRange {
	if portRange == "ANY" {
		return model.PortRange{PortStart: 1, PortEnd: 65535}
	}

	parts := strings.Split(portRange, ":")
	switch len(parts) {
	case 1:
		port, _ := strconv.Atoi(parts[0])
		return model.PortRange{PortStart: port, PortEnd: port}
	case 2:
		portStart, _ := strconv.Atoi(parts[0])
		portEnd, _ := strconv.Atoi(parts[1])
		return model.PortRange{PortStart: portStart, PortEnd: portEnd}
	default:
		logger.DefaultLogger().Errorf("parse port range %s failed", portRange)
		return model.PortRange{PortStart: 1, PortEnd: 65535}
	}
}

var statelessRuleActionMap = map[string]string{
	"aws:pass":           "accept",
	"aws:drop":           "drop",
	"aws:forward_to_sfe": "forward",
}
