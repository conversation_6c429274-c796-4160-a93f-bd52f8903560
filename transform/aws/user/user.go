package user

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "user"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "iam_user_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["user_list"] = append(resourceData["user_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, userSchema, userData["user_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
}

type User struct {
	AccessKeys     []AccessKey     `json:"accessKeys"`
	LoginProfile   *LoginProfile   `json:"loginProfile"`
	Mfa            []Mfa           `json:"mfa"`
	Policies       []Policy        `json:"policies"`
	User           UserClass       `json:"user"`
	PasswordPolicy *PasswordPolicy `json:"password_policy"`
}

type AccessKey struct {
	AccessKeyID string `json:"AccessKeyId"`
	CreateDate  string `json:"CreateDate"`
	Status      string `json:"Status"`
	UserName    string `json:"UserName"`
}

type LoginProfile struct {
	CreateDate            string `json:"CreateDate"`
	UserName              string `json:"UserName"`
	PasswordResetRequired bool   `json:"PasswordResetRequired"`
}

type Mfa struct {
	EnableDate   string `json:"EnableDate"`
	SerialNumber string `json:"SerialNumber"`
	UserName     string `json:"UserName"`
}

type Policy struct {
	PolicyArn  string `json:"PolicyArn"`
	PolicyName string `json:"PolicyName"`
}

type UserClass struct {
	Arn                 string      `json:"Arn"`
	CreateDate          string      `json:"CreateDate"`
	Path                string      `json:"Path"`
	UserID              string      `json:"UserId"`
	UserName            string      `json:"UserName"`
	PasswordLastUsed    *string     `json:"PasswordLastUsed"`
	PermissionsBoundary interface{} `json:"PermissionsBoundary"`
	Tags                []Tags      `json:"Tags"`
}

type PasswordPolicy struct {
	AllowUsersToChangePassword bool `json:"AllowUsersToChangePassword"`
	ExpirePasswords            bool `json:"ExpirePasswords"`
	HardExpiry                 bool `json:"HardExpiry"`
	MaxPasswordAge             int  `json:"MaxPasswordAge"`
	MinimumPasswordLength      int  `json:"MinimumPasswordLength"`
	PasswordReusePrevention    int  `json:"PasswordReusePrevention"`
	RequireLowercaseCharacters bool `json:"RequireLowercaseCharacters"`
	RequireNumbers             bool `json:"RequireNumbers"`
	RequireSymbols             bool `json:"RequireSymbols"`
	RequireUppercaseCharacters bool `json:"RequireUppercaseCharacters"`
}

type Tags struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.UserGraph, error) {
	original := &User{}
	resource := &model.UserGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.User.UserID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "user", original.User.UserName)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "user"
	resource.Name = original.User.UserName
	resource.OriginalLabels = lo.Map(original.User.Tags,
		func(tag Tags, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.DisplayName = original.User.UserName
	resource.Enabled = true
	t, _ := time.Parse(time.RFC3339, original.User.CreateDate)
	resource.CreatedAt = lo.Ternary(t.UnixMilli() > 0, t.UnixMilli(), 0)
	resource.MFAEnabled = original.Mfa != nil
	if original.LoginProfile != nil {
		resource.LoginAllowed = true
	}

	resource.Policy = lo.Map(original.Policies, func(policy Policy, _ int) *model.PolicyGraph {
		return &model.PolicyGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "policy", policy.PolicyName),
				TargetUID: resource.UID,
			},
		}
	})
	resource.AccessKey = lo.Map(original.AccessKeys, func(key AccessKey, _ int) *model.AkGraph {
		return &model.AkGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ak", key.AccessKeyID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
