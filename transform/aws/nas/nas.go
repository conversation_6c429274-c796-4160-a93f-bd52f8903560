package nas

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nas"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "efs_fileSystem_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nas_list"] = append(resourceData["nas_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["nas_acl_rule_list"] = append(resourceData["nas_acl_rule_list"],
			utils.GenParamsFromStructSlice(resource.ACLRule)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, nasSchema, resourceData["nas_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, nasACLRuleSchema, resourceData["nas_acl_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type EFS struct {
	EFS         EFSClass     `json:"efs"`
	MountPoints []MountPoint `json:"mount_points"`
	Policy      interface{}  `json:"policy"`
}

type EFSClass struct {
	CreationTime                 string               `json:"CreationTime"`
	CreationToken                string               `json:"CreationToken"`
	FileSystemID                 string               `json:"FileSystemId"`
	LifeCycleState               string               `json:"LifeCycleState"`
	NumberOfMountTargets         int64                `json:"NumberOfMountTargets"`
	OwnerID                      string               `json:"OwnerId"`
	PerformanceMode              string               `json:"PerformanceMode"`
	SizeInBytes                  SizeInBytes          `json:"SizeInBytes"`
	Tags                         []Tag                `json:"Tags"`
	AvailabilityZoneID           interface{}          `json:"AvailabilityZoneId"`
	AvailabilityZoneName         interface{}          `json:"AvailabilityZoneName"`
	Encrypted                    bool                 `json:"Encrypted"`
	FileSystemArn                string               `json:"FileSystemArn"`
	FileSystemProtection         FileSystemProtection `json:"FileSystemProtection"`
	KmsKeyID                     string               `json:"KmsKeyId"`
	Name                         string               `json:"Name"`
	ProvisionedThroughputInMibps interface{}          `json:"ProvisionedThroughputInMibps"`
	ThroughputMode               string               `json:"ThroughputMode"`
}

type FileSystemProtection struct {
	ReplicationOverwriteProtection string `json:"ReplicationOverwriteProtection"`
}

type SizeInBytes struct {
	Value           int64       `json:"Value"`
	Timestamp       string      `json:"Timestamp"`
	ValueInArchive  interface{} `json:"ValueInArchive"`
	ValueInIA       int64       `json:"ValueInIA"`
	ValueInStandard int64       `json:"ValueInStandard"`
}

type MountPoint struct {
	FileSystemID         string `json:"FileSystemId"`
	LifeCycleState       string `json:"LifeCycleState"`
	MountTargetID        string `json:"MountTargetId"`
	SubnetID             string `json:"SubnetId"`
	AvailabilityZoneID   string `json:"AvailabilityZoneId"`
	AvailabilityZoneName string `json:"AvailabilityZoneName"`
	IPAddress            string `json:"IpAddress"`
	NetworkInterfaceID   string `json:"NetworkInterfaceId"`
	OwnerID              string `json:"OwnerId"`
	VpcID                string `json:"VpcId"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NASGraph, error) {
	original := &EFS{}
	resource := &model.NASGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.EFS.FileSystemID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nas", original.EFS.FileSystemID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nas"
	resource.Name = original.EFS.Name
	resource.OriginalLabels = lo.Map(original.EFS.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Status = lo.Ternary(strings.EqualFold(original.EFS.LifeCycleState, "available"), "on", "off")
	resource.Encrypt = original.EFS.Encrypted
	resource.IPs = lo.Map(original.MountPoints, func(e MountPoint, _ int) string { return e.IPAddress })
	resource.VPC = lo.Map(original.MountPoints, func(e MountPoint, _ int) *model.VPCGraph {
		return &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", e.VpcID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.Subnet = lo.Map(original.MountPoints, func(e MountPoint, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", e.SubnetID),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
