package ecs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ec2_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type ECS struct {
	Instance Ec2Instance `json:"instance"`
}

type Ec2Instance struct {
	AmiLaunchIndex                          int64                            `json:"AmiLaunchIndex"`
	Architecture                            string                           `json:"Architecture"`
	BlockDeviceMappings                     []Ec2BlockDeviceMapping          `json:"BlockDeviceMappings"`
	BootMode                                string                           `json:"BootMode"`
	CapacityReservationID                   interface{}                      `json:"CapacityReservationId"`
	CapacityReservationSpecification        CapacityReservationSpecification `json:"CapacityReservationSpecification"`
	ClientToken                             string                           `json:"ClientToken"`
	CPUOptions                              CPUOptions                       `json:"CpuOptions"`
	CurrentInstanceBootMode                 string                           `json:"CurrentInstanceBootMode"`
	EbsOptimized                            bool                             `json:"EbsOptimized"`
	ElasticGPUAssociations                  interface{}                      `json:"ElasticGpuAssociations"`
	ElasticInferenceAcceleratorAssociations interface{}                      `json:"ElasticInferenceAcceleratorAssociations"`
	EnaSupport                              bool                             `json:"EnaSupport"`
	EnclaveOptions                          EnclaveOptions                   `json:"EnclaveOptions"`
	HibernationOptions                      HibernationOptions               `json:"HibernationOptions"`
	Hypervisor                              string                           `json:"Hypervisor"`
	IamInstanceProfile                      IamInstanceProfile               `json:"IamInstanceProfile"`
	ImageID                                 string                           `json:"ImageId"`
	InstanceID                              string                           `json:"InstanceId"`
	InstanceLifecycle                       string                           `json:"InstanceLifecycle"`
	InstanceType                            string                           `json:"InstanceType"`
	Ipv6Address                             string                           `json:"Ipv6Address"`
	KernelID                                interface{}                      `json:"KernelId"`
	KeyName                                 string                           `json:"KeyName"`
	LaunchTime                              string                           `json:"LaunchTime"`
	Licenses                                interface{}                      `json:"Licenses"`
	MaintenanceOptions                      MaintenanceOptions               `json:"MaintenanceOptions"`
	MetadataOptions                         MetadataOptions                  `json:"MetadataOptions"`
	Monitoring                              Monitoring                       `json:"Monitoring"`
	NetworkInterfaces                       []Ec2NetworkInterface            `json:"NetworkInterfaces"`
	OutpostArn                              interface{}                      `json:"OutpostArn"`
	Placement                               Placement                        `json:"Placement"`
	Platform                                string                           `json:"Platform"`
	PlatformDetails                         string                           `json:"PlatformDetails"`
	PrivateDNSName                          string                           `json:"PrivateDnsName"`
	PrivateDNSNameOptions                   PrivateDNSNameOptions            `json:"PrivateDnsNameOptions"`
	PrivateIPAddress                        string                           `json:"PrivateIpAddress"`
	ProductCodes                            []interface{}                    `json:"ProductCodes"`
	PublicDNSName                           string                           `json:"PublicDnsName"`
	PublicIPAddress                         string                           `json:"PublicIpAddress"`
	RamdiskID                               interface{}                      `json:"RamdiskId"`
	RootDeviceName                          string                           `json:"RootDeviceName"`
	RootDeviceType                          string                           `json:"RootDeviceType"`
	SecurityGroups                          []Group                          `json:"SecurityGroups"`
	SourceDestCheck                         bool                             `json:"SourceDestCheck"`
	SpotInstanceRequestID                   interface{}                      `json:"SpotInstanceRequestId"`
	SriovNetSupport                         interface{}                      `json:"SriovNetSupport"`
	State                                   State                            `json:"State"`
	StateReason                             interface{}                      `json:"StateReason"`
	StateTransitionReason                   string                           `json:"StateTransitionReason"`
	SubnetID                                string                           `json:"SubnetId"`
	Tags                                    []Tag                            `json:"Tags"`
	TPMSupport                              interface{}                      `json:"TpmSupport"`
	UsageOperation                          string                           `json:"UsageOperation"`
	UsageOperationUpdateTime                string                           `json:"UsageOperationUpdateTime"`
	VirtualizationType                      string                           `json:"VirtualizationType"`
	VpcID                                   string                           `json:"VpcId"`
}

type Association struct {
	PublicIP string `json:"PublicIP"`
}

type Ec2BlockDeviceMapping struct {
	DeviceName string `json:"DeviceName"`
	Ebs        Ebs    `json:"Ebs"`
}

type Ebs struct {
	AssociatedResource  interface{} `json:"AssociatedResource"`
	AttachTime          string      `json:"AttachTime"`
	DeleteOnTermination bool        `json:"DeleteOnTermination"`
	Status              string      `json:"Status"`
	VolumeID            string      `json:"VolumeId"`
	VolumeOwnerID       interface{} `json:"VolumeOwnerId"`
}

type CPUOptions struct {
	AMDSevSnp      string `json:"AmdSevSnp"`
	CoreCount      int64  `json:"CoreCount"`
	ThreadsPerCore int64  `json:"ThreadsPerCore"`
}

type CapacityReservationSpecification struct {
	CapacityReservationPreference string      `json:"CapacityReservationPreference"`
	CapacityReservationTarget     interface{} `json:"CapacityReservationTarget"`
}

type EnclaveOptions struct {
	Enabled bool `json:"Enabled"`
}

type HibernationOptions struct {
	Configured bool `json:"Configured"`
}

type IamInstanceProfile struct {
	Arn string `json:"Arn"`
	ID  string `json:"Id"`
}

type MaintenanceOptions struct {
	AutoRecovery string `json:"AutoRecovery"`
}

type MetadataOptions struct {
	HTTPEndpoint            string `json:"HttpEndpoint"`
	HTTPProtocolIpv6        string `json:"HttpProtocolIpv6"`
	HTTPPutResponseHopLimit int64  `json:"HttpPutResponseHopLimit"`
	HTTPTokens              string `json:"HttpTokens"`
	InstanceMetadataTags    string `json:"InstanceMetadataTags"`
	State                   string `json:"State"`
}

type Monitoring struct {
	State string `json:"State"`
}

type Ec2NetworkInterface struct {
	Association                     Association        `json:"Association"`
	Attachment                      Attachment         `json:"Attachment"`
	ConnectionTrackingConfiguration interface{}        `json:"ConnectionTrackingConfiguration"`
	Description                     string             `json:"Description"`
	Groups                          []Group            `json:"Groups"`
	InterfaceType                   string             `json:"InterfaceType"`
	Ipv4Prefixes                    interface{}        `json:"Ipv4Prefixes"`
	Ipv6Addresses                   []interface{}      `json:"Ipv6Addresses"`
	Ipv6Prefixes                    interface{}        `json:"Ipv6Prefixes"`
	MACAddress                      string             `json:"MacAddress"`
	NetworkInterfaceID              string             `json:"NetworkInterfaceId"`
	OwnerID                         string             `json:"OwnerId"`
	PrivateDNSName                  interface{}        `json:"PrivateDnsName"`
	PrivateIPAddress                string             `json:"PrivateIpAddress"`
	PrivateIPAddresses              []PrivateIPAddress `json:"PrivateIpAddresses"`
	SourceDestCheck                 bool               `json:"SourceDestCheck"`
	Status                          string             `json:"Status"`
	SubnetID                        string             `json:"SubnetId"`
	VpcID                           string             `json:"VpcId"`
}

type Attachment struct {
	AttachTime          string      `json:"AttachTime"`
	AttachmentID        string      `json:"AttachmentId"`
	InstanceID          string      `json:"InstanceId"`
	DeleteOnTermination bool        `json:"DeleteOnTermination"`
	DeviceIndex         int64       `json:"DeviceIndex"`
	EnaSrdSpecification interface{} `json:"EnaSrdSpecification"`
	NetworkCardIndex    int64       `json:"NetworkCardIndex"`
	Status              string      `json:"Status"`
}

type Group struct {
	GroupID   string `json:"GroupId"`
	GroupName string `json:"GroupName"`
}

type PrivateIPAddress struct {
	Association      interface{} `json:"Association"`
	Primary          bool        `json:"Primary"`
	PrivateDNSName   *string     `json:"PrivateDnsName"`
	PrivateIPAddress string      `json:"PrivateIpAddress"`
}

type Placement struct {
	Affinity             interface{} `json:"Affinity"`
	AvailabilityZone     string      `json:"AvailabilityZone"`
	GroupID              interface{} `json:"GroupId"`
	GroupName            string      `json:"GroupName"`
	HostID               interface{} `json:"HostId"`
	HostResourceGroupArn interface{} `json:"HostResourceGroupArn"`
	PartitionNumber      interface{} `json:"PartitionNumber"`
	SpreadDomain         interface{} `json:"SpreadDomain"`
	Tenancy              string      `json:"Tenancy"`
}

type PrivateDNSNameOptions struct {
	EnableResourceNameDNSAAAARecord bool   `json:"EnableResourceNameDnsAAAARecord"`
	EnableResourceNameDNSARecord    bool   `json:"EnableResourceNameDnsARecord"`
	HostnameType                    string `json:"HostnameType"`
}

type State struct {
	Code int64  `json:"Code"`
	Name string `json:"Name"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &ECS{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ecs"
	resource.OriginalLabels = lo.Map(original.Instance.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.Hostname = resource.Name
	resource.Class = "cvm"
	// FIXME: 数据中没有OS信息，需要从AMI信息中获取
	osType := strings.ToLower(original.Instance.PlatformDetails)
	resource.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")
	resource.Spec = original.Instance.InstanceType
	resource.Status = strings.ToLower(original.Instance.State.Name)
	resource.PrimaryPrivateIP = original.Instance.PrivateIPAddress
	resource.PrimaryPublicIP = original.Instance.PublicIPAddress
	resource.PrivateIPList = lo.Map(original.Instance.NetworkInterfaces, func(e Ec2NetworkInterface, _ int) string {
		return e.PrivateIPAddress
	})
	resource.PublicIPList = lo.Map(original.Instance.NetworkInterfaces, func(e Ec2NetworkInterface, _ int) string {
		return e.Association.PublicIP
	})

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.SubnetID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG,
		lo.Map(original.Instance.SecurityGroups, func(e Group, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e.GroupID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.ENI = append(resource.ENI,
		lo.Map(original.Instance.NetworkInterfaces, func(e Ec2NetworkInterface, _ int) *model.ENIGraph {
			return &model.ENIGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "eni", e.NetworkInterfaceID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	return resource, nil
}
