package bucket

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"slices"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "bucket"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "s3_bucket_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["bucket_list"] = append(resourceData["bucket_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)

		resourceData["cors_list"] = append(resourceData["cors_list"],
			utils.GenParamsFromStructSlice(resource.CORS)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, bucketSchema, userData["bucket_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, corsSchema, userData["cors_list"], map[string]any{"last_updated": "test"})
}

type Bucket struct {
	ACL        []ACL             `json:"acl"`
	Bucket     BucketClass       `json:"bucket"`
	Cors       CORSConfiguration `json:"cors"`
	Encryption Encryption        `json:"encryption"`
	Logging    interface{}       `json:"logging"`
	ObjACL     []ObjACL          `json:"objAcl"`
	Policy     string            `json:"policy"`
}

type ACL struct {
	Grantee    Grantee `json:"Grantee"`
	Permission string  `json:"Permission"`
}

type Grantee struct {
	Type         string      `json:"Type"`
	DisplayName  interface{} `json:"DisplayName"`
	EmailAddress interface{} `json:"EmailAddress"`
	ID           string      `json:"ID"`
	URI          string      `json:"URI"`
}

type BucketClass struct {
	CreationDate string `json:"CreationDate"`
	Name         string `json:"Name"`
}

type Encryption struct {
	Rules []Rule `json:"Rules"`
}

type Rule struct {
	ApplyServerSideEncryptionByDefault ApplyServerSideEncryptionByDefault `json:"ApplyServerSideEncryptionByDefault"`
	BucketKeyEnabled                   bool                               `json:"BucketKeyEnabled"`
}

type ApplyServerSideEncryptionByDefault struct {
	SSEAlgorithm   string      `json:"SSEAlgorithm"`
	KMSMasterKeyID interface{} `json:"KMSMasterKeyID"`
}

type CORSConfiguration struct {
	CORSRules []CORSRule `json:"CORSRule"`
}

type CORSRule struct {
	AllowedOrigin []string `json:"AllowedOrigin"`
	AllowedMethod []string `json:"AllowedMethod"`
	AllowedHeader []string `json:"AllowedHeader"`
	ExposeHeader  []string `json:"ExposeHeader"`
	MaxAgeSeconds int64    `json:"MaxAgeSeconds"`
}

type ObjACL struct {
	ACL    []ACL  `json:"acl"`
	Object string `json:"object"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.BucketGraph, error) {
	original := &Bucket{}
	resource := &model.BucketGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Bucket.Name
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "bucket", original.Bucket.Name)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "bucket"
	resource.Name = original.Bucket.Name

	resource.ACL = parseBucketACL(original.ACL)

	rule, found := lo.Find(original.Encryption.Rules, func(rule Rule) bool {
		return rule.BucketKeyEnabled
	})
	if found {
		resource.EncryptionEnabled = true
		resource.EncryptionAlgorithm = rule.ApplyServerSideEncryptionByDefault.SSEAlgorithm
	}

	resource.LoggingEnabled = (original.Logging != nil)

	resource.CORS = lo.Map(original.Cors.CORSRules, func(rule CORSRule, _ int) *model.BucketCORSRuleGraph {
		return &model.BucketCORSRuleGraph{
			AllowedOrigins: rule.AllowedOrigin,
			AllowedMethods: rule.AllowedMethod,
			AllowedHeaders: rule.AllowedHeader,
			ExposeHeaders:  rule.ExposeHeader,
			MaxAgeSeconds:  int(rule.MaxAgeSeconds),
		}
	})

	resource.Policy = provider_utils.ParsePolicyDocument(original.Policy, resource.UID)

	return resource, nil
}

func parseBucketACL(acls []ACL) []string {
	acl := []string{}
	_, found := lo.Find(acls, func(item ACL) bool {
		return slices.Contains([]string{"FULL_CONTROL", "WRITE", "WRITE_CAP"}, item.Permission) &&
			item.Grantee.Type == "Group" && item.Grantee.URI == "http://acs.amazonaws.com/groups/global/AllUsers"
	})
	if found {
		acl = append(acl, "public-write")
	}
	_, found = lo.Find(acls, func(item ACL) bool {
		return slices.Contains([]string{"FULL_CONTROL", "READ", "READ_CAP"}, item.Permission) &&
			item.Grantee.Type == "Group" && item.Grantee.URI == "http://acs.amazonaws.com/groups/global/AllUsers"
	})
	if found {
		acl = append(acl, "public-read")
	}
	if len(acl) == 0 {
		acl = append(acl, "private")
	}
	return acl
}
