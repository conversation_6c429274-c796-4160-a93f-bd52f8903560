package launch_template

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "launch_template"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ec2_launchTemplateVersion_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["launch_template_list"] = append(resourceData["launch_template_list"], utils.GenParamsFromStruct(resource))
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, launchTemplateSchema, resourceData["launch_template_list"], map[string]any{"last_updated": "test"})
}

type LaunchTemplate struct {
	LauchTemplateVersion LauchTemplateVersion `json:"lauchTemplateVersion"`
}

type LauchTemplateVersion struct {
	CreateTime         string             `json:"CreateTime"`
	CreatedBy          string             `json:"CreatedBy"`
	DefaultVersion     bool               `json:"DefaultVersion"`
	LaunchTemplateData LaunchTemplateData `json:"LaunchTemplateData"`
	LaunchTemplateID   string             `json:"LaunchTemplateId"`
	LaunchTemplateName string             `json:"LaunchTemplateName"`
}

type LaunchTemplateData struct {
	TagSpecifications []TagSpecification `json:"TagSpecifications"`
}

type TagSpecification struct {
	ResourceType string `json:"ResourceType"`
	Tags         []Tag  `json:"Tags"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.LaunchTemplateGraph, error) {
	original := &LaunchTemplate{}
	resource := &model.LaunchTemplateGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.LauchTemplateVersion.LaunchTemplateID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "launch-template", original.LauchTemplateVersion.LaunchTemplateID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "launch-template"
	resource.Name = original.LauchTemplateVersion.LaunchTemplateName
	resource.OriginalLabels = lo.Flatten(
		lo.Map(original.LauchTemplateVersion.LaunchTemplateData.TagSpecifications,
			func(e TagSpecification, _ int) []*model.KVGraph {
				return lo.Map(e.Tags, func(e Tag, _ int) *model.KVGraph {
					return utils.NewLabel(e.Key, e.Value, resource.UID)
				})
			},
		),
	)

	return resource, nil
}
