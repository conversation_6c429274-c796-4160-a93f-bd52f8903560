package audit_log

import "AssetStandardizer/graph"

var auditLogStatusSchema = graph.NodeSchema{
	Label: "AuditLogStatus",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
	},
}

var auditLogStatusEntrySchema = graph.NodeSchema{
	Label: "AuditLogStatusEntry",
	Properties: map[string]graph.PropertyRef{
		"uid":            {Name: "uid"},
		"service_name":   {Name: "service_name"},
		"event_name":     {Name: "event_name"},
		"last_ingest_at": {Name: "last_ingest_at"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "AuditLogStatus",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_ENTRY",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
