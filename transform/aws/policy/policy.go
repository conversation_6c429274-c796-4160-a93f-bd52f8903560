package policy

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"net/url"
	"slices"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "iam_policy_version_detail",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   10 * time.Second,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resources := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		policy, err := parseOne(assetMsg)
		if err != nil {
			return nil, err
		} else {
			policy.TransformedObject, _ = sonic.MarshalString(policy)
		}

		resources["policy_list"] = append(resources["policy_list"], utils.GenParamsFromStruct(policy))
		for _, statement := range policy.PolicyStatement {
			resources["policy_statement_list"] = append(resources["policy_statement_list"],
				utils.GenParamsFromStruct(statement),
			)
		}
	}
	return resources, nil
}

func updateResources(resources map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, policySchema, resources["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policyStatementSchema, resources["policy_statement_list"], map[string]any{"last_updated": "test"})
}

type PolicyDetail struct {
	Policy  PolicyInfo `json:"policy"`
	Version Version    `json:"version"`
}

type PolicyInfo struct {
	Arn                           string `json:"Arn"`
	AttachmentCount               int64  `json:"AttachmentCount"`
	CreateDate                    string `json:"CreateDate"`
	DefaultVersionID              string `json:"DefaultVersionId"`
	Description                   string `json:"Description"`
	IsAttachable                  bool   `json:"IsAttachable"`
	Path                          string `json:"Path"`
	PermissionsBoundaryUsageCount int64  `json:"PermissionsBoundaryUsageCount"`
	PolicyID                      string `json:"PolicyId"`
	PolicyName                    string `json:"PolicyName"`
	Tags                          []Tags `json:"Tags"`
	UpdateDate                    string `json:"UpdateDate"`
}

type Version struct {
	CreateDate       string `json:"CreateDate"`
	Document         string `json:"Document"`
	IsDefaultVersion bool   `json:"IsDefaultVersion"`
	VersionID        string `json:"VersionId"`
}

type PolicyDocument struct {
	Statement []Statement `json:"Statement"`
	Version   string      `json:"Version"`
}

type Statement struct {
	Action    any    `json:"Action"`
	Effect    string `json:"Effect"`
	Resource  any    `json:"Resource"`
	Condition any    `json:"Condition"`
	Principal any    `json:"Principal"`
}

type Tags struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

var managedArnPrefixes = []string{
	"arn:aws:iam::aws:policy",
	"arn:aws-cn:iam::aws:policy",
}

func parseOne(assetMsg *model.AssetMessage) (*model.PolicyGraph, error) {
	original := &PolicyDetail{}
	policy := &model.PolicyGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	policy.Provider = provider_utils.ProviderID
	policy.Region = assetMsg.Region
	policy.OriginalObject = string(assetMsg.RawLog)
	policy.OriginalID = original.Policy.Arn
	policy.UID = utils.GenerateUID(policy.Provider, "policy", original.Policy.PolicyName)
	policy.LastSeen = time.Now().UnixMilli()
	policy.Kind = "policy"
	policy.Name = original.Policy.PolicyName
	policy.Description = original.Policy.Description
	policy.OriginalLabels = lo.Map(original.Policy.Tags,
		func(e Tags, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, policy.UID) },
	)

	if slices.Contains(managedArnPrefixes, strings.Split(original.Policy.Arn, "/")[0]) {
		policy.Class = "managed"
	} else {
		policy.Class = "custom"
	}

	document, err := url.QueryUnescape(original.Version.Document)
	if err != nil {
		return nil, err
	}
	policy.PolicyStatement = provider_utils.ParsePolicyDocument(document, policy.UID)

	return policy, nil
}
