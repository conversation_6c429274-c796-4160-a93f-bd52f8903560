package security_group

import "AssetStandardizer/graph"

var sgSchema = graph.NodeSchema{
	Label: "SecurityGroup",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"last_updated":       {Name: "last_updated", SetInKwargs: true},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"is_default":         {Name: "is_default"},
	},
}

var sgRuleSchema = graph.NodeSchema{
	Label: "SecurityGroupRule",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
		"rule_id":      {Name: "rule_id"},
		"description":  {Name: "description"},
		"protocol":     {Name: "protocol"},
		"policy":       {Name: "policy"},
		"priority":     {Name: "priority"},
		"direction":    {Name: "direction"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SecurityGroup",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
		{
			TargetNodeLabel: "SecurityGroup",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "peer_sg_id"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "HAS_PEER_SG",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var srcIPRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SecurityGroupRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_SRC_IP_RANGE",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var srcPortRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SecurityGroupRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_SRC_PORT_RANGE",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dstIPRangeSchema = graph.NodeSchema{
	Label: "IPRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SecurityGroupRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_DST_IP_RANGE",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var dstPortRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "SecurityGroupRule",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.INWARD,
			RelLabel:  "HAS_DST_PORT_RANGE",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
