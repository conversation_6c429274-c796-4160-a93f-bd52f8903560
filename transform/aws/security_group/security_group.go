package security_group

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "security_group"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ec2_sg_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["sg_list"] = append(resourceData["sg_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_rule_list"] = append(resourceData["sg_rule_list"],
			utils.GenParamsFromStructSlice(resource.Rules)...,
		)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.Rules, func(e *model.SGRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.DstPortRange)
			})...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgRuleSchema, resourceData["sg_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIPRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIPRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type SG struct {
	SecurityGroup SecurityGroup `json:"securityGroup"`
}

type SecurityGroup struct {
	Description         string         `json:"Description"`
	GroupID             string         `json:"GroupId"`
	GroupName           string         `json:"GroupName"`
	IPPermissions       []IPPermission `json:"IpPermissions"`
	IPPermissionsEgress []IPPermission `json:"IpPermissionsEgress"`
	OwnerID             string         `json:"OwnerId"`
	Tags                []Tag          `json:"Tags"`
	VpcID               string         `json:"VpcId"`
}

type IPPermission struct {
	FromPort         int           `json:"FromPort"`
	IPProtocol       string        `json:"IpProtocol"`
	IPRanges         []IPRange     `json:"IpRanges"`
	Ipv6Ranges       []interface{} `json:"Ipv6Ranges"`
	PrefixListIDS    []interface{} `json:"PrefixListIds"`
	ToPort           int           `json:"ToPort"`
	UserIDGroupPairs []interface{} `json:"UserIdGroupPairs"`
}

type IPRange struct {
	CIDRIP      string `json:"CidrIp"`
	Description string `json:"Description"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.SGGraph, error) {
	original := &SG{}
	resource := &model.SGGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.SecurityGroup.GroupID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "security-group", original.SecurityGroup.GroupID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "security-group"
	resource.Name = original.SecurityGroup.GroupName
	resource.Description = original.SecurityGroup.Description
	resource.OriginalLabels = lo.Map(original.SecurityGroup.Tags,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.IsDefault = strings.EqualFold(original.SecurityGroup.GroupName, "default")
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.SecurityGroup.VpcID),
			TargetUID: resource.UID,
		},
	})

	rules, err := parseSGRule(original.SecurityGroup.IPPermissions, "ingress", resource.UID)
	if err != nil {
		return nil, fmt.Errorf("parseSGRule failed, perm: %v, err: %v", original.SecurityGroup.IPPermissions, err)
	}
	resource.Rules = append(resource.Rules, rules...)

	rules, err = parseSGRule(original.SecurityGroup.IPPermissionsEgress, "egress", resource.UID)
	if err != nil {
		return nil, fmt.Errorf("parseSGRule failed, perm: %v, err: %v", original.SecurityGroup.IPPermissionsEgress, err)
	}
	resource.Rules = append(resource.Rules, rules...)

	return resource, nil
}

func parseSGRule(perms []IPPermission, direction string, targetUID string) ([]*model.SGRuleGraph, error) {
	results := []*model.SGRuleGraph{}
	for _, perm := range perms {
		rule := &model.SGRuleGraph{}
		ruleJson, err := sonic.MarshalString(perm)
		if err != nil {
			return nil, fmt.Errorf("marshal perm error: %s, perm: %v", err, perm)
		}
		rule.UID = utils.GenerateUID(provider_utils.ProviderID, "sg_rule", ruleJson)
		rule.TargetUID = targetUID
		rule.Protocol = lo.Ternary(perm.IPProtocol != "-1", perm.IPProtocol, "all")
		rule.Policy = "accept"
		rule.Direction = direction

		var ipRanges []*model.IPRangeGraph
		for _, ipRange := range perm.IPRanges {
			ip, err := provider_utils.ParseIPRange(ipRange.CIDRIP, rule.UID)
			if err != nil {
				return nil, fmt.Errorf("parse ip range error: %s, cidr: %s", err, ipRange.CIDRIP)
			}
			ipRanges = append(ipRanges, ip)
		}

		if direction == "ingress" {
			rule.SrcIPRange = ipRanges
			rule.DstIPRange = []*model.IPRangeGraph{utils.NewAnyIPRange(rule.UID, false)}
		} else {
			rule.SrcIPRange = []*model.IPRangeGraph{utils.NewAnyIPRange(rule.UID, false)}
			rule.DstIPRange = ipRanges
		}

		rule.SrcPortRange = []*model.PortRangeGraph{utils.NewPortRange(0, 65535, rule.UID)}
		rule.DstPortRange = []*model.PortRangeGraph{utils.NewPortRange(perm.FromPort, perm.ToPort, rule.UID)}

		results = append(results, rule)
	}
	return results, nil
}
