package lb_listener

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var lbListenerLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "lb-listener"})

func NewLBListenerService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_listener_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformLBListener,
		UpdateResources: updateLBListeners,
	}
}

func transformLBListener(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseLBListener(assetMsg)
		if err != nil {
			lbListenerLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_listener_list"] = append(resourceData["lb_listener_list"], utils.GenParamsFromStruct(resource))
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["port_range_list"] = append(resourceData["port_range_list"],
			utils.GenParamsFromStructSlice(resource.PortRange)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

	}
	return resourceData, nil
}

func updateLBListeners(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbListenerSchema, resourceData["lb_listener_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, portRangeSchema, resourceData["port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})

}

type LBListener struct {
	Listener Listener `json:"listener"`
}

type Listener struct {
	AlpnPolicy           interface{}     `json:"AlpnPolicy"`
	Certificates         interface{}     `json:"Certificates"`
	DefaultActions       []DefaultAction `json:"DefaultActions"`
	ListenerArn          string          `json:"ListenerArn"`
	LoadBalancerArn      string          `json:"LoadBalancerArn"`
	MutualAuthentication interface{}     `json:"MutualAuthentication"`
	Port                 int             `json:"Port"`
	Protocol             string          `json:"Protocol"`
	SSLPolicy            interface{}     `json:"SslPolicy"`
}

type DefaultAction struct {
	Type                      string        `json:"Type"`
	AuthenticateCognitoConfig interface{}   `json:"AuthenticateCognitoConfig"`
	AuthenticateOidcConfig    interface{}   `json:"AuthenticateOidcConfig"`
	FixedResponseConfig       interface{}   `json:"FixedResponseConfig"`
	ForwardConfig             ForwardConfig `json:"ForwardConfig"`
	Order                     interface{}   `json:"Order"`
	RedirectConfig            interface{}   `json:"RedirectConfig"`
	TargetGroupArn            string        `json:"TargetGroupArn"`
}

type ForwardConfig struct {
	TargetGroupStickinessConfig interface{}   `json:"TargetGroupStickinessConfig"`
	TargetGroups                []TargetGroup `json:"TargetGroups"`
}

type TargetGroup struct {
	TargetGroupArn string      `json:"TargetGroupArn"`
	Weight         interface{} `json:"Weight"`
}

func parseLBListener(assetMsg *model.AssetMessage) (*model.LbListenerGraph, error) {
	original := &LBListener{}
	resource := &model.LbListenerGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Listener.ListenerArn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb-listener", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb-listener"
	resource.Name = fmt.Sprintf("%s@%d/%s", original.Listener.LoadBalancerArn, original.Listener.Port, original.Listener.Protocol)

	resource.Protocol = strings.ToLower(original.Listener.Protocol)
	resource.Status = "active"
	resource.CertExists = lo.ToPtr(original.Listener.Certificates != nil)

	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.Listener.LoadBalancerArn),
			TargetUID: resource.UID,
		},
	})
	resource.TargetUID = utils.GenerateUID(resource.Provider, "lb", original.Listener.LoadBalancerArn)
	resource.PortRange = append(resource.PortRange, utils.NewPortRange(original.Listener.Port, original.Listener.Port, resource.UID))

	return resource, nil
}
