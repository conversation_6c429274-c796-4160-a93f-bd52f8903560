package k8s

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"slices"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "k8s"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "k8s_cluster_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["k8s_list"] = append(resourceData["k8s_list"], utils.GenParamsFromStruct(resource))

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, k8sSchema, resourceData["k8s_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
}

type K8S struct {
	Cluster Cluster     `json:"cluster"`
	Subnets []K8SSubnet `json:"subnets"`
}

type Cluster struct {
	AccessConfig            AccessConfig            `json:"AccessConfig"`
	Arn                     string                  `json:"Arn"`
	CertificateAuthority    CertificateAuthority    `json:"CertificateAuthority"`
	ClientRequestToken      interface{}             `json:"ClientRequestToken"`
	ConnectorConfig         interface{}             `json:"ConnectorConfig"`
	CreatedAt               string                  `json:"CreatedAt"`
	EncryptionConfig        interface{}             `json:"EncryptionConfig"`
	Endpoint                string                  `json:"Endpoint"`
	Health                  Health                  `json:"Health"`
	ID                      interface{}             `json:"Id"`
	Identity                Identity                `json:"Identity"`
	KubernetesNetworkConfig KubernetesNetworkConfig `json:"KubernetesNetworkConfig"`
	Logging                 Logging                 `json:"Logging"`
	Name                    string                  `json:"Name"`
	OutpostConfig           interface{}             `json:"OutpostConfig"`
	PlatformVersion         string                  `json:"PlatformVersion"`
	ResourcesVpcConfig      ResourcesVpcConfig      `json:"ResourcesVpcConfig"`
	RoleArn                 string                  `json:"RoleArn"`
	Status                  string                  `json:"Status"`
	Tags                    K8STags                 `json:"Tags"`
	UpgradePolicy           UpgradePolicy           `json:"UpgradePolicy"`
	Version                 string                  `json:"Version"`
}

type AccessConfig struct {
	AuthenticationMode                      string      `json:"AuthenticationMode"`
	BootstrapClusterCreatorAdminPermissions interface{} `json:"BootstrapClusterCreatorAdminPermissions"`
}

type CertificateAuthority struct {
	Data string `json:"Data"`
}

type Health struct {
	Issues []interface{} `json:"Issues"`
}

type Identity struct {
	Oidc Oidc `json:"Oidc"`
}

type Oidc struct {
	Issuer string `json:"Issuer"`
}

type KubernetesNetworkConfig struct {
	IPFamily        string `json:"IpFamily"`
	ServiceIpv4CIDR string `json:"ServiceIpv4Cidr"`
	ServiceIpv6CIDR string `json:"ServiceIpv6Cidr"`
}

type Logging struct {
	ClusterLogging []ClusterLogging `json:"ClusterLogging"`
}

type ClusterLogging struct {
	Enabled bool     `json:"Enabled"`
	Types   []string `json:"Types"`
}

type ResourcesVpcConfig struct {
	ClusterSecurityGroupID string   `json:"ClusterSecurityGroupId"`
	EndpointPrivateAccess  bool     `json:"EndpointPrivateAccess"`
	EndpointPublicAccess   bool     `json:"EndpointPublicAccess"`
	PublicAccessCidrs      []string `json:"PublicAccessCidrs"`
	SecurityGroupIDS       []string `json:"SecurityGroupIds"`
	SubnetIDS              []string `json:"SubnetIds"`
	VpcID                  string   `json:"VpcId"`
}

type K8STags map[string]string

type UpgradePolicy struct {
	SupportType string `json:"SupportType"`
}

type K8SSubnet struct {
	CIDRBlock string `json:"CidrBlock"`
	SubnetID  string `json:"SubnetId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.K8SGraph, error) {
	original := &K8S{}
	resource := &model.K8SGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cluster.Arn
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "kubernetes", original.Cluster.Arn)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "kubernetes"
	resource.Name = original.Cluster.Name
	for k, v := range original.Cluster.Tags {
		resource.OriginalLabels = append(resource.OriginalLabels, utils.NewLabel(k, v, resource.UID))
	}

	resource.Status = lo.Ternary(strings.EqualFold(original.Cluster.Status, "active"), "running", "stopped")
	resource.EngineVersion = original.Cluster.Version
	if original.Cluster.ResourcesVpcConfig.EndpointPublicAccess {
		resource.PublicEndpoint = original.Cluster.Endpoint
	}
	if original.Cluster.ResourcesVpcConfig.EndpointPrivateAccess {
		resource.PrivateEndpoint = original.Cluster.Endpoint
	}

	resource.PodCIDRs = lo.Map(original.Subnets, func(e K8SSubnet, _ int) string { return e.CIDRBlock })
	resource.ServiceCIDRs = []string{original.Cluster.KubernetesNetworkConfig.ServiceIpv4CIDR}
	_, found := lo.Find(original.Cluster.Logging.ClusterLogging,
		func(e ClusterLogging) bool {
			return e.Enabled && slices.Contains(e.Types, "audit")
		},
	)
	resource.AuditLogEnabled = lo.ToPtr(found)

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Cluster.ResourcesVpcConfig.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet,
		lo.Map(original.Cluster.ResourcesVpcConfig.SubnetIDS, func(e string, _ int) *model.SubnetGraph {
			return &model.SubnetGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "subnet", e),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.SG = append(resource.SG,
		lo.Map(original.Cluster.ResourcesVpcConfig.SecurityGroupIDS, func(e string, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	return resource, nil
}
