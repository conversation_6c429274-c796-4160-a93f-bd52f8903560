package subnet

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "subnet"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_subnet_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["subnet_list"] = append(resourceData["subnet_list"], utils.GenParamsFromStruct(resource))

		resourceData["acl_list"] = append(resourceData["acl_list"], utils.GenParamsFromStructSlice(resource.ACL)...)

		resourceData["src_ip_range_list"] = append(resourceData["src_ip_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.SrcIPRange)
			})...,
		)
		resourceData["src_port_range_list"] = append(resourceData["src_port_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.SrcPortRange)
			})...,
		)
		resourceData["dst_ip_range_list"] = append(resourceData["dst_ip_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.DstIPRange)
			})...,
		)
		resourceData["dst_port_range_list"] = append(resourceData["dst_port_range_list"],
			lo.FlatMap(resource.ACL, func(acl *model.ACLRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(acl.DstPortRange)
			})...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, aclSchema, resourceData["acl_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcIpRangeSchema, resourceData["src_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, srcPortRangeSchema, resourceData["src_port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstIpRangeSchema, resourceData["dst_ip_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dstPortRangeSchema, resourceData["dst_port_range_list"], map[string]any{"last_updated": "test"})
}

type AwsSubnet struct {
	ACL    []SubnetACL `json:"acl"`
	Subnet SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	Associations []SubnetAssociation `json:"Associations"`
	Entries      []Entry             `json:"Entries"`
	IsDefault    bool                `json:"IsDefault"`
	NetworkACLID string              `json:"NetworkAclId"`
	OwnerID      string              `json:"OwnerId"`
	Tags         []interface{}       `json:"Tags"`
	VpcID        string              `json:"VpcId"`
}

type SubnetAssociation struct {
	NetworkACLAssociationID string `json:"NetworkAclAssociationId"`
	NetworkACLID            string `json:"NetworkAclId"`
	SubnetID                string `json:"SubnetId"`
}

type Entry struct {
	CIDRBlock     string      `json:"CidrBlock"`
	Egress        bool        `json:"Egress"`
	ICMPTypeCode  interface{} `json:"IcmpTypeCode"`
	Ipv6CIDRBlock string      `json:"Ipv6CidrBlock"`
	PortRange     PortRange   `json:"PortRange"`
	Protocol      string      `json:"Protocol"`
	RuleAction    string      `json:"RuleAction"`
	RuleNumber    int         `json:"RuleNumber"`
}

type PortRange struct {
	From int `json:"from"`
	To   int `json:"to"`
}

type SubnetClass struct {
	AssignIpv6AddressOnCreation   bool                          `json:"AssignIpv6AddressOnCreation"`
	AvailabilityZone              string                        `json:"AvailabilityZone"`
	AvailabilityZoneID            string                        `json:"AvailabilityZoneId"`
	AvailableIPAddressCount       int                           `json:"AvailableIpAddressCount"`
	CIDRBlock                     string                        `json:"CidrBlock"`
	CustomerOwnedIpv4Pool         interface{}                   `json:"CustomerOwnedIpv4Pool"`
	DefaultForAz                  bool                          `json:"DefaultForAz"`
	EnableDns64                   bool                          `json:"EnableDns64"`
	EnableLniAtDeviceIndex        interface{}                   `json:"EnableLniAtDeviceIndex"`
	Ipv6CIDRBlockAssociationSet   []Ipv6CIDRBlockAssociationSet `json:"Ipv6CidrBlockAssociationSet"`
	Ipv6Native                    bool                          `json:"Ipv6Native"`
	MapCustomerOwnedIPOnLaunch    interface{}                   `json:"MapCustomerOwnedIpOnLaunch"`
	MapPublicIPOnLaunch           bool                          `json:"MapPublicIpOnLaunch"`
	OutpostArn                    interface{}                   `json:"OutpostArn"`
	OwnerID                       string                        `json:"OwnerId"`
	PrivateDNSNameOptionsOnLaunch PrivateDNSNameOptionsOnLaunch `json:"PrivateDnsNameOptionsOnLaunch"`
	State                         string                        `json:"State"`
	SubnetArn                     string                        `json:"SubnetArn"`
	SubnetID                      string                        `json:"SubnetId"`
	Tags                          []Tags                        `json:"Tags"`
	VpcID                         string                        `json:"VpcId"`
}
type Ipv6CIDRBlockAssociationSet struct {
	Ipv6CIDRBlock string `json:"ipv6CidrBlock"`
}

type PrivateDNSNameOptionsOnLaunch struct {
	EnableResourceNameDNSAAAARecord bool   `json:"EnableResourceNameDnsAAAARecord"`
	EnableResourceNameDNSARecord    bool   `json:"EnableResourceNameDnsARecord"`
	HostnameType                    string `json:"HostnameType"`
}
type Tags struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.SubnetGraph, error) {
	original := &AwsSubnet{}
	resource := &model.SubnetGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Subnet.SubnetID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "subnet", original.Subnet.SubnetID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "subnet"
	resource.OriginalLabels = lo.Map(original.Subnet.Tags,
		func(tag Tags, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.IsDefault = original.Subnet.DefaultForAz
	resource.CIDR = original.Subnet.CIDRBlock
	resource.AvailableIPCount = original.Subnet.AvailableIPAddressCount

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Subnet.VpcID),
			TargetUID: resource.UID,
		},
	})
	for _, acl := range original.ACL {
		if aclRules, err := parseACL(acl.Entries, resource.UID); err != nil {
			return nil, err
		} else {
			resource.ACL = append(resource.ACL, aclRules...)
		}
	}

	return resource, nil
}

func parseACL(entries []Entry, targetUID string) ([]*model.ACLRuleGraph, error) {
	rules := make([]*model.ACLRuleGraph, 0, len(entries))

	for _, e := range entries {
		entryJson, err := sonic.MarshalString(e)
		if err != nil {
			return nil, fmt.Errorf("sonic.MarshalString failed, entry: %v, err: %v", e, err)
		}
		rule := &model.ACLRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, "acl_rule", entryJson),
				TargetUID: targetUID,
			},
			Protocol:  lo.Ternary(e.Protocol != "-1", e.Protocol, "all"),
			IPVersion: lo.Ternary(e.Ipv6CIDRBlock == "", "IPv4", "IPv6"),
			Policy:    lo.Ternary(e.RuleAction == "allow", "accept", "drop"),
			Priority:  e.RuleNumber,
			Direction: lo.Ternary(e.Egress, "egress", "ingress"),
		}

		ipRange, err := provider_utils.ParseIPRange(e.CIDRBlock, rule.UID)
		if err != nil {
			return nil, fmt.Errorf("parse ip range error: %v, cidr: %s", err, e.CIDRBlock)
		}

		if e.Egress {
			rule.SrcIPRange = append(rule.SrcIPRange, utils.NewAnyIPRange(rule.UID, rule.IPVersion == "IPv6"))
			rule.DstIPRange = append(rule.DstIPRange, ipRange)
		} else {
			rule.SrcIPRange = append(rule.SrcIPRange, ipRange)
			rule.DstIPRange = append(rule.DstIPRange, utils.NewAnyIPRange(rule.UID, rule.IPVersion == "IPv6"))
		}
		rule.SrcPortRange = append(rule.SrcPortRange, utils.NewPortRange(0, 65535, rule.UID))
		rule.DstPortRange = append(rule.DstPortRange, utils.NewPortRange(e.PortRange.From, e.PortRange.To, rule.UID))

		rules = append(rules, rule)
	}
	return rules, nil
}
