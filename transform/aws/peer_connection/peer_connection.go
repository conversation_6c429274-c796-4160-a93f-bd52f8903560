package peer_connection

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "peer-connection"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_peerConnection_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["peer_connection_list"] = append(resourceData["peer_connection_list"],
			utils.GenParamsFromStruct(resource),
		)
		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.FromVPC)...,
		)
		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.ToVPC)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, peerConnectionSchema, resourceData["peer_connection_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
}

type PeerConnection struct {
	PeerConnection PeerConnectionClass `json:"peerConnection"`
}

type PeerConnectionClass struct {
	VpcPeeringConnectionID string           `json:"vpcPeeringConnectionId"`
	RequesterVpcInfo       RequesterVpcInfo `json:"requesterVpcInfo"`
	AccepterVpcInfo        AccepterVpcInfo  `json:"accepterVpcInfo"`
	Status                 Status           `json:"status"`
	TagSet                 []Tags           `json:"tagSet"`
}

type AccepterVpcInfo struct {
	OwnerID        string         `json:"ownerId"`
	VpcID          string         `json:"vpcId"`
	CIDRBlock      string         `json:"cidrBlock"`
	PeeringOptions PeeringOptions `json:"peeringOptions"`
}

type PeeringOptions struct {
	AllowEgressFromLocalClassicLinkToRemoteVpc string `json:"allowEgressFromLocalClassicLinkToRemoteVpc"`
	AllowEgressFromLocalVpcToRemoteClassicLink string `json:"allowEgressFromLocalVpcToRemoteClassicLink"`
	AllowDNSResolutionFromRemoteVpc            string `json:"allowDnsResolutionFromRemoteVpc"`
}

type RequesterVpcInfo struct {
	OwnerID   string `json:"ownerId"`
	VpcID     string `json:"vpcId"`
	CIDRBlock string `json:"cidrBlock"`
}

type Status struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type Tags struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.PeerConnectionGraph, error) {
	original := &PeerConnection{}
	resource := &model.PeerConnectionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.PeerConnection.VpcPeeringConnectionID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "peer-connection", original.PeerConnection.VpcPeeringConnectionID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "peer-connection"
	resource.OriginalLabels = lo.Map(original.PeerConnection.TagSet,
		func(tag Tags, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.Status = lo.Ternary(strings.EqualFold(original.PeerConnection.Status.Code, "active"), "running", "stopped")
	resource.FromVPC = append(resource.FromVPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.PeerConnection.RequesterVpcInfo.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.ToVPC = append(resource.ToVPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.PeerConnection.AccepterVpcInfo.VpcID),
			TargetUID: resource.UID,
		},
	})
	return resource, nil
}
