package eni

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aws/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eni"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ec2_eni_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eni_list"] = append(resourceData["eni_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

	}

	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcEniSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetEniSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgEniSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEniSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipEniSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})

}

type Eni struct {
	Eni EniClass `json:"eni"`
}

type EniClass struct {
	Association                     interface{}           `json:"Association"`
	Attachment                      EniAttachment         `json:"Attachment"`
	AvailabilityZone                string                `json:"AvailabilityZone"`
	ConnectionTrackingConfiguration interface{}           `json:"ConnectionTrackingConfiguration"`
	DenyAllIgwTraffic               interface{}           `json:"DenyAllIgwTraffic"`
	Description                     string                `json:"Description"`
	Groups                          []EniGroup            `json:"Groups"`
	InterfaceType                   string                `json:"InterfaceType"`
	Ipv4Prefixes                    interface{}           `json:"Ipv4Prefixes"`
	Ipv6Address                     interface{}           `json:"Ipv6Address"`
	Ipv6Addresses                   []interface{}         `json:"Ipv6Addresses"`
	Ipv6Native                      interface{}           `json:"Ipv6Native"`
	Ipv6Prefixes                    interface{}           `json:"Ipv6Prefixes"`
	MACAddress                      string                `json:"MacAddress"`
	NetworkInterfaceID              string                `json:"NetworkInterfaceId"`
	OutpostArn                      interface{}           `json:"OutpostArn"`
	OwnerID                         string                `json:"OwnerId"`
	PrivateDNSName                  interface{}           `json:"PrivateDnsName"`
	PrivateIPAddress                string                `json:"PrivateIpAddress"`
	PrivateIPAddresses              []EniPrivateIPAddress `json:"PrivateIpAddresses"`
	RequesterID                     string                `json:"RequesterId"`
	RequesterManaged                bool                  `json:"RequesterManaged"`
	SourceDestCheck                 bool                  `json:"SourceDestCheck"`
	Status                          string                `json:"Status"`
	SubnetID                        string                `json:"SubnetId"`
	TagSet                          []TagSet              `json:"TagSet"`
	VpcID                           string                `json:"VpcId"`
}

type EniAttachment struct {
	AttachTime          string      `json:"AttachTime"`
	AttachmentID        string      `json:"AttachmentId"`
	DeleteOnTermination bool        `json:"DeleteOnTermination"`
	DeviceIndex         int64       `json:"DeviceIndex"`
	EnaSrdSpecification interface{} `json:"EnaSrdSpecification"`
	InstanceID          string      `json:"InstanceId"`
	InstanceOwnerID     string      `json:"InstanceOwnerId"`
	NetworkCardIndex    int64       `json:"NetworkCardIndex"`
	Status              string      `json:"Status"`
}

type EniGroup struct {
	GroupID   string `json:"GroupId"`
	GroupName string `json:"GroupName"`
}

type EniPrivateIPAddress struct {
	Association      EniAssociation `json:"Association"`
	Primary          bool           `json:"Primary"`
	PrivateDNSName   *string        `json:"PrivateDnsName"`
	PrivateIPAddress string         `json:"PrivateIpAddress"`
}

type EniAssociation struct {
	PublicIP     string `json:"PublicIp"`
	AllocationID string `json:"AllocationId"`
}

type TagSet struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ENIGraph, error) {
	original := &Eni{}
	resource := &model.ENIGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Eni.NetworkInterfaceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eni", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Eni.Description
	resource.Kind = "eni"
	resource.OriginalLabels = lo.Map(original.Eni.TagSet,
		func(e TagSet, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)
	resource.Name = provider_utils.GetNameFromTags(resource.OriginalLabels)

	resource.PrimaryIP = original.Eni.PrivateIPAddress
	resource.MacAddress = original.Eni.MACAddress
	resource.Status = lo.Ternary(strings.EqualFold(original.Eni.Status, "InUse"), "inuse", "available")
	resource.Class = "primary"
	resource.PrivateIPList = lo.Map(original.Eni.PrivateIPAddresses, func(e EniPrivateIPAddress, _ int) string { return e.PrivateIPAddress })
	resource.PublicIPList = lo.FilterMap(original.Eni.PrivateIPAddresses, func(e EniPrivateIPAddress, _ int) (string, bool) {
		return e.Association.PublicIP, e.Association.PublicIP != ""
	})

	if original.Eni.Attachment.InstanceID != "" {
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.Eni.Attachment.InstanceID),
				TargetUID: resource.UID,
			},
		})
	}
	if original.Eni.VpcID != "" {
		resource.VPC = append(resource.VPC, &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", original.Eni.VpcID),
				TargetUID: resource.UID,
			},
		})
	}
	if original.Eni.SubnetID != "" {
		resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", original.Eni.SubnetID),
				TargetUID: resource.UID,
			},
		})
	}
	resource.SG = append(resource.SG,
		lo.Map(original.Eni.Groups, func(e EniGroup, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e.GroupID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	return resource, nil
}
