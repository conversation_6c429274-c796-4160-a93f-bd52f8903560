package utils

import (
	"AssetStandardizer/model"
	"fmt"
	"reflect"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
	"github.com/mitchellh/mapstructure"
	"github.com/samber/lo"
)

func GenerateUID(provider string, kind string, id string) string {
	if id == "" {
		return ""
	}
	return uuid.NewSHA1(uuid.Nil, []byte(fmt.Sprintf("%s+%s+%s", provider, kind, id))).String()
}

func Decode(input interface{}, output interface{}) error {
	config := &mapstructure.DecoderConfig{
		Metadata: nil,
		TagName:  "json",
		Result:   output,
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return err
	}

	return decoder.Decode(input)
}

type ValueMap map[string]any
type MergeAssetConfig struct {
	omitempty bool
}
type MergeAssetOption func(*MergeAssetConfig)

func WithOmitempty() MergeAssetOption {
	return func(mac *MergeAssetConfig) { mac.omitempty = true }
}
func MergeAsset(asset model.Asset, resource any, options ...MergeAssetOption) model.Asset {
	cfg := &MergeAssetConfig{}
	for _, option := range options {
		option(cfg)
	}
	resourceMap := StructToMap(resource)
	if resourceMap == nil {
		return asset
	}

	for k, v := range resourceMap {
		if cfg.omitempty && (!reflect.ValueOf(v).IsValid() || reflect.ValueOf(v).IsZero()) {
			continue
		}
		asset[k] = v
	}
	return asset
}

func StructToMap(obj any) map[string]any {
	var result map[string]any
	Decode(obj, &result)
	return result
}

func ToJsonStr(value any) string {
	if value == nil {
		return ""
	}

	b, err := sonic.MarshalString(value)
	if err != nil {
		return ""
	}
	return b
}

func GenParamsFromStruct(input any) map[string]any {
	var output map[string]any

	config := &mapstructure.DecoderConfig{
		Metadata: nil,
		TagName:  "graph",
		Result:   &output,
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return nil
	}

	decoder.Decode(input)

	return output
}

func GenParamsFromStructSlice[T any](input []T) []map[string]any {
	return lo.Map(input, func(item T, _ int) map[string]any {
		return GenParamsFromStruct(item)
	})
}

func NewAnyIPRange(targetUID string, isIPv6 bool) (ipRange *model.IPRangeGraph) {
	if isIPv6 {
		ipRange = &model.IPRangeGraph{
			BaseNode: model.BaseNode{
				UID:       GenerateUID("", "ip_range", "::/0-ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"),
				TargetUID: targetUID,
			},
			Start: "::",
			End:   "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff",
		}
	} else {
		ipRange = &model.IPRangeGraph{
			BaseNode: model.BaseNode{
				UID:       GenerateUID("", "ip_range", "0.0.0.0-***************"),
				TargetUID: targetUID,
			},
			Start: "0.0.0.0",
			End:   "***************",
		}
	}
	return ipRange
}


func NewPortRange(start int, end int, targetUID string) *model.PortRangeGraph {
	return &model.PortRangeGraph{
		BaseNode: model.BaseNode{
			UID:       GenerateUID("", "port_range", fmt.Sprintf("%d-%d", start, end)),
			TargetUID: targetUID,
		},
		Start: start,
		End:   end,
	}
}

func NewLabel(k string, v string, targetUID string) *model.KVGraph {
	return &model.KVGraph{
		BaseNode: model.BaseNode{
			UID:       GenerateUID("", "label", fmt.Sprintf("%s:%s", k, v)),
			TargetUID: targetUID,
		},
		Key:   k,
		Value: v,
	}
}
