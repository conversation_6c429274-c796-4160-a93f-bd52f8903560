package utils

import (
	"AssetStandardizer/model"
	"testing"

	"github.com/stretchr/testify/assert"
)

var testResource = model.VPCGraph{
	BaseNode: model.BaseNode{
		UID:       "123",
		TargetUID: "456",
	},
	ObjectMetaGraph: model.ObjectMetaGraph{
		Provider:       "test",
		OriginalID:     "789",
		OriginalObject: "test",
		Region:         "test",
		LastSeen:       123,
		Kind:           "test",
	},
	Subnet: []*model.SubnetGraph{
		{
			BaseNode: model.BaseNode{
				UID: "123",
			},
		},
	},
}

func TestGenParamsFromStruct(t *testing.T) {
	params := GenParamsFromStruct(testResource)

	assert.Equal(t, params["uid"], "123")
	assert.Equal(t, params["target_uid"], "456")
	assert.Equal(t, params["provider"], "test")
	assert.Equal(t, params["original_id"], "789")
	assert.Equal(t, params["original_object"], "test")
	assert.Equal(t, params["region"], "test")
	assert.Equal(t, params["last_seen"], int64(123))
	assert.Equal(t, params["kind"], "test")
	assert.Equal(t, params["subnet"], nil)
}

func TestToJsonStr(t *testing.T) {
	json := ToJsonStr(testResource)

	assert.Equal(t, json, `{"uid": "123","provider": "test","original_id": "789","original_object": "test","region": "test","kind": "test","last_seen": 123,"subnet": [{"uid": "123"}]}`)
}
