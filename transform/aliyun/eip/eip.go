package eip

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eip"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_eip_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eip_list"] = append(resourceData["eip_list"], utils.GenParamsFromStruct(resource))
		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)
		resourceData["nat_list"] = append(resourceData["nat_list"],
			utils.GenParamsFromStructSlice(resource.NAT)...,
		)
		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}

	return resourceData, nil
}

func updateResources(eipData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eipSchema, eipData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniEipSchema, eipData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, natEipSchema, eipData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEipSchema, eipData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbEipSchema, eipData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEipSchema, eipData["label_list"], map[string]any{"last_updated": "test"})

}

type Resource struct {
	Eip EipClass `json:"eip"`
}

type EipClass struct {
	AllocationID              string         `json:"AllocationId"`
	AllocationTime            string         `json:"AllocationTime"`
	Bandwidth                 string         `json:"Bandwidth"`
	BandwidthPackageBandwidth string         `json:"BandwidthPackageBandwidth"`
	BandwidthPackageID        string         `json:"BandwidthPackageId"`
	BandwidthPackageType      string         `json:"BandwidthPackageType"`
	BizType                   string         `json:"BizType"`
	BusinessStatus            string         `json:"BusinessStatus"`
	ChargeType                string         `json:"ChargeType"`
	DeletionProtection        bool           `json:"DeletionProtection"`
	Description               string         `json:"Description"`
	ExpiredTime               string         `json:"ExpiredTime"`
	HDMonitorStatus           string         `json:"HDMonitorStatus"`
	HasReservationData        string         `json:"HasReservationData"`
	ISP                       string         `json:"ISP"`
	InstanceID                string         `json:"InstanceId"`
	InstanceRegionID          string         `json:"InstanceRegionId"`
	InstanceType              string         `json:"InstanceType"`
	InternetChargeType        string         `json:"InternetChargeType"`
	IPAddress                 string         `json:"IpAddress"`
	Mode                      string         `json:"Mode"`
	Name                      string         `json:"Name"`
	Netmode                   string         `json:"Netmode"`
	OperationLocks            OperationLocks `json:"OperationLocks"`
	PrivateIPAddress          string         `json:"PrivateIpAddress"`
	RegionID                  string         `json:"RegionId"`
	ResourceGroupID           string         `json:"ResourceGroupId"`
	SecondLimited             bool           `json:"SecondLimited"`
	SecurityProtectionTypes   OperationLocks `json:"SecurityProtectionTypes"`
	SegmentInstanceID         string         `json:"SegmentInstanceId"`
	ServiceManaged            int64          `json:"ServiceManaged"`
	Status                    string         `json:"Status"`
	Tags                      Tags           `json:"Tags"`
	VpcID                     string         `json:"VpcId"`
}

type OperationLocks struct {
	LockReason []any `json:"lockReason"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	TagKey   string `json:"Key"`
	TagValue string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.EipGraph, error) {
	original := &Resource{}
	resource := &model.EipGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Eip.AllocationID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eip", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Eip.Description
	resource.Kind = "eip"
	resource.Name = original.Eip.Name
	resource.OriginalLabels = lo.Map(original.Eip.Tags.Tag,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.IP = original.Eip.IPAddress
	resource.Status = lo.Ternary(strings.EqualFold(original.Eip.Status, "InUse"), "binded", "available")
	resource.Bandwidth, _ = strconv.Atoi(original.Eip.Bandwidth)
	resource.ISP = original.Eip.ISP
	switch original.Eip.InstanceType {
	case "NetworkInterface":
		resource.ENI = append(resource.ENI, &model.ENIGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eni", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "Nat":
		resource.NAT = append(resource.NAT, &model.NATGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "nat", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "EcsInstance":
		resource.ECS = append(resource.ECS, &model.ECSGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "ecs", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	case "SlbInstance":
		resource.LB = append(resource.LB, &model.LBGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "lb", original.Eip.InstanceID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
