package cdn

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cdn"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cdn_domain_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cdn_list"] = append(resourceData["cdn_list"], utils.GenParamsFromStruct(resource))
		resourceData["source_site_list"] = append(resourceData["source_site_list"],
			utils.GenParamsFromStructSlice(resource.SourceSite)...,
		)
	}

	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cdnSchema, resourceData["cdn_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sourceSiteSchema, resourceData["source_site_list"], map[string]any{"last_updated": "test"})

}

type CDN struct {
	CDNDomain     CDNDomain      `json:"cdnDomain"`
	DomainConfigs []DomainConfig `json:"domainConfigs"`
}

type CDNDomain struct {
	CDNType         string  `json:"CdnType"`
	Cname           string  `json:"Cname"`
	Coverage        string  `json:"Coverage"`
	Description     string  `json:"Description"`
	DomainID        int64   `json:"DomainId"`
	DomainName      string  `json:"DomainName"`
	DomainStatus    string  `json:"DomainStatus"`
	GmtCreated      string  `json:"GmtCreated"`
	GmtModified     string  `json:"GmtModified"`
	ResourceGroupID string  `json:"ResourceGroupId"`
	Sandbox         string  `json:"Sandbox"`
	Sources         Sources `json:"Sources"`
	SSLProtocol     string  `json:"SslProtocol"`
}

type Sources struct {
	Source []Source `json:"Source"`
}

type Source struct {
	Content  string `json:"Content"`
	Port     int64  `json:"Port"`
	Priority string `json:"Priority"`
	Type     string `json:"Type"`
	Weight   string `json:"Weight"`
}

type DomainConfig struct {
	ConfigID     string       `json:"ConfigId"`
	FunctionArgs FunctionArgs `json:"FunctionArgs"`
	FunctionName string       `json:"FunctionName"`
	ParentID     string       `json:"ParentId"`
	Status       string       `json:"Status"`
}

type FunctionArgs struct {
	FunctionArg []FunctionArg `json:"FunctionArg"`
}

type FunctionArg struct {
	ArgName  string `json:"ArgName"`
	ArgValue string `json:"ArgValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CDNGraph, error) {
	original := &CDN{}
	resource := &model.CDNGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.CDNDomain.DomainName
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "cdn", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cdn"
	resource.Name = original.CDNDomain.DomainName

	resource.Status = lo.Ternary(strings.EqualFold(original.CDNDomain.DomainStatus, "online"), "running", "stopped")
	resource.HTTPSEnabled = original.CDNDomain.SSLProtocol == "on"
	resource.CNAME = original.CDNDomain.Cname
	resource.Coverage = strings.ToLower(original.CDNDomain.Coverage)
	resource.Class = strings.ToLower(original.CDNDomain.CDNType)

	resource.SourceSite = lo.Map(original.CDNDomain.Sources.Source, func(e Source, _ int) *model.SourceSiteGraph {
		weight, _ := strconv.Atoi(e.Weight)
		return &model.SourceSiteGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "source_site", fmt.Sprintf("%s-%d", e.Content, weight)),
				TargetUID: resource.UID,
			},
			URL:    e.Content,
			Weight: weight,
		}
	})

	if v, found := extractFromCDNFunctions(original.DomainConfigs, "https_force", "enable"); found {
		resource.ForceHTTPS = v == "on"
	}

	if v, found := extractFromCDNFunctions(original.DomainConfigs, "referer_white_list_set", "allow_empty"); found {
		resource.AllowEmptyReferer = v == "on"
	}
	if v, found := extractFromCDNFunctions(original.DomainConfigs, "referer_white_list_set", "refer_domain_allow_list"); found {
		resource.RefererWhiteList = strings.Split(v, ",")
	}

	if v, found := extractFromCDNFunctions(original.DomainConfigs, "referer_black_list_set", "allow_empty"); found {
		resource.AllowEmptyReferer = v == "on"
	}
	if v, found := extractFromCDNFunctions(original.DomainConfigs, "referer_black_list_set", "refer_domain_deny_list"); found {
		resource.RefererWhiteList = strings.Split(v, ",")
	}

	if v, found := extractFromCDNFunctions(original.DomainConfigs, "ip_white_list_set", "ip_list"); found {
		resource.IPWhiteList = lo.Map(strings.Split(v, ","), func(e string, _ int) string { return provider_utils.FormatCIDR(e) })
	}
	if v, found := extractFromCDNFunctions(original.DomainConfigs, "ip_black_list_set", "ip_list"); found {
		resource.IPBlackList = lo.Map(strings.Split(v, ","), func(e string, _ int) string { return provider_utils.FormatCIDR(e) })
	}

	_, resource.RateLimitEnabled = lo.Find(original.DomainConfigs, func(e DomainConfig) bool { return e.FunctionName == "limitRate" })
	// not support by aliyun
	resource.FreqLimitEnabled = false
	// TODO: get data from cloud monitor, no data now.
	resource.BandwidthLimitEnabled = false

	return resource, nil
}

func extractFromCDNFunctions(domainConfigs []DomainConfig, functionName string, argName string) (string, bool) {
	for _, e := range domainConfigs {
		if e.FunctionName != functionName || e.Status != "on" {
			return "", false
		}
		for _, arg := range e.FunctionArgs.FunctionArg {
			if arg.ArgName == argName {
				return arg.ArgValue, true
			}
		}
	}
	return "", false
}
