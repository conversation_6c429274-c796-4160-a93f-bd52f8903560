package function

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "function"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "fc_function_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["function_list"] = append(resourceData["function_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, functionSchema, resourceData["function_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type Function struct {
	Function FunctionClass `json:"function"`
}

type FunctionClass struct {
	CodeChecksum            string                  `json:"codeChecksum"`
	CodeSize                int64                   `json:"codeSize"`
	CPU                     float64                 `json:"cpu"`
	CreatedTime             string                  `json:"createdTime"`
	Description             string                  `json:"description"`
	DisableOndemand         bool                    `json:"disableOndemand"`
	DiskSize                int64                   `json:"diskSize"`
	EnvironmentVariables    interface{}             `json:"environmentVariables"`
	FunctionArn             string                  `json:"functionArn"`
	FunctionID              string                  `json:"functionId"`
	FunctionName            string                  `json:"functionName"`
	Handler                 string                  `json:"handler"`
	InstanceConcurrency     int64                   `json:"instanceConcurrency"`
	InstanceLifecycleConfig InstanceLifecycleConfig `json:"instanceLifecycleConfig"`
	InternetAccess          bool                    `json:"internetAccess"`
	LastModifiedTime        string                  `json:"lastModifiedTime"`
	Layers                  interface{}             `json:"layers"`
	LogConfig               LogConfig               `json:"logConfig"`
	MemorySize              int64                   `json:"memorySize"`
	NASConfig               NASConfig               `json:"nasConfig"`
	OSSMountConfig          OSSMountConfig          `json:"ossMountConfig"`
	Role                    string                  `json:"role"`
	Runtime                 string                  `json:"runtime"`
	Tags                    []Tag                   `json:"tags"`
	Timeout                 int64                   `json:"timeout"`
	TracingConfig           TracingConfig           `json:"tracingConfig"`
	VpcConfig               FunctionVpcConfig       `json:"vpcConfig"`
}

type InstanceLifecycleConfig struct {
	Initializer Initializer `json:"initializer"`
	PreStop     Initializer `json:"preStop"`
}

type Initializer struct {
	Handler string `json:"handler"`
	Timeout int64  `json:"timeout"`
}

type LogConfig struct {
	EnableInstanceMetrics bool   `json:"enableInstanceMetrics"`
	EnableRequestMetrics  bool   `json:"enableRequestMetrics"`
	Logstore              string `json:"logstore"`
	Project               string `json:"project"`
}

type NASConfig struct {
	GroupID     int64                 `json:"groupId"`
	MountPoints []NASConfigMountPoint `json:"mountPoints"`
	UserID      int64                 `json:"userId"`
}

type NASConfigMountPoint struct {
	EnableTLS  bool   `json:"enableTLS"`
	MountDir   string `json:"mountDir"`
	ServerAddr string `json:"serverAddr"`
}

type OSSMountConfig struct {
	MountPoints []OSSMountConfigMountPoint `json:"mountPoints"`
}
type OSSMountConfigMountPoint struct {
	BucketName string `json:"bucketName"`
	BucketPath string `json:"bucketPath"`
	Endpoint   string `json:"endpoint"`
	MountDir   string `json:"mountDir"`
	ReadOnly   bool   `json:"readOnly"`
}

type TracingConfig struct {
	Params interface{} `json:"params"`
}

type FunctionVpcConfig struct {
	SecurityGroupID string   `json:"securityGroupId"`
	VSwitchIDS      []string `json:"vSwitchIds"`
	VpcID           string   `json:"vpcId"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.FunctionGraph, error) {
	original := &Function{}
	resource := &model.FunctionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Function.FunctionID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "function", original.Function.FunctionID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "function"
	resource.Name = original.Function.FunctionName
	resource.Description = original.Function.Description
	resource.OriginalLabels = lo.Map(original.Function.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.DirectInternetAccessAllowed = original.Function.InternetAccess
	resource.Runtime = strings.ToLower(original.Function.Runtime)
	resource.LogEnabled = original.Function.LogConfig.Logstore != ""
	resource.LogPath = original.Function.LogConfig.Logstore

	if len(original.Function.NASConfig.MountPoints) > 0 {
		resource.NasMountPoints = lo.Map(original.Function.NASConfig.MountPoints, func(e NASConfigMountPoint, _ int) string {
			return e.ServerAddr
		})
	}

	if len(original.Function.OSSMountConfig.MountPoints) > 0 {
		resource.BucketMountPoints = lo.Map(original.Function.OSSMountConfig.MountPoints, func(e OSSMountConfigMountPoint, _ int) string {
			return fmt.Sprintf("%s:%s", e.BucketName, e.BucketPath)
		})
	}

	if original.Function.VpcConfig.VpcID != "" {
		resource.VPC = append(resource.VPC, &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", original.Function.VpcConfig.VpcID),
				TargetUID: resource.UID,
			},
		})
		resource.Subnet = append(resource.Subnet,
			lo.Map(original.Function.VpcConfig.VSwitchIDS, func(e string, _ int) *model.SubnetGraph {
				return &model.SubnetGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "subnet", e),
						TargetUID: resource.UID,
					},
				}
			})...,
		)
		resource.SG = append(resource.SG, &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", original.Function.VpcConfig.SecurityGroupID),
				TargetUID: resource.UID,
			},
		})
	}

	return resource, nil
}
