package rds

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "rds"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "rds_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["rds_list"] = append(resourceData["rds_list"], utils.GenParamsFromStruct(resource))

		resourceData["account_list"] = append(resourceData["account_list"],
			utils.GenParamsFromStructSlice(resource.Accounts)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, rdsSchema, resourceData["rds_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, rdsAccountSchema, resourceData["account_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type Rds struct {
	Instance       Instance      `json:"instance"`
	IPWhiteList    []IPWhiteList `json:"ipWhiteList"`
	SecurityGroups []RdsSG       `json:"securityGroups"`
	Accounts       []RdsAccount  `json:"accounts"`
	Tde            RdsTde        `json:"tde"`
	BackupList     []BackupList  `json:"backupList"`
	AuditEnabled   bool          `json:"auditEnabled"`
}

type BackupList struct {
	BackupDownloadURL         string `json:"BackupDownloadURL"`
	BackupEndTime             string `json:"BackupEndTime"`
	BackupID                  string `json:"BackupId"`
	BackupInitiator           string `json:"BackupInitiator"`
	BackupIntranetDownloadURL string `json:"BackupIntranetDownloadURL"`
	BackupMethod              string `json:"BackupMethod"`
	BackupMode                string `json:"BackupMode"`
	BackupSize                int64  `json:"BackupSize"`
	BackupStartTime           string `json:"BackupStartTime"`
	BackupStatus              string `json:"BackupStatus"`
	BackupType                string `json:"BackupType"`
	Checksum                  string `json:"Checksum"`
	ConsistentTime            int64  `json:"ConsistentTime"`
	CopyOnlyBackup            string `json:"CopyOnlyBackup"`
	DBInstanceID              string `json:"DBInstanceId"`
	Encryption                string `json:"Encryption"`
	Engine                    string `json:"Engine"`
	EngineVersion             string `json:"EngineVersion"`
	HostInstanceID            string `json:"HostInstanceID"`
	IsAvail                   int64  `json:"IsAvail"`
	MetaStatus                string `json:"MetaStatus"`
	StorageClass              string `json:"StorageClass"`
	StoreStatus               string `json:"StoreStatus"`
}

type RdsAccount struct {
	AccountDescription string             `json:"AccountDescription"`
	AccountName        string             `json:"AccountName"`
	AccountStatus      string             `json:"AccountStatus"`
	AccountType        string             `json:"AccountType"`
	DBInstanceID       string             `json:"DBInstanceId"`
	DatabasePrivileges DatabasePrivileges `json:"DatabasePrivileges"`
	PrivExceeded       string             `json:"PrivExceeded"`
}

type DatabasePrivileges struct {
	DatabasePrivilege []DatabasePrivilege `json:"DatabasePrivilege,omitempty"`
}

type DatabasePrivilege struct {
	AccountPrivilege       string `json:"AccountPrivilege"`
	AccountPrivilegeDetail string `json:"AccountPrivilegeDetail"`
	DBName                 string `json:"DBName"`
}

type IPWhiteList struct {
	DBInstanceIPArrayAttribute string `json:"DBInstanceIPArrayAttribute"`
	DBInstanceIPArrayName      string `json:"DBInstanceIPArrayName"`
	SecurityIPList             string `json:"SecurityIPList"`
	SecurityIPType             string `json:"SecurityIPType"`
}

type Instance struct {
	ConnectionMode        string                `json:"ConnectionMode"`
	ConnectionString      string                `json:"ConnectionString"`
	CreateTime            string                `json:"CreateTime"`
	DBInstanceClass       string                `json:"DBInstanceClass"`
	DBInstanceDescription string                `json:"DBInstanceDescription"`
	DBInstanceID          string                `json:"DBInstanceId"`
	DBInstanceMemory      int64                 `json:"DBInstanceMemory"`
	DBInstanceNetType     string                `json:"DBInstanceNetType"`
	DBInstanceStatus      string                `json:"DBInstanceStatus"`
	DBInstanceStorageType string                `json:"DBInstanceStorageType"`
	DBInstanceType        string                `json:"DBInstanceType"`
	DeletionProtection    bool                  `json:"DeletionProtection"`
	Engine                string                `json:"Engine"`
	EngineVersion         string                `json:"EngineVersion"`
	ExpireTime            string                `json:"ExpireTime"`
	InstanceNetworkType   string                `json:"InstanceNetworkType"`
	LockMode              string                `json:"LockMode"`
	LockReason            string                `json:"LockReason"`
	MutriORsignle         bool                  `json:"MutriORsignle"`
	PayType               string                `json:"PayType"`
	ReadOnlyDBInstanceIDS ReadOnlyDBInstanceIDS `json:"ReadOnlyDBInstanceIds"`
	RegionID              string                `json:"RegionId"`
	ResourceGroupID       string                `json:"ResourceGroupId"`
	TipsLevel             int64                 `json:"TipsLevel"`
	VSwitchID             string                `json:"VSwitchId"`
	VpcCloudInstanceID    string                `json:"VpcCloudInstanceId"`
	VpcID                 string                `json:"VpcId"`
	ZoneID                string                `json:"ZoneId"`
}

type ReadOnlyDBInstanceIDS struct {
	ReadOnlyDBInstanceID []any `json:"readOnlyDBInstanceId"`
}

type RdsSG struct {
	SecurityGroupId string `json:"SecurityGroupId"`
}

type RdsTde struct {
	Databases ReadOnlyDBInstanceIDS `json:"Databases"`
	RequestID string                `json:"RequestId"`
	TDEMode   string                `json:"TDEMode"`
	TDEStatus string                `json:"TDEStatus"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.RDSGraph, error) {
	original := &Rds{}
	resource := &model.RDSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.DBInstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "rds", original.Instance.DBInstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "rds"
	resource.Name = original.Instance.DBInstanceDescription

	resource.ConnectionAddress = original.Instance.ConnectionString
	resource.Engine = original.Instance.Engine
	resource.EngineVersion = original.Instance.EngineVersion
	resource.PublicAllowed = original.Instance.DBInstanceNetType != "Intranet"
	resource.TDEEnabled = lo.ToPtr(strings.EqualFold(original.Tde.TDEStatus, "Enabled"))
	resource.IpWhiteList = lo.Flatten(
		lo.Map(original.IPWhiteList, func(ip IPWhiteList, _ int) []string {
			return lo.Map(strings.Split(ip.SecurityIPList, ","), func(ip string, _ int) string {
				return provider_utils.FormatCIDR(ip)
			})
		}),
	)
	resource.BackupAvailable = len(original.BackupList) > 0
	_, foundAuto := lo.Find(original.BackupList, func(backup BackupList) bool {
		return strings.EqualFold(backup.BackupType, "automated")
	})
	resource.BackupMethod = lo.Ternary(foundAuto, "auto", "manual")
	lastBackupTime := lo.MaxBy(original.BackupList, func(a, b BackupList) bool {
		return a.BackupEndTime > b.BackupEndTime
	})
	backuptime, err := time.Parse(time.RFC3339, lastBackupTime.BackupEndTime)
	if err != nil {
		resource.LastBackupTime = 0
	} else {
		resource.LastBackupTime = backuptime.UnixMilli()
	}
	resource.LogFileExists = original.AuditEnabled

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcID),
			TargetUID: resource.UID,
		},
	})

	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.VSwitchID),
			TargetUID: resource.UID,
		},
	})

	resource.SG = append(resource.SG, lo.Map(original.SecurityGroups, func(sg RdsSG, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", sg.SecurityGroupId),
				TargetUID: resource.UID,
			},
		}
	})...)

	resource.Accounts = lo.Map(original.Accounts, func(account RdsAccount, _ int) *model.RDSAccountGraph {
		return &model.RDSAccountGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "rds-account", account.AccountName),
				TargetUID: resource.UID,
			},
			Name:        account.AccountName,
			Enabled:     strings.EqualFold(account.AccountStatus, "available"),
			Class:       lo.Ternary(strings.EqualFold(account.AccountType, "super"), "admin", "user"),
			Description: account.AccountDescription,
		}
	})

	return resource, nil
}
