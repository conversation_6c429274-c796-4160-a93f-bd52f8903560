package nas

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nas"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "nas_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nas_list"] = append(resourceData["nas_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["nas_acl_rule_list"] = append(resourceData["nas_acl_rule_list"],
			utils.GenParamsFromStructSlice(resource.ACLRule)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, nasSchema, resourceData["nas_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, nasACLRuleSchema, resourceData["nas_acl_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type NAS struct {
	ACLRules []ACLRule `json:"aclRules"`
	NAS      NASClass  `json:"nas"`
}

type ACLRule struct {
	AccessGroupName  string `json:"AccessGroupName"`
	AccessRuleID     string `json:"AccessRuleId"`
	FileSystemType   string `json:"FileSystemType"`
	Ipv6SourceCIDRIP string `json:"Ipv6SourceCidrIp"`
	Priority         int    `json:"Priority"`
	RWAccess         string `json:"RWAccess"`
	RegionID         string `json:"RegionId"`
	SourceCIDRIP     string `json:"SourceCidrIp"`
	UserAccess       string `json:"UserAccess"`
}

type NASClass struct {
	AccessPointCount     string            `json:"AccessPointCount"`
	AutoSnapshotPolicyID string            `json:"AutoSnapshotPolicyId"`
	Capacity             int64             `json:"Capacity"`
	ChargeType           string            `json:"ChargeType"`
	CreateTime           string            `json:"CreateTime"`
	Description          string            `json:"Description"`
	EncryptType          int64             `json:"EncryptType"`
	ExpiredTime          string            `json:"ExpiredTime"`
	FileSystemID         string            `json:"FileSystemId"`
	FileSystemType       string            `json:"FileSystemType"`
	KMSKeyID             string            `json:"KMSKeyId"`
	LDAP                 LDAP              `json:"Ldap"`
	MeteredArchiveSize   int64             `json:"MeteredArchiveSize"`
	MeteredIASize        int64             `json:"MeteredIASize"`
	MeteredSize          int64             `json:"MeteredSize"`
	MountTargets         MountTargets      `json:"MountTargets"`
	Options              LDAP              `json:"Options"`
	Packages             LDAP              `json:"Packages"`
	ProtocolType         string            `json:"ProtocolType"`
	RegionID             string            `json:"RegionId"`
	ResourceGroupID      string            `json:"ResourceGroupId"`
	Status               string            `json:"Status"`
	StorageType          string            `json:"StorageType"`
	SupportedFeatures    SupportedFeatures `json:"SupportedFeatures"`
	Tags                 NasTags           `json:"Tags"`
	VpcID                string            `json:"VpcId"`
	ZoneID               string            `json:"ZoneId"`
}

type LDAP struct {
}

type MountTargets struct {
	MountTarget []MountTarget `json:"MountTarget"`
}

type MountTarget struct {
	AccessGroupName   string `json:"AccessGroupName"`
	ClientMasterNodes LDAP   `json:"ClientMasterNodes"`
	MountTargetDomain string `json:"MountTargetDomain"`
	NetworkType       string `json:"NetworkType"`
	Status            string `json:"Status"`
	Tags              LDAP   `json:"Tags"`
	VpcID             string `json:"VpcId"`
	VswID             string `json:"VswId"`
}

type SupportedFeatures struct {
	SupportedFeature []string `json:"SupportedFeature"`
}

type NasTags struct {
	Tag []NasTag `json:"Tag"`
}

type NasTag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NASGraph, error) {
	original := &NAS{}
	resource := &model.NASGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NAS.FileSystemID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nas", original.NAS.FileSystemID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nas"
	resource.Name = original.NAS.Description
	resource.Description = original.NAS.Description
	resource.OriginalLabels = lo.Map(original.NAS.Tags.Tag,
		func(e NasTag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.Status = lo.Ternary(strings.EqualFold(original.NAS.Status, "running"), "on", "off")
	resource.Encrypt = original.NAS.EncryptType != 0
	resource.Class = strings.ToLower(original.NAS.ProtocolType)
	resource.Domains = lo.Map(original.NAS.MountTargets.MountTarget, func(mountTarget MountTarget, _ int) string { return mountTarget.MountTargetDomain })
	resource.VPC = lo.Map(original.NAS.MountTargets.MountTarget, func(mountTarget MountTarget, _ int) *model.VPCGraph {
		return &model.VPCGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "vpc", mountTarget.VpcID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.Subnet = lo.Map(original.NAS.MountTargets.MountTarget, func(mountTarget MountTarget, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", mountTarget.VswID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.ACLRule = lo.Map(original.ACLRules, func(aclRule ACLRule, _ int) *model.NasACLRuleGraph {
		return &model.NasACLRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "nas-acl-rule", aclRule.AccessRuleID),
				TargetUID: resource.UID,
			},
			AclID:      aclRule.AccessRuleID,
			Priority:   aclRule.Priority,
			SourceCidr: lo.CoalesceOrEmpty(aclRule.SourceCIDRIP, aclRule.Ipv6SourceCIDRIP),
			RWAccess:   aclRule.RWAccess,
			UserAccess: aclRule.UserAccess,
		}
	})

	return resource, nil
}
