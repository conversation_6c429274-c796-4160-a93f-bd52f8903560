package ak

import "AssetStandardizer/graph"

var akSchema = graph.NodeSchema{
	Label: "AccessKey",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"enabled":            {Name: "enabled"},
		"created_at":         {Name: "created_at"},
		"last_used_at":       {Name: "last_used_at"},
	},
}

var akUserSchema = graph.NodeSchema{
	Label: "IAMUser",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "AccessKey",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "HAS",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
