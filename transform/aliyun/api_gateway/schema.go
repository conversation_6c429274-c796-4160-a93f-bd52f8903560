package api_gateway

import "AssetStandardizer/graph"

var apiGatewaySchema = graph.NodeSchema{
	Label: "ApiGateway",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"status":             {Name: "status"},
		"https_policies":     {Name: "https_policies"},
	},
}

var apiInfoSchema = graph.NodeSchema{
	Label: "ApiInfo",
	Properties: map[string]graph.PropertyRef{
		"uid":         {Name: "uid"},
		"id":          {Name: "id"},
		"name":        {Name: "name"},
		"auth_type":   {Name: "auth_type"},
		"description": {Name: "description"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ApiGateway",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var apiGatewayAclSchema = graph.NodeSchema{
	Label: "ApiGatewayACL",
	Properties: map[string]graph.PropertyRef{
		"uid":        {Name: "uid"},
		"id":         {Name: "id"},
		"name":       {Name: "name"},
		"status":     {Name: "status"},
		"ip_version": {Name: "ip_version"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ApiGateway",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var apiGatewayAclRuleSchema = graph.NodeSchema{
	Label: "ApiGatewayACLRule",
	Properties: map[string]graph.PropertyRef{
		"uid":         {Name: "uid"},
		"ip":          {Name: "ip"},
		"description": {Name: "description"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "ApiGatewayACL",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
