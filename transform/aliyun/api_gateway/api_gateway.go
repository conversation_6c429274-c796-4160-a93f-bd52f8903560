package api_gateway

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "api_gateway"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "apiGateway_apiGateway_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["api_gateway_list"] = append(resourceData["api_gateway_list"], utils.GenParamsFromStruct(resource))

		resourceData["api_info_list"] = append(resourceData["api_info_list"],
			utils.GenParamsFromStructSlice(resource.ApiInfo)...,
		)
		resourceData["api_gateway_acl_list"] = append(resourceData["api_gateway_acl_list"],
			utils.GenParamsFromStructSlice(resource.Acl)...,
		)
		resourceData["api_gateway_acl_rule_list"] = append(resourceData["api_gateway_acl_rule_list"],
			lo.FlatMap(resource.Acl, func(e *model.ApiGatewayAclGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(e.ACLRule)
			})...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, apiGatewaySchema, resourceData["api_gateway_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, apiInfoSchema, resourceData["api_info_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, apiGatewayAclSchema, resourceData["api_gateway_acl_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, apiGatewayAclRuleSchema, resourceData["api_gateway_acl_rule_list"], map[string]any{"last_updated": "test"})
}

type APIGateway struct {
	APIGatewayInfo APIGatewayInfo `json:"apiGatewayInfo"`
	DetailedApis   []DetailedAPI  `json:"detailedApis"`
}

type APIGatewayInfo struct {
	Instance ApiGatewayInstance `json:"instance"`
	Ipv4ACL  APIACL             `json:"ipv4_acl"`
	Ipv6ACL  APIACL             `json:"ipv6_acl"`
}

type ApiGatewayInstance struct {
	ACLStatus             string         `json:"AclStatus"`
	DedicatedInstanceType string         `json:"DedicatedInstanceType"`
	EgressIpv6Enable      bool           `json:"EgressIpv6Enable"`
	HTTPSPolicies         string         `json:"HttpsPolicies"`
	IPV6ACLStatus         string         `json:"IPV6AclStatus"`
	InstanceCIDRBlock     string         `json:"InstanceCidrBlock"`
	InstanceID            string         `json:"InstanceId"`
	InstanceName          string         `json:"InstanceName"`
	InstanceRpsLimit      int64          `json:"InstanceRpsLimit"`
	InstanceType          string         `json:"InstanceType"`
	InternetEgressAddress string         `json:"InternetEgressAddress"`
	MaintainEndTime       string         `json:"MaintainEndTime"`
	MaintainStartTime     string         `json:"MaintainStartTime"`
	PrivateDNSList        PrivateDNSList `json:"PrivateDnsList"`
	Status                string         `json:"Status"`
	SupportIpv6           bool           `json:"SupportIpv6"`
	VpcEgressAddress      string         `json:"VpcEgressAddress"`
	VpcIntranetEnable     bool           `json:"VpcIntranetEnable"`
	VpcSlbIntranetEnable  bool           `json:"VpcSlbIntranetEnable"`
}

type PrivateDNSList struct {
}

type APIACL struct {
	ACLID            string       `json:"AclId"`
	RequestID        string       `json:"RequestId"`
	ACLName          string       `json:"AclName"`
	ACLEntrys        ApiACLEntrys `json:"AclEntrys"`
	AddressIPVersion string       `json:"AddressIPVersion"`
}

type ApiACLEntrys struct {
	ACLEntry []ApiACLEntry `json:"AclEntry"`
}

type ApiACLEntry struct {
	ACLEntryIP      string `json:"AclEntryIp"`
	ACLEntryComment string `json:"AclEntryComment"`
}

type DetailedAPI struct {
	AllowSignatureMethod string         `json:"AllowSignatureMethod"`
	APIID                string         `json:"ApiId"`
	APIName              string         `json:"ApiName"`
	AppCodeAuthType      string         `json:"AppCodeAuthType"`
	AuthType             string         `json:"AuthType"`
	BackendEnable        bool           `json:"BackendEnable"`
	CreatedTime          string         `json:"CreatedTime"`
	DeployedInfos        DeployedInfos  `json:"DeployedInfos"`
	Description          string         `json:"Description"`
	DisableInternet      bool           `json:"DisableInternet"`
	ErrorCodeSamples     PrivateDNSList `json:"ErrorCodeSamples"`
	FailResultSample     string         `json:"FailResultSample"`
	ForceNonceCheck      bool           `json:"ForceNonceCheck"`
	GroupID              string         `json:"GroupId"`
	GroupName            string         `json:"GroupName"`
	Mock                 string         `json:"Mock"`
	MockResult           string         `json:"MockResult"`
	ModifiedTime         string         `json:"ModifiedTime"`
	RegionID             string         `json:"RegionId"`
	RequestConfig        RequestConfig  `json:"RequestConfig"`
	RequestID            string         `json:"RequestId"`
	ResultSample         string         `json:"ResultSample"`
	ResultType           string         `json:"ResultType"`
	ServiceConfig        ServiceConfig  `json:"ServiceConfig"`
	ServiceParameters    PrivateDNSList `json:"ServiceParameters"`
	Visibility           string         `json:"Visibility"`
	WebSocketAPIType     string         `json:"WebSocketApiType"`
}

type DeployedInfos struct {
	DeployedInfo []DeployedInfo `json:"DeployedInfo"`
}

type DeployedInfo struct {
	DeployedStatus   string  `json:"DeployedStatus"`
	EffectiveVersion *string `json:"EffectiveVersion,omitempty"`
	StageName        string  `json:"StageName"`
}

type RequestConfig struct {
	BodyFormat          string `json:"BodyFormat"`
	PostBodyDescription string `json:"PostBodyDescription"`
	RequestHTTPMethod   string `json:"RequestHttpMethod"`
	RequestMode         string `json:"RequestMode"`
	RequestPath         string `json:"RequestPath"`
	RequestProtocol     string `json:"RequestProtocol"`
}

type ServiceConfig struct {
	ContentTypeCatagory string         `json:"ContentTypeCatagory"`
	ContentTypeValue    string         `json:"ContentTypeValue"`
	Mock                string         `json:"Mock"`
	MockHeaders         PrivateDNSList `json:"MockHeaders"`
	MockResult          string         `json:"MockResult"`
	ServiceAddress      string         `json:"ServiceAddress"`
	ServiceHTTPMethod   string         `json:"ServiceHttpMethod"`
	ServicePath         string         `json:"ServicePath"`
	ServiceProtocol     string         `json:"ServiceProtocol"`
	ServiceTimeout      int64          `json:"ServiceTimeout"`
	ServiceVpcEnable    string         `json:"ServiceVpcEnable"`
	VpcConfig           VpcConfig      `json:"VpcConfig"`
}

type VpcConfig struct {
	InstanceID string `json:"InstanceId"`
	Name       string `json:"Name"`
	Port       int64  `json:"Port"`
	VpcID      string `json:"VpcId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ApiGatewayGraph, error) {
	original := &APIGateway{}
	resource := &model.ApiGatewayGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.APIGatewayInfo.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "api-gateway", original.APIGatewayInfo.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "api-gateway"
	resource.Name = original.APIGatewayInfo.Instance.InstanceName

	resource.Status = original.APIGatewayInfo.Instance.Status
	resource.HTTPSPolicies = strings.Split(original.APIGatewayInfo.Instance.HTTPSPolicies, ",")
	resource.ApiInfo = lo.Map(original.DetailedApis, func(api DetailedAPI, _ int) *model.ApiInfoGraph {
		return &model.ApiInfoGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "api-info", api.APIID),
				TargetUID: resource.UID,
			},
			ID:          api.APIID,
			Name:        api.APIName,
			AuthType:    api.AuthType,
			Description: api.Description,
		}
	})
	ipv4AclUID := utils.GenerateUID(resource.Provider, "api-gateway-acl", original.APIGatewayInfo.Ipv4ACL.ACLID)
	resource.Acl = append(resource.Acl,
		&model.ApiGatewayAclGraph{
			BaseNode: model.BaseNode{
				UID:       ipv4AclUID,
				TargetUID: resource.UID,
			},
			ID:        original.APIGatewayInfo.Ipv4ACL.ACLID,
			Name:      original.APIGatewayInfo.Ipv4ACL.ACLName,
			Status:    strings.ToLower(original.APIGatewayInfo.Instance.ACLStatus),
			IPVersion: 4,
			ACLRule: lo.Map(original.APIGatewayInfo.Ipv4ACL.ACLEntrys.ACLEntry, func(ACLEntry ApiACLEntry, _ int) *model.ApiGatewayACLRuleGraph {
				return &model.ApiGatewayACLRuleGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "api-gateway-acl-rule", original.APIGatewayInfo.Ipv4ACL.ACLID+ACLEntry.ACLEntryIP),
						TargetUID: ipv4AclUID,
					},
					IP:          provider_utils.FormatCIDR(ACLEntry.ACLEntryIP),
					Description: ACLEntry.ACLEntryComment,
				}
			}),
		})

	ipv6AclUID := utils.GenerateUID(resource.Provider, "api-gateway-acl", original.APIGatewayInfo.Ipv6ACL.ACLID)
	resource.Acl = append(resource.Acl,
		&model.ApiGatewayAclGraph{
			BaseNode: model.BaseNode{
				UID:       ipv6AclUID,
				TargetUID: resource.UID,
			},
			ID:        original.APIGatewayInfo.Ipv6ACL.ACLID,
			Name:      original.APIGatewayInfo.Ipv6ACL.ACLName,
			Status:    strings.ToLower(original.APIGatewayInfo.Instance.IPV6ACLStatus),
			IPVersion: 6,
			ACLRule: lo.Map(original.APIGatewayInfo.Ipv6ACL.ACLEntrys.ACLEntry, func(ACLEntry ApiACLEntry, _ int) *model.ApiGatewayACLRuleGraph {
				return &model.ApiGatewayACLRuleGraph{
					BaseNode: model.BaseNode{
						UID:       utils.GenerateUID(resource.Provider, "api-gateway-acl-rule", original.APIGatewayInfo.Ipv6ACL.ACLID+ACLEntry.ACLEntryIP),
						TargetUID: ipv6AclUID,
					},
					IP:          provider_utils.FormatCIDR(ACLEntry.ACLEntryIP),
					Description: ACLEntry.ACLEntryComment,
				}
			}),
		})

	return resource, nil
}
