package vpc

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "vpc"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_vpc_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["vpc_list"] = append(resourceData["vpc_list"], utils.GenParamsFromStruct(resource))

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
}

type Vpc struct {
	Vpc VpcClass `json:"vpc"`
}

type VpcClass struct {
	CenStatus           string              `json:"CenStatus"`
	CIDRBlock           string              `json:"CidrBlock"`
	CreationTime        string              `json:"CreationTime"`
	Description         string              `json:"Description"`
	EnabledIpv6         bool                `json:"EnabledIpv6"`
	Ipv6CIDRBlock       string              `json:"Ipv6CidrBlock"`
	IsDefault           bool                `json:"IsDefault"`
	NatGatewayIDS       NatGatewayIDS       `json:"NatGatewayIds"`
	OwnerID             int64               `json:"OwnerId"`
	RegionID            string              `json:"RegionId"`
	ResourceGroupID     string              `json:"ResourceGroupId"`
	RouterTableIDS      RouterTableIDS      `json:"RouterTableIds"`
	SecondaryCIDRBlocks SecondaryCIDRBlocks `json:"SecondaryCidrBlocks"`
	Status              string              `json:"Status"`
	UserCidrs           UserCidrs           `json:"UserCidrs"`
	VRouterID           string              `json:"VRouterId"`
	VSwitchIDS          VSwitchIDS          `json:"VSwitchIds"`
	VpcID               string              `json:"VpcId"`
	VpcName             string              `json:"VpcName"`
}

type NatGatewayIDS struct {
	NatGatewayIDS []string `json:"NatGatewayIds"`
}

type RouterTableIDS struct {
	RouterTableIDS []string `json:"RouterTableIds"`
}

type SecondaryCIDRBlocks struct {
	SecondaryCIDRBlock []string `json:"SecondaryCidrBlock"`
}

type UserCidrs struct {
}

type VSwitchIDS struct {
	VSwitchID []string `json:"VSwitchId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.VPCGraph, error) {
	original := &Vpc{}
	resource := &model.VPCGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Vpc.VpcID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "vpc", original.Vpc.VpcID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "vpc"
	resource.Name = original.Vpc.VpcName
	resource.Description = original.Vpc.Description

	resource.CIDR = original.Vpc.CIDRBlock
	resource.CIDRv6 = original.Vpc.Ipv6CIDRBlock
	resource.IsDefault = original.Vpc.IsDefault
	resource.SecondaryCIDRs = original.Vpc.SecondaryCIDRBlocks.SecondaryCIDRBlock
	resource.Subnet = lo.Map(original.Vpc.VSwitchIDS.VSwitchID, func(v string, _ int) *model.SubnetGraph {
		return &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", v),
				TargetUID: resource.UID,
			},
		}
	})

	return resource, nil
}
