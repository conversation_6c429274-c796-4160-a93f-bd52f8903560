package aliyun

import (
	"AssetStandardizer/model"
	"AssetStandardizer/transform/aliyun/ak"
	"AssetStandardizer/transform/aliyun/api_gateway"
	"AssetStandardizer/transform/aliyun/audit_log"
	"AssetStandardizer/transform/aliyun/bucket"
	"AssetStandardizer/transform/aliyun/cdn"
	"AssetStandardizer/transform/aliyun/cen"
	"AssetStandardizer/transform/aliyun/ebs"
	"AssetStandardizer/transform/aliyun/ecs"
	"AssetStandardizer/transform/aliyun/eip"
	"AssetStandardizer/transform/aliyun/elasticsearch"
	"AssetStandardizer/transform/aliyun/eni"
	"AssetStandardizer/transform/aliyun/function"
	"AssetStandardizer/transform/aliyun/k8s"
	"AssetStandardizer/transform/aliyun/lb"
	"AssetStandardizer/transform/aliyun/lb_listener"
	"AssetStandardizer/transform/aliyun/nas"
	"AssetStandardizer/transform/aliyun/nat"
	"AssetStandardizer/transform/aliyun/peer_connection"
	"AssetStandardizer/transform/aliyun/policy"
	"AssetStandardizer/transform/aliyun/rds"
	"AssetStandardizer/transform/aliyun/redis"
	"AssetStandardizer/transform/aliyun/role"
	"AssetStandardizer/transform/aliyun/security_group"
	"AssetStandardizer/transform/aliyun/sls"
	"AssetStandardizer/transform/aliyun/subnet"
	"AssetStandardizer/transform/aliyun/user"
	"AssetStandardizer/transform/aliyun/vpc"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

const ProviderID = "aliyun"

var AssetMessageChan = make(chan *model.AssetMessage, 100)
var AssetTypeMap = map[string]chan *model.AssetMessage{}

func init() {
	resourceServices := []model.ResourceService{
		eip.NewResourceService(),
		ak.NewResourceService(),
		user.NewResourceService(),
		policy.NewResourceService(),
		role.NewResourceService(),
		ecs.NewResourceService(),
		security_group.NewResourceService(),
		vpc.NewResourceService(),
		subnet.NewResourceService(),
		lb.NewSLBService(),
		lb.NewNLBService(),
		lb_listener.NewSLBListenerService(),
		lb_listener.NewNLBListenerService(),
		eni.NewResourceService(),
		nat.NewResourceService(),
		bucket.NewResourceService(),
		peer_connection.NewResourceService(),
		cdn.NewResourceService(),
		redis.NewResourceService(),
		ebs.NewVolumeService(),
		ebs.NewImageService(),
		ebs.NewSnapshotService(),
		api_gateway.NewResourceService(),
		cen.NewResourceService(),
		rds.NewResourceService(),
		elasticsearch.NewResourceService(),
		function.NewResourceService(),
		nas.NewResourceService(),
		k8s.NewResourceService(),
		sls.NewResourceService(),
		audit_log.NewResourceService(),
	}

	for _, resourceService := range resourceServices {
		if err := resourceService.Validate(); err != nil {
			logger.Errorf("validate resource service failed: %s", err)
			continue
		}
		resourceService.Start()
		AssetTypeMap[resourceService.GetMessageType()] = resourceService.GetAssetMsgCh()
	}
	go dispatch()
}

func dispatch() {
	for assetMsg := range AssetMessageChan {
		if assetMsgChan, ok := AssetTypeMap[assetMsg.Type]; ok {
			assetMsgChan <- assetMsg
		} else {
			logger.Errorf("unknown type: %s", assetMsg.Type)
			continue
		}
	}
}
