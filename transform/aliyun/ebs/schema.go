package ebs

import "AssetStandardizer/graph"

var imageSchema = graph.NodeSchema{
	Label: "EBS",
	Properties: map[string]graph.PropertyRef{
		"uid":                   {Name: "uid"},
		"provider":              {Name: "provider"},
		"original_id":           {Name: "original_id"},
		"transformed_object":    {Name: "transformed_object"},
		"region":                {Name: "region"},
		"last_seen":             {Name: "last_seen"},
		"description":           {Name: "description"},
		"kind":                  {Name: "kind"},
		"name":                  {Name: "name"},
		"status":                {Name: "status"},
		"class":                 {Name: "class"},
		"encrypted":             {Name: "encrypted"},
		"auto_snapshot_enabled": {Name: "auto_snapshot_enabled"},
	},
}

var volumeSchema = graph.NodeSchema{
	Label: "EBS",
	Properties: map[string]graph.PropertyRef{
		"uid":                   {Name: "uid"},
		"provider":              {Name: "provider"},
		"original_id":           {Name: "original_id"},
		"transformed_object":    {Name: "transformed_object"},
		"region":                {Name: "region"},
		"last_seen":             {Name: "last_seen"},
		"description":           {Name: "description"},
		"kind":                  {Name: "kind"},
		"name":                  {Name: "name"},
		"status":                {Name: "status"},
		"class":                 {Name: "class"},
		"encrypted":             {Name: "encrypted"},
		"auto_snapshot_enabled": {Name: "auto_snapshot_enabled"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "EBS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "CREATED_FROM",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var ecsVolumeSchema = graph.NodeSchema{
	Label: "ECS",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "EBS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ATTACHED_TO",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var snapshotSchema = graph.NodeSchema{
	Label: "EBS",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"status":             {Name: "status"},
		"class":              {Name: "class"},
		"encrypted":          {Name: "encrypted"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "EBS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "CREATED_FROM",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var labelEBSSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"key":          {Name: "key"},
		"value":        {Name: "value"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "EBS",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LABEL",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
