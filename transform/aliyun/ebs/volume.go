package ebs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var volumeLogger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "volume"})

func NewVolumeService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_disk_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformVolume,
		UpdateResources: updateVolumes,
	}
}

func transformVolume(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseVolume(assetMsg)
		if err != nil {
			volumeLogger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["volume_list"] = append(resourceData["volume_list"], utils.GenParamsFromStruct(resource))

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["image_list"] = append(resourceData["image_list"],
			utils.GenParamsFromStructSlice(resource.Image)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateVolumes(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, volumeSchema, resourceData["volume_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, imageSchema, resourceData["image_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelEBSSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsVolumeSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
}

type Disk struct {
	Disk DiskClass `json:"disk"`
}

type DiskClass struct {
	AttachedTime                  string         `json:"AttachedTime"`
	Attachments                   Attachments    `json:"Attachments"`
	AutoSnapshotPolicyID          string         `json:"AutoSnapshotPolicyId"`
	BdfID                         string         `json:"BdfId"`
	Category                      string         `json:"Category"`
	CreationTime                  string         `json:"CreationTime"`
	DeleteAutoSnapshot            bool           `json:"DeleteAutoSnapshot"`
	DeleteWithInstance            bool           `json:"DeleteWithInstance"`
	Description                   string         `json:"Description"`
	DetachedTime                  string         `json:"DetachedTime"`
	Device                        string         `json:"Device"`
	DiskChargeType                string         `json:"DiskChargeType"`
	DiskID                        string         `json:"DiskId"`
	DiskName                      string         `json:"DiskName"`
	EnableAutoSnapshot            bool           `json:"EnableAutoSnapshot"`
	EnableAutomatedSnapshotPolicy bool           `json:"EnableAutomatedSnapshotPolicy"`
	Encrypted                     bool           `json:"Encrypted"`
	ExpiredTime                   string         `json:"ExpiredTime"`
	Iops                          int64          `json:"IOPS"`
	ImageID                       string         `json:"ImageId"`
	InstanceID                    string         `json:"InstanceId"`
	KMSKeyID                      string         `json:"KMSKeyId"`
	MultiAttach                   string         `json:"MultiAttach"`
	OperationLocks                OperationLocks `json:"OperationLocks"`
	PerformanceLevel              string         `json:"PerformanceLevel"`
	Placement                     OperationLocks `json:"Placement"`
	Portable                      bool           `json:"Portable"`
	ProductCode                   string         `json:"ProductCode"`
	RegionID                      string         `json:"RegionId"`
	ResourceGroupID               string         `json:"ResourceGroupId"`
	SerialNumber                  string         `json:"SerialNumber"`
	Size                          int64          `json:"Size"`
	SourceSnapshotID              string         `json:"SourceSnapshotId"`
	Status                        string         `json:"Status"`
	StorageClusterID              string         `json:"StorageClusterId"`
	StorageSetID                  string         `json:"StorageSetId"`
	Tags                          Tags           `json:"Tags"`
	Throughput                    int64          `json:"Throughput"`
	Type                          string         `json:"Type"`
	ZoneID                        string         `json:"ZoneId"`
}

type OperationLocks struct {
	LockReason []any `json:"lockReason"`
}

type Attachments struct {
	Attachment []Attachment `json:"Attachment"`
}

type Attachment struct {
	AttachedTime string `json:"AttachedTime"`
	Device       string `json:"Device"`
	InstanceID   string `json:"InstanceId"`
}

func parseVolume(assetMsg *model.AssetMessage) (*model.EBSGraph, error) {
	original := &Disk{}
	resource := &model.EBSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Disk.DiskID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ebs", original.Disk.DiskID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "ebs"
	resource.Name = original.Disk.DiskName
	resource.Description = original.Disk.Description
	resource.OriginalLabels = lo.Map(original.Disk.Tags.Tag,
		func(tag Tag, _ int) *model.KVGraph { return utils.NewLabel(tag.Key, tag.Value, resource.UID) },
	)

	resource.Class = "volume"
	resource.Encrypted = original.Disk.Encrypted
	resource.Status = lo.Ternary(strings.EqualFold(original.Disk.Status, "in_use"), "inuse", "available")
	resource.AutoSnapshotEnabled = lo.ToPtr(original.Disk.EnableAutoSnapshot && original.Disk.EnableAutomatedSnapshotPolicy)
	resource.Image = append(resource.Image, &model.EBSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ebs", original.Disk.ImageID),
			TargetUID: resource.UID,
		},
	})
	resource.ECS = lo.Map(original.Disk.Attachments.Attachment,
		func(e Attachment, _ int) *model.ECSGraph {
			return &model.ECSGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "ecs", e.InstanceID),
					TargetUID: resource.UID,
				},
			}
		},
	)

	return resource, nil
}
