package ecs

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "ecs"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["ecs_list"] = append(resourceData["ecs_list"], utils.GenParamsFromStruct(resource))

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["eni_list"] = append(resourceData["eni_list"],
			utils.GenParamsFromStructSlice(resource.ENI)...,
		)

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, ecsSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
}

type ECS struct {
	Instance EcsInstance `json:"instance"`
}

type EcsInstance struct {
	AutoReleaseTime            string                     `json:"AutoReleaseTime"`
	ClusterID                  string                     `json:"ClusterId"`
	CPU                        int64                      `json:"Cpu"`
	CPUOptions                 CPUOptions                 `json:"CpuOptions"`
	CreationTime               string                     `json:"CreationTime"`
	CreditSpecification        string                     `json:"CreditSpecification"`
	DedicatedHostAttribute     DedicatedHostAttribute     `json:"DedicatedHostAttribute"`
	DedicatedInstanceAttribute DedicatedInstanceAttribute `json:"DedicatedInstanceAttribute"`
	DeletionProtection         bool                       `json:"DeletionProtection"`
	DeploymentSetID            string                     `json:"DeploymentSetId"`
	Description                string                     `json:"Description"`
	DeviceAvailable            bool                       `json:"DeviceAvailable"`
	EcsCapacityReservationAttr EcsCapacityReservationAttr `json:"EcsCapacityReservationAttr"`
	EipAddress                 EipAddress                 `json:"EipAddress"`
	ExpiredTime                string                     `json:"ExpiredTime"`
	GPUAmount                  int64                      `json:"GPUAmount"`
	GPUSpec                    string                     `json:"GPUSpec"`
	HibernationOptions         HibernationOptions         `json:"HibernationOptions"`
	HostName                   string                     `json:"HostName"`
	ImageID                    string                     `json:"ImageId"`
	ImageOptions               ImageOptions               `json:"ImageOptions"`
	InnerIPAddress             ImageOptions               `json:"InnerIpAddress"`
	InstanceChargeType         string                     `json:"InstanceChargeType"`
	InstanceID                 string                     `json:"InstanceId"`
	InstanceName               string                     `json:"InstanceName"`
	InstanceNetworkType        string                     `json:"InstanceNetworkType"`
	InstanceType               string                     `json:"InstanceType"`
	InstanceTypeFamily         string                     `json:"InstanceTypeFamily"`
	InternetChargeType         string                     `json:"InternetChargeType"`
	InternetMaxBandwidthIn     int64                      `json:"InternetMaxBandwidthIn"`
	InternetMaxBandwidthOut    int64                      `json:"InternetMaxBandwidthOut"`
	IoOptimized                bool                       `json:"IoOptimized"`
	KeyPairName                string                     `json:"KeyPairName"`
	Memory                     int64                      `json:"Memory"`
	MetadataOptions            MetadataOptions            `json:"MetadataOptions"`
	NetworkInterfaces          NetworkInterfaces          `json:"NetworkInterfaces"`
	OSName                     string                     `json:"OSName"`
	OSNameEn                   string                     `json:"OSNameEn"`
	OSType                     string                     `json:"OSType"`
	OperationLocks             ImageOptions               `json:"OperationLocks"`
	PrivateDNSNameOptions      ImageOptions               `json:"PrivateDnsNameOptions"`
	PublicIPAddress            ImageOptions               `json:"PublicIpAddress"`
	Recyclable                 bool                       `json:"Recyclable"`
	RegionID                   string                     `json:"RegionId"`
	ResourceGroupID            string                     `json:"ResourceGroupId"`
	SaleCycle                  string                     `json:"SaleCycle"`
	SecurityGroupIDS           SecurityGroupIDS           `json:"SecurityGroupIds"`
	SerialNumber               string                     `json:"SerialNumber"`
	SpotPriceLimit             int64                      `json:"SpotPriceLimit"`
	SpotStrategy               string                     `json:"SpotStrategy"`
	StartTime                  string                     `json:"StartTime"`
	Status                     string                     `json:"Status"`
	StoppedMode                string                     `json:"StoppedMode"`
	Tags                       Tags                       `json:"Tags"`
	VLANID                     string                     `json:"VlanId"`
	VpcAttributes              VpcAttributes              `json:"VpcAttributes"`
	ZoneID                     string                     `json:"ZoneId"`
}

type CPUOptions struct {
	CoreCount      int64  `json:"CoreCount"`
	NUMA           string `json:"Numa"`
	ThreadsPerCore int64  `json:"ThreadsPerCore"`
}

type DedicatedHostAttribute struct {
	DedicatedHostClusterID string `json:"DedicatedHostClusterId"`
	DedicatedHostID        string `json:"DedicatedHostId"`
	DedicatedHostName      string `json:"DedicatedHostName"`
}

type DedicatedInstanceAttribute struct {
	Affinity string `json:"Affinity"`
	Tenancy  string `json:"Tenancy"`
}

type EcsCapacityReservationAttr struct {
	CapacityReservationID         string `json:"CapacityReservationId"`
	CapacityReservationPreference string `json:"CapacityReservationPreference"`
}

type EipAddress struct {
	AllocationID       string `json:"AllocationId"`
	InternetChargeType string `json:"InternetChargeType"`
	IPAddress          string `json:"IpAddress"`
}

type HibernationOptions struct {
	Configured bool `json:"Configured"`
}

type ImageOptions struct {
	LoginAsNonRoot any `json:"loginAsNonRoot"`
}

type MetadataOptions struct {
	HTTPEndpoint string `json:"HttpEndpoint"`
	HTTPTokens   string `json:"HttpTokens"`
}

type NetworkInterfaces struct {
	NetworkInterface []NetworkInterface `json:"NetworkInterface"`
}

type NetworkInterface struct {
	MACAddress         string        `json:"MacAddress"`
	NetworkInterfaceID string        `json:"NetworkInterfaceId"`
	PrimaryIPAddress   string        `json:"PrimaryIpAddress"`
	PrivateIPSets      PrivateIPSets `json:"PrivateIpSets"`
	Type               string        `json:"Type"`
}

type PrivateIPSets struct {
	PrivateIPSet []PrivateIPSet `json:"PrivateIpSet"`
}

type PrivateIPSet struct {
	Primary          bool   `json:"Primary"`
	PrivateIPAddress string `json:"PrivateIpAddress"`
}

type SecurityGroupIDS struct {
	SecurityGroupID []string `json:"SecurityGroupId"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type OperationLocks struct {
	LockReason []any `json:"lockReason"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

type VpcAttributes struct {
	NatIPAddress     string           `json:"NatIpAddress"`
	PrivateIPAddress PrivateIPAddress `json:"PrivateIpAddress"`
	VSwitchID        string           `json:"VSwitchId"`
	VpcID            string           `json:"VpcId"`
}

type PrivateIPAddress struct {
	IPAddress []string `json:"IpAddress"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ECSGraph, error) {
	original := &ECS{}
	resource := &model.ECSGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "ecs", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Instance.Description
	resource.Kind = "ecs"
	resource.Name = original.Instance.InstanceName
	resource.OriginalLabels = lo.Map(original.Instance.Tags.Tag,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Hostname = original.Instance.HostName
	resource.Class = "cvm"
	resource.OSName = original.Instance.OSName
	osType := strings.ToLower(original.Instance.OSType)
	resource.OSType = lo.Ternary(strings.Contains(osType, "linux"), "linux", "windows")
	resource.Spec = original.Instance.InstanceType
	resource.Status = strings.ToLower(original.Instance.Status)
	resource.DeleteProtection = lo.ToPtr(original.Instance.DeletionProtection)

	primaryPrivateIP := lo.FilterMap(original.Instance.NetworkInterfaces.NetworkInterface, func(e NetworkInterface, _ int) (string, bool) {
		return e.PrimaryIPAddress, e.Type == "Primary"
	})
	if len(primaryPrivateIP) > 0 {
		resource.PrimaryPrivateIP = primaryPrivateIP[0]
	}
	resource.PrimaryPublicIP = original.Instance.EipAddress.IPAddress

	resource.PrivateIPList = lo.Uniq(append(lo.Map(original.Instance.NetworkInterfaces.NetworkInterface, func(e NetworkInterface, _ int) string { return e.PrimaryIPAddress }),
		lo.Flatten(lo.Map(original.Instance.NetworkInterfaces.NetworkInterface, func(e NetworkInterface, _ int) []string {
			return lo.Map(e.PrivateIPSets.PrivateIPSet, func(e PrivateIPSet, _ int) string { return e.PrivateIPAddress })
		}))...))
	if resource.PrimaryPublicIP != "" {
		resource.PublicIPList = []string{resource.PrimaryPublicIP}
	} else {
		resource.PublicIPList = []string{}
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.VpcAttributes.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.VpcAttributes.VSwitchID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG,
		lo.Map(original.Instance.SecurityGroupIDS.SecurityGroupID, func(e string, _ int) *model.SGGraph {
			return &model.SGGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "security-group", e),
					TargetUID: resource.UID,
				},
			}
		})...,
	)
	resource.ENI = append(resource.ENI,
		lo.Map(original.Instance.NetworkInterfaces.NetworkInterface, func(e NetworkInterface, _ int) *model.ENIGraph {
			return &model.ENIGraph{
				BaseNode: model.BaseNode{
					UID:       utils.GenerateUID(resource.Provider, "eni", e.NetworkInterfaceID),
					TargetUID: resource.UID,
				},
			}
		})...,
	)

	return resource, nil
}
