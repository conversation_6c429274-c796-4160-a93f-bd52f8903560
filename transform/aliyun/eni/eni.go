package eni

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "eni"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "ecs_eni_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	var resourceData = map[string][]map[string]any{}

	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["eni_list"] = append(resourceData["eni_list"], utils.GenParamsFromStruct(resource))

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["ecs_list"] = append(resourceData["ecs_list"],
			utils.GenParamsFromStructSlice(resource.ECS)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

	}

	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, eniSchema, resourceData["eni_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcEniSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetEniSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgEniSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ecsEniSchema, resourceData["ecs_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipEniSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})

}

type Eni struct {
	Eni EniClass `json:"eni"`
}

type EniClass struct {
	AssociatedPublicIP          AssociatedPublicIP `json:"AssociatedPublicIp"`
	Attachment                  Attachment         `json:"Attachment"`
	CreationTime                string             `json:"CreationTime"`
	DeleteOnRelease             bool               `json:"DeleteOnRelease"`
	InstanceID                  string             `json:"InstanceId"`
	Ipv4PrefixSets              Attachment         `json:"Ipv4PrefixSets"`
	Ipv6PrefixSets              Attachment         `json:"Ipv6PrefixSets"`
	Ipv6Sets                    Attachment         `json:"Ipv6Sets"`
	MACAddress                  string             `json:"MacAddress"`
	NetworkInterfaceID          string             `json:"NetworkInterfaceId"`
	NetworkInterfaceTrafficMode string             `json:"NetworkInterfaceTrafficMode"`
	OwnerID                     string             `json:"OwnerId"`
	PrivateIPAddress            string             `json:"PrivateIpAddress"`
	PrivateIPSets               EniPrivateIPSets   `json:"PrivateIpSets"`
	QueueNumber                 int64              `json:"QueueNumber,omitempty"`
	ResourceGroupID             string             `json:"ResourceGroupId"`
	SecurityGroupIDS            SecurityGroupIDS   `json:"SecurityGroupIds"`
	ServiceManaged              bool               `json:"ServiceManaged"`
	Status                      string             `json:"Status"`
	Type                        string             `json:"Type"`
	VSwitchID                   string             `json:"VSwitchId"`
	VpcID                       string             `json:"VpcId"`
	ZoneID                      string             `json:"ZoneId"`
	Description                 string             `json:"Description,omitempty"`
	NetworkInterfaceName        string             `json:"NetworkInterfaceName,omitempty"`
	ServiceID                   int64              `json:"ServiceID,omitempty"`
	SourceDestCheck             bool               `json:"SourceDestCheck,omitempty"`
}

type AssociatedPublicIP struct {
	PublicIPAddress string `json:"PublicIpAddress,omitempty"`
}

type EniPrivateIPSets struct {
	EniPrivateIPSet []EniPrivateIPSet `json:"PrivateIpSet"`
}

type EniPrivateIPSet struct {
	AssociatedPublicIP AssociatedPublicIP `json:"AssociatedPublicIp"`
	Primary            bool               `json:"Primary"`
	PrivateIPAddress   string             `json:"PrivateIpAddress"`
}

type Attachment struct {
	AttachedTime string `json:"AttachedTime"`
	Device       string `json:"Device"`
	InstanceID   string `json:"InstanceId"`
}

type SecurityGroupIDS struct {
	SecurityGroupID []string `json:"SecurityGroupId"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ENIGraph, error) {
	original := &Eni{}
	resource := &model.ENIGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Eni.NetworkInterfaceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(provider_utils.ProviderID, "eni", resource.OriginalID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Description = original.Eni.Description
	resource.Kind = "eni"
	resource.Name = original.Eni.NetworkInterfaceName

	resource.PrimaryIP = original.Eni.PrivateIPAddress
	resource.MacAddress = original.Eni.MACAddress
	resource.Status = lo.Ternary(strings.EqualFold(original.Eni.Status, "InUse"), "inuse", "available")
	resource.Class = lo.Ternary(strings.EqualFold(original.Eni.Type, "primary"), "primary", "secondary")
	resource.PrivateIPList = lo.Map(original.Eni.PrivateIPSets.EniPrivateIPSet, func(e EniPrivateIPSet, _ int) string { return e.PrivateIPAddress })
	resource.PublicIPList = lo.FilterMap(original.Eni.PrivateIPSets.EniPrivateIPSet, func(e EniPrivateIPSet, _ int) (string, bool) {
		return e.AssociatedPublicIP.PublicIPAddress, e.AssociatedPublicIP.PublicIPAddress != ""
	})

	resource.ECS = append(resource.ECS, &model.ECSGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "ecs", original.Eni.InstanceID),
			TargetUID: resource.UID,
		},
	})
	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Eni.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Eni.VSwitchID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG, lo.Map(original.Eni.SecurityGroupIDS.SecurityGroupID, func(e string, _ int) *model.SGGraph {
		return &model.SGGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "security-group", e),
				TargetUID: resource.UID,
			},
		}
	})...)
	return resource, nil
}
