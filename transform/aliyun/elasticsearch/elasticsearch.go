package elasticsearch

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "elasticsearch"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "es_instance_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["es_list"] = append(resourceData["es_list"], utils.GenParamsFromStruct(resource))

		resourceData["es_node_list"] = append(resourceData["es_node_list"],
			utils.GenParamsFromStructSlice(resource.Node)...,
		)

		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, esSchema, resourceData["es_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, esNodeSchema, resourceData["es_node_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
}

type Es struct {
	Instance EsInstance `json:"instance"`
	Nodes    []Node     `json:"nodes"`
}

type EsInstance struct {
	AdvancedDedicateMaster       bool                `json:"advancedDedicateMaster"`
	AdvancedSetting              AdvancedSetting     `json:"advancedSetting"`
	ArchType                     string              `json:"archType"`
	ClientNodeConfiguration      NodeConfiguration   `json:"clientNodeConfiguration"`
	CreatedAt                    string              `json:"createdAt"`
	DedicateMaster               bool                `json:"dedicateMaster"`
	Description                  string              `json:"description"`
	DictList                     []DictList          `json:"dictList"`
	Domain                       string              `json:"domain"`
	PublicDomain                 string              `json:"publicDomain"`
	ElasticDataNodeConfiguration NodeConfiguration   `json:"elasticDataNodeConfiguration"`
	EnableKibanaPrivateNetwork   bool                `json:"enableKibanaPrivateNetwork"`
	EnableKibanaPublicNetwork    bool                `json:"enableKibanaPublicNetwork"`
	EnablePublic                 bool                `json:"enablePublic"`
	EsConfig                     EsConfig            `json:"esConfig"`
	EsIPWhitelist                []string            `json:"esIPWhitelist"`
	EsVersion                    string              `json:"esVersion"`
	ExtendConfigs                []ExtendConfig      `json:"extendConfigs"`
	HaveClientNode               bool                `json:"haveClientNode"`
	HaveKibana                   bool                `json:"haveKibana"`
	InstanceCategory             string              `json:"instanceCategory"`
	InstanceID                   string              `json:"instanceId"`
	KibanaConfiguration          KibanaConfiguration `json:"kibanaConfiguration"`
	KibanaPrivateIPWhitelist     []string            `json:"kibanaPrivateIPWhitelist"`
	KibanaIPWhitelist            []string            `json:"kibanaIpWhitelist"`
	MasterConfiguration          MasterConfiguration `json:"masterConfiguration"`
	NetworkConfig                NetworkConfig       `json:"networkConfig"`
	NodeAmount                   int64               `json:"nodeAmount"`
	NodeSpec                     NodeSpec            `json:"nodeSpec"`
	PaymentType                  string              `json:"paymentType"`
	Port                         int64               `json:"port"`
	PrivateNetworkIPWhiteList    []string            `json:"privateNetworkIpWhiteList"`
	PublicIPWhitelist            []string            `json:"publicIpWhitelist"`
	Protocol                     string              `json:"protocol"`
	ResourceGroupID              string              `json:"resourceGroupId"`
	ServiceVpc                   bool                `json:"serviceVpc"`
	Status                       string              `json:"status"`
	Tags                         []Tag               `json:"tags"`
	UpdatedAt                    string              `json:"updatedAt"`
	VpcInstanceID                string              `json:"vpcInstanceId"`
	WarmNode                     bool                `json:"warmNode"`
	WarmNodeConfiguration        NodeConfiguration   `json:"warmNodeConfiguration"`
	ZoneCount                    int64               `json:"zoneCount"`
	ZoneInfos                    []ZoneInfo          `json:"zoneInfos"`
	KibanaDomain                 string              `json:"kibanaDomain"`
}

type AdvancedSetting struct {
	GcName string `json:"gcName"`
}

type NodeConfiguration struct {
}

type DictList struct {
	FileSize   int64  `json:"fileSize"`
	Name       string `json:"name"`
	SourceType string `json:"sourceType"`
	Type       string `json:"type"`
}

type EsConfig struct {
	ActionAutoCreateIndex         string `json:"action.auto_create_index"`
	ActionDestructiveRequiresName string `json:"action.destructive_requires_name"`
	XpackSecurityAuditEnabled     string `json:"xpack.security.audit.enabled"`
	XpackWatcherEnabled           string `json:"xpack.watcher.enabled"`
}

type ExtendConfig struct {
	ConfigType           string  `json:"configType"`
	Value                *string `json:"value,omitempty"`
	MaintainEndTime      *string `json:"maintainEndTime,omitempty"`
	MaintainStartTime    *string `json:"maintainStartTime,omitempty"`
	AliVersion           *string `json:"aliVersion,omitempty"`
	FollowClusterEnabled *bool   `json:"followClusterEnabled,omitempty"`
	HasArchivedIndex     *bool   `json:"hasArchivedIndex,omitempty"`
	OpenStoreEnable      *bool   `json:"openStoreEnable,omitempty"`
	StorageType          *string `json:"storageType,omitempty"`
	TotalSizeInMB        *int64  `json:"totalSizeInMb,omitempty"`
	Type                 *string `json:"type,omitempty"`
}

type KibanaConfiguration struct {
	Amount   int64  `json:"amount"`
	Disk     int64  `json:"disk"`
	Spec     string `json:"spec"`
	SpecInfo string `json:"specInfo"`
}

type MasterConfiguration struct {
	Amount   int64  `json:"amount"`
	Disk     int64  `json:"disk"`
	DiskType string `json:"diskType"`
	Spec     string `json:"spec"`
	SpecInfo string `json:"specInfo"`
}

type NetworkConfig struct {
	Type             string             `json:"type"`
	VpcID            string             `json:"vpcId"`
	VsArea           string             `json:"vsArea"`
	VswitchID        string             `json:"vswitchId"`
	WhiteIPGroupList []WhiteIPGroupList `json:"whiteIpGroupList"`
}

type WhiteIPGroupList struct {
	GroupName   string   `json:"groupName"`
	IPS         []string `json:"ips"`
	WhiteIPType string   `json:"whiteIpType"`
}

type NodeSpec struct {
	Spec     string `json:"spec"`
	SpecInfo string `json:"specInfo"`
}

type ZoneInfo struct {
	Status string `json:"status"`
	ZoneID string `json:"zoneId"`
}

type Node struct {
	Health   string `json:"health"`
	Host     string `json:"host"`
	NodeType string `json:"nodeType"`
	Port     int64  `json:"port"`
	ZoneID   string `json:"zoneId"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.ESGraph, error) {
	original := &Es{}
	resource := &model.ESGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Instance.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "es", original.Instance.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "es"
	resource.Name = original.Instance.Description
	resource.Description = original.Instance.Description
	resource.OriginalLabels = lo.Map(original.Instance.Tags,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Status = lo.Ternary(strings.EqualFold(original.Instance.Status, "active"), "running", "stopped")
	resource.PublicEndpoint = original.Instance.PublicDomain
	resource.PrivateEndpoint = original.Instance.Domain
	resource.IPWhiteList = append(resource.IPWhiteList, original.Instance.PrivateNetworkIPWhiteList...)
	resource.IPWhiteList = append(resource.IPWhiteList, original.Instance.PublicIPWhitelist...)
	resource.EngineVersion = original.Instance.EsVersion
	resource.PublicAllowed = original.Instance.EnablePublic

	resource.KibanaIPWhiteList = append(resource.KibanaIPWhiteList, original.Instance.KibanaPrivateIPWhitelist...)
	resource.KibanaIPWhiteList = append(resource.KibanaIPWhiteList, original.Instance.KibanaIPWhitelist...)
	resource.KibanaProtocol = strings.ToLower(original.Instance.Protocol)
	resource.KibanaPublicAllowed = original.Instance.EnableKibanaPublicNetwork
	if resource.KibanaPublicAllowed {
		resource.KibanaPublicEndpoint = original.Instance.KibanaDomain
	} else {
		resource.KibanaPrivateEndpoint = original.Instance.KibanaDomain
	}

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Instance.NetworkConfig.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.Instance.NetworkConfig.VswitchID),
			TargetUID: resource.UID,
		},
	})
	resource.Node = lo.Map(original.Nodes, func(node Node, _ int) *model.ESNodeGraph {
		return &model.ESNodeGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "es-node", node.Host),
				TargetUID: resource.UID,
			},
			IP:    node.Host,
			Class: lo.Ternary(node.NodeType == "KIBANA", "kibana", "es"),
		}
	})

	return resource, nil
}
