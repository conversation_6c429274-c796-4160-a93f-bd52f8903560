package elasticsearch

import "AssetStandardizer/graph"

var esSchema = graph.NodeSchema{
	Label: "Elasticsearch",
	Properties: map[string]graph.PropertyRef{
		"uid":                     {Name: "uid"},
		"provider":                {Name: "provider"},
		"original_id":             {Name: "original_id"},
		"transformed_object":      {Name: "transformed_object"},
		"region":                  {Name: "region"},
		"last_seen":               {Name: "last_seen"},
		"description":             {Name: "description"},
		"kind":                    {Name: "kind"},
		"name":                    {Name: "name"},
		"status":                  {Name: "status"},
		"public_endpoint":         {Name: "public_endpoint"},
		"private_endpoint":        {Name: "private_endpoint"},
		"ip_white_list":           {Name: "ip_white_list"},
		"ip_black_list":           {Name: "ip_black_list"},
		"engine_version":          {Name: "engine_version"},
		"public_allowed":          {Name: "public_allowed"},
		"kibana_public_endpoint":  {Name: "kibana_public_endpoint"},
		"kibana_private_endpoint": {Name: "kibana_private_endpoint"},
		"kibana_protocol":         {Name: "kibana_protocol"},
		"kibana_ip_white_list":    {Name: "kibana_ip_white_list"},
		"kibana_ip_black_list":    {Name: "kibana_ip_black_list"},
		"kibana_public_allowed":   {Name: "kibana_public_allowed"},
	},
}

var esNodeSchema = graph.NodeSchema{
	Label: "ElasticsearchNode",
	Properties: map[string]graph.PropertyRef{
		"uid":     {Name: "uid"},
		"name":    {Name: "name"},
		"class":   {Name: "class"},
		"ip":      {Name: "ip"},
		"node_id": {Name: "node_id"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Elasticsearch",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "PART_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var eipSchema = graph.NodeSchema{
	Label: "EIP",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Elasticsearch",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ASSIGNED_TO",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var sgSchema = graph.NodeSchema{
	Label: "SecurityGroup",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Elasticsearch",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "ATTACHED_TO",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var labelSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"key":   {Name: "key"},
		"value": {Name: "value"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Elasticsearch",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LABEL",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var vpcSchema = graph.NodeSchema{
	Label: "VPC",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Elasticsearch",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var subnetSchema = graph.NodeSchema{
	Label: "Subnet",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Elasticsearch",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "MEMBER_OF",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
