package nat

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strconv"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "nat"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "nat_gateway_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["nat_list"] = append(resourceData["nat_list"], utils.GenParamsFromStruct(resource))

		resourceData["snat_rule_list"] = append(resourceData["snat_rule_list"],
			utils.GenParamsFromStructSlice(resource.SNATRules)...,
		)
		resourceData["ip_range_list"] = append(resourceData["ip_range_list"],
			lo.FlatMap(resource.SNATRules, func(snatRule *model.SNATRuleGraph, _ int) []map[string]any {
				return utils.GenParamsFromStructSlice(snatRule.IPRange)
			})...,
		)

		resourceData["dnat_rule_list"] = append(resourceData["dnat_rule_list"],
			utils.GenParamsFromStructSlice(resource.DNATRules)...,
		)

		resourceData["eip_list"] = append(resourceData["eip_list"],
			utils.GenParamsFromStructSlice(resource.EIP)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, natSchema, resourceData["nat_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, snatRuleSchema, resourceData["snat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, dnatRuleSchema, resourceData["dnat_rule_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, eipSchema, resourceData["eip_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, ipRangeSchema, resourceData["ip_range_list"], map[string]any{"last_updated": "test"})
}

type NAT struct {
	DnatTable  []DnatTable `json:"dnatTable"`
	NatGateway NatGateway  `json:"natGateway"`
	SnatTable  []SnatTable `json:"snatTable"`
}

type DnatTable struct {
	ExternalIP       string `json:"ExternalIp"`
	ExternalPort     string `json:"ExternalPort"`
	ForwardEntryID   string `json:"ForwardEntryId"`
	ForwardEntryName string `json:"ForwardEntryName"`
	ForwardTableID   string `json:"ForwardTableId"`
	InternalIP       string `json:"InternalIp"`
	InternalPort     string `json:"InternalPort"`
	IPProtocol       string `json:"IpProtocol"`
	NatGatewayID     string `json:"NatGatewayId"`
	Status           string `json:"Status"`
}

type NatGateway struct {
	AutoPay                   bool                  `json:"AutoPay"`
	BusinessStatus            string                `json:"BusinessStatus"`
	CreationTime              string                `json:"CreationTime"`
	DeletionProtection        bool                  `json:"DeletionProtection"`
	Description               string                `json:"Description"`
	EcsMetricEnabled          bool                  `json:"EcsMetricEnabled"`
	EipBindMode               string                `json:"EipBindMode"`
	EnableSessionLog          string                `json:"EnableSessionLog"`
	ExpiredTime               string                `json:"ExpiredTime"`
	ForwardTableIDS           ForwardTableIDS       `json:"ForwardTableIds"`
	FullNatTableIDS           FullNatTableIDS       `json:"FullNatTableIds"`
	ICMPReplyEnabled          bool                  `json:"IcmpReplyEnabled"`
	InstanceChargeType        string                `json:"InstanceChargeType"`
	InternetChargeType        string                `json:"InternetChargeType"`
	IPLists                   IPLists               `json:"IpLists"`
	Name                      string                `json:"Name"`
	NatGatewayID              string                `json:"NatGatewayId"`
	NatGatewayPrivateInfo     NatGatewayPrivateInfo `json:"NatGatewayPrivateInfo"`
	NatType                   string                `json:"NatType"`
	NetworkType               string                `json:"NetworkType"`
	PrivateLinkEnabled        *bool                 `json:"PrivateLinkEnabled,omitempty"`
	RegionID                  string                `json:"RegionId"`
	ResourceGroupID           string                `json:"ResourceGroupId"`
	SnatTableIDS              SnatTableIDS          `json:"SnatTableIds"`
	Spec                      string                `json:"Spec"`
	Status                    string                `json:"Status"`
	Tags                      Tags                  `json:"Tags"`
	VpcID                     string                `json:"VpcId"`
	SecurityProtectionEnabled *bool                 `json:"SecurityProtectionEnabled,omitempty"`
}

type ForwardTableIDS struct {
	ForwardTableID []string `json:"ForwardTableId"`
}

type FullNatTableIDS struct {
}

type IPLists struct {
	IPList []IPList `json:"IpList"`
}

type IPList struct {
	AllocationID     string `json:"AllocationId"`
	IPAddress        string `json:"IpAddress"`
	PrivateIPAddress string `json:"PrivateIpAddress"`
	UsingStatus      string `json:"UsingStatus"`
}

type NatGatewayPrivateInfo struct {
	EniInstanceID           string `json:"EniInstanceId"`
	EniType                 string `json:"EniType"`
	IzNo                    string `json:"IzNo"`
	MaxBandwidth            int64  `json:"MaxBandwidth"`
	MaxSessionEstablishRate int64  `json:"MaxSessionEstablishRate"`
	MaxSessionQuota         int64  `json:"MaxSessionQuota"`
	PrivateIPAddress        string `json:"PrivateIpAddress"`
	VswitchID               string `json:"VswitchId"`
}

type SnatTableIDS struct {
	SnatTableID []string `json:"SnatTableId"`
}

type SnatTable struct {
	EipAffinity     string `json:"EipAffinity"`
	NatGatewayID    string `json:"NatGatewayId"`
	SnatEntryID     string `json:"SnatEntryId"`
	SnatEntryName   string `json:"SnatEntryName"`
	SnatIP          string `json:"SnatIp"`
	SnatTableID     string `json:"SnatTableId"`
	SourceCIDR      string `json:"SourceCIDR"`
	SourceVSwitchID string `json:"SourceVSwitchId"`
	Status          string `json:"Status"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.NATGraph, error) {
	original := &NAT{}
	resource := &model.NATGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.NatGateway.NatGatewayID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "nat", original.NatGateway.NatGatewayID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "nat"
	resource.Name = original.NatGateway.Name
	resource.Description = original.NatGateway.Description
	resource.OriginalLabels = lo.Map(original.NatGateway.Tags.Tag,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.TagKey, e.TagValue, resource.UID) },
	)

	resource.Status = strings.ToLower(original.NatGateway.Status)
	resource.Spec = original.NatGateway.Spec

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.NatGateway.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "subnet", original.NatGateway.NatGatewayPrivateInfo.VswitchID),
			TargetUID: resource.UID,
		},
	})
	resource.EIP = lo.Map(original.NatGateway.IPLists.IPList, func(ip IPList, _ int) *model.EipGraph {
		return &model.EipGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "eip", ip.AllocationID),
				TargetUID: resource.UID,
			},
		}
	})
	resource.SNATRules = lo.FilterMap(original.SnatTable, func(snatTable SnatTable, _ int) (*model.SNATRuleGraph, bool) {
		natUID := utils.GenerateUID(resource.Provider, "snat_rule", snatTable.SnatEntryID)
		ipRange, err := provider_utils.ParseIPRange(snatTable.SourceCIDR, natUID)
		if err != nil {
			logger.Errorf("parse snat table ip range error, snatTable: %v, error: %v", snatTable, err)
			return nil, false
		}

		return &model.SNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       natUID,
				TargetUID: resource.UID,
			},
			RuleID:  snatTable.SnatEntryID,
			Name:    snatTable.SnatEntryName,
			Status:  strings.ToLower(snatTable.Status),
			IPRange: []*model.IPRangeGraph{ipRange},
		}, true
	})

	resource.DNATRules = lo.Map(original.DnatTable, func(dnatTable DnatTable, _ int) *model.DNATRuleGraph {
		internalPort, _ := strconv.Atoi(dnatTable.InternalPort)
		externalPort, _ := strconv.Atoi(dnatTable.ExternalPort)
		return &model.DNATRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "dnat_rule", dnatTable.ForwardEntryID),
				TargetUID: resource.UID,
			},
			RuleID:       dnatTable.ForwardEntryID,
			Name:         dnatTable.ForwardEntryName,
			Status:       strings.ToLower(dnatTable.Status),
			Protocol:     strings.ToLower(dnatTable.IPProtocol),
			InternalIP:   dnatTable.InternalIP,
			InternalPort: internalPort,
			ExternalIP:   dnatTable.ExternalIP,
			ExternalPort: externalPort,
		}
	})

	return resource, nil
}
