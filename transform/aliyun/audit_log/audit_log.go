package audit_log

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strconv"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "audit_log"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "auditlog-entries-aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["audit_log_status_list"] = append(resourceData["audit_log_status_list"], utils.GenParamsFromStruct(resource))

		resourceData["audit_log_status_entry_list"] = append(resourceData["audit_log_status_entry_list"],
			utils.GenParamsFromStructSlice(resource.Entries)...,
		)
	}

	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, auditLogStatusSchema, resourceData["audit_log_status_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, auditLogStatusEntrySchema, resourceData["audit_log_status_entry_list"], map[string]any{"last_updated": "test"})
}

type AuditLog struct {
	AuditLogEntries []AuditLogEntry `json:"auditLogEntries"`
}

type AuditLogEntry struct {
	ServiceName string `json:"serviceName"`
	EventName   string `json:"eventName"`
	LastIngest  string `json:"lastIngest"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.AuditLogStatusGraph, error) {
	original := &AuditLog{}
	resource := &model.AuditLogStatusGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = "audit-log"
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "audit-log-status", "audit-log")
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "audit-log-status"
	resource.Name = "audit-log"
	resource.Description = "audit-log"

	resource.Entries = append(resource.Entries, lo.Map(original.AuditLogEntries, func(e AuditLogEntry, _ int) *model.AuditLogStatusEntryGraph {
		entry := &model.AuditLogStatusEntryGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "audit-log-status-entry", e.ServiceName+e.EventName),
				TargetUID: resource.UID,
			},
			ServiceName: e.ServiceName,
			EventName:   e.EventName,
		}
		entry.LastIngestAt, _ = strconv.ParseInt(e.LastIngest, 10, 64)
		entry.LastIngestAt *= 1000
		return entry
	})...)

	return resource, nil
}
