package k8s

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"encoding/json"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "k8s"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "k8s_cluster_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["k8s_list"] = append(resourceData["k8s_list"], utils.GenParamsFromStruct(resource))

		resourceData["sg_list"] = append(resourceData["sg_list"],
			utils.GenParamsFromStructSlice(resource.SG)...,
		)

		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.VPC)...,
		)

		resourceData["subnet_list"] = append(resourceData["subnet_list"],
			utils.GenParamsFromStructSlice(resource.Subnet)...,
		)

		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, k8sSchema, resourceData["k8s_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, sgSchema, resourceData["sg_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, subnetSchema, resourceData["subnet_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
}

type K8S struct {
	Cluster Cluster `json:"cluster"`
}

type Cluster struct {
	ClusterID              string            `json:"cluster_id"`
	ClusterSpec            string            `json:"cluster_spec"`
	ClusterType            string            `json:"cluster_type"`
	Created                string            `json:"created"`
	CurrentVersion         string            `json:"current_version"`
	DeletionProtection     bool              `json:"deletion_protection"`
	ExternalLoadbalancerID string            `json:"external_loadbalancer_id"`
	InitVersion            string            `json:"init_version"`
	MaintenanceWindow      MaintenanceWindow `json:"maintenance_window"`
	MasterURL              string            `json:"master_url"`
	MetaData               string            `json:"meta_data"`
	Name                   string            `json:"name"`
	NetworkMode            string            `json:"network_mode"`
	OperationPolicy        OperationPolicy   `json:"operation_policy"`
	Profile                string            `json:"profile"`
	RegionID               string            `json:"region_id"`
	ResourceGroupID        string            `json:"resource_group_id"`
	SecurityGroupID        string            `json:"security_group_id"`
	Size                   int64             `json:"size"`
	State                  string            `json:"state"`
	Tags                   []K8STag          `json:"tags"`
	Updated                string            `json:"updated"`
	VpcID                  string            `json:"vpc_id"`
	VswitchID              string            `json:"vswitch_id"`
	ZoneID                 string            `json:"zone_id"`
}

type K8STag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type MaintenanceWindow struct {
	Enable       bool   `json:"enable"`
	WeeklyPeriod string `json:"weekly_period"`
}

type OperationPolicy struct {
}

type MasterURL struct {
	APIServerEndpoint         string `json:"api_server_endpoint"`
	IntranetAPIServerEndpoint string `json:"intranet_api_server_endpoint"`
}

type Metadata struct {
	ServiceCIDR      string `json:"ServiceCIDR"`
	VpcCIDR          string `json:"VpcCidr"`
	AuditProjectName string `json:"AuditProjectName"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.K8SGraph, error) {
	original := &K8S{}
	resource := &model.K8SGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cluster.ClusterID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "kubernetes", original.Cluster.ClusterID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "kubernetes"
	resource.Name = original.Cluster.Name

	resource.Status = lo.Ternary(strings.EqualFold(original.Cluster.State, "running"), "running", "stopped")
	resource.EngineVersion = original.Cluster.CurrentVersion

	var masterUrl MasterURL
	err := json.Unmarshal([]byte(original.Cluster.MasterURL), &masterUrl)
	if err != nil {
		return nil, err
	}
	if masterUrl.APIServerEndpoint != "" {
		resource.PublicEndpoint = masterUrl.APIServerEndpoint
	}
	if masterUrl.IntranetAPIServerEndpoint != "" {
		resource.PrivateEndpoint = masterUrl.IntranetAPIServerEndpoint
	}

	var metadata Metadata
	err = json.Unmarshal([]byte(original.Cluster.MetaData), &metadata)
	if err != nil {
		return nil, err
	}
	resource.PodCIDRs = []string{metadata.VpcCIDR}
	resource.ServiceCIDRs = []string{metadata.ServiceCIDR}
	resource.AuditLogEnabled = lo.ToPtr(metadata.AuditProjectName != "")

	resource.VPC = append(resource.VPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.Cluster.VpcID),
			TargetUID: resource.UID,
		},
	})
	if len(original.Cluster.VswitchID) > 0 {
		resource.Subnet = append(resource.Subnet, &model.SubnetGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(resource.Provider, "subnet", original.Cluster.VswitchID),
				TargetUID: resource.UID,
			},
		})
	}
	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.Cluster.ExternalLoadbalancerID),
			TargetUID: resource.UID,
		},
	})
	resource.SG = append(resource.SG, &model.SGGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "security-group", original.Cluster.SecurityGroupID),
			TargetUID: resource.UID,
		},
	})

	return resource, nil
}
