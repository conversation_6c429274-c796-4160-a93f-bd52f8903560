package cen

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "cen"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "cen_cen_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["cen_list"] = append(resourceData["cen_list"], utils.GenParamsFromStruct(resource))

		resourceData["cen_route_entry_list"] = append(resourceData["cen_route_entry_list"],
			utils.GenParamsFromStructSlice(resource.CenRouteEntry)...,
		)

		for _, attachedInstance := range resource.AttachedInstance {
			instanceType := attachedInstance.InstanceType
			resourceData[instanceType] = append(resourceData[instanceType], utils.GenParamsFromStruct(attachedInstance))
		}

	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, cenSchema, resourceData["cen_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, cenRouteEntrySchema, resourceData["cen_route_entry_list"], map[string]any{"last_updated": "test"})

	for key, instances := range resourceData {
		if strings.HasSuffix(key, "_list") {
			continue
		}

		instanceSchema := cenAttachedInstanceSchema
		instanceSchema.OtherRelationships = append(instanceSchema.OtherRelationships, relToInstance(key)...)
		graph.Run(n4jSession, instanceSchema, instances, map[string]any{"last_updated": "test"})
	}
}

type Csn struct {
	AttachedInstances []AttachedInstance `json:"attachedInstances"`
	Cen               Cen                `json:"cen"`
	Routers           []Router           `json:"routers"`
}

type AttachedInstance struct {
	CenID                   string `json:"CenId"`
	ChildInstanceAttachTime string `json:"ChildInstanceAttachTime"`
	ChildInstanceID         string `json:"ChildInstanceId"`
	ChildInstanceOwnerID    int64  `json:"ChildInstanceOwnerId"`
	ChildInstanceRegionID   string `json:"ChildInstanceRegionId"`
	ChildInstanceType       string `json:"ChildInstanceType"`
	Status                  string `json:"Status"`
}

type Cen struct {
	CenBandwidthPackageIDS CenBandwidthPackageIDS `json:"CenBandwidthPackageIds"`
	CenID                  string                 `json:"CenId"`
	CreationTime           string                 `json:"CreationTime"`
	Ipv6Level              string                 `json:"Ipv6Level"`
	Name                   string                 `json:"Name"`
	ProtectionLevel        string                 `json:"ProtectionLevel"`
	ResourceGroupID        string                 `json:"ResourceGroupId"`
	Status                 string                 `json:"Status"`
}

type CenBandwidthPackageIDS struct {
	CenBandwidthPackageID []string `json:"CenBandwidthPackageId"`
}

type Router struct {
	Router      RouterClass     `json:"Router"`
	RouteTables []CenRouteTable `json:"RouteTables"`
}

type CenRouteTable struct {
	RouterTable  RouterTable  `json:"RouterTable"`
	RouteEntries []RouteEntry `json:"RouteEntries"`
	BasicEntries []BasicEntry `json:"BasicEntries"`
}

type RouteEntry struct {
	PathAttributes                              PathAttributes `json:"PathAttributes"`
	TransitRouterRouteEntryDestinationCIDRBlock string         `json:"TransitRouterRouteEntryDestinationCidrBlock"`
	TransitRouterRouteEntryNextHopID            string         `json:"TransitRouterRouteEntryNextHopId"`
	TransitRouterRouteEntryNextHopResourceID    string         `json:"TransitRouterRouteEntryNextHopResourceId"`
	TransitRouterRouteEntryNextHopResourceType  string         `json:"TransitRouterRouteEntryNextHopResourceType"`
	TransitRouterRouteEntryNextHopType          string         `json:"TransitRouterRouteEntryNextHopType"`
	TransitRouterRouteEntryOriginResourceID     string         `json:"TransitRouterRouteEntryOriginResourceId"`
	TransitRouterRouteEntryOriginResourceType   string         `json:"TransitRouterRouteEntryOriginResourceType"`
	TransitRouterRouteEntryStatus               string         `json:"TransitRouterRouteEntryStatus"`
	TransitRouterRouteEntryType                 string         `json:"TransitRouterRouteEntryType"`
	CreateTime                                  *string        `json:"CreateTime,omitempty"`
	OperationalMode                             *bool          `json:"OperationalMode,omitempty"`
	TransitRouterRouteEntryDescription          *string        `json:"TransitRouterRouteEntryDescription,omitempty"`
	TransitRouterRouteEntryID                   *string        `json:"TransitRouterRouteEntryId,omitempty"`
	TransitRouterRouteEntryName                 *string        `json:"TransitRouterRouteEntryName,omitempty"`
}

type PathAttributes struct {
	AsPaths            []string `json:"AsPaths,omitempty"`
	OriginInstanceID   string   `json:"OriginInstanceId"`
	OriginInstanceType string   `json:"OriginInstanceType"`
	OriginRouteType    string   `json:"OriginRouteType"`
	Preference         int64    `json:"Preference"`
}

type RouterTable struct {
	CreateTime                         string            `json:"CreateTime"`
	RegionID                           string            `json:"RegionId"`
	RouteTableOptions                  RouteTableOptions `json:"RouteTableOptions"`
	TransitRouterID                    string            `json:"TransitRouterId"`
	TransitRouterRouteTableDescription string            `json:"TransitRouterRouteTableDescription"`
	TransitRouterRouteTableID          string            `json:"TransitRouterRouteTableId"`
	TransitRouterRouteTableName        string            `json:"TransitRouterRouteTableName"`
	TransitRouterRouteTableStatus      string            `json:"TransitRouterRouteTableStatus"`
	TransitRouterRouteTableType        string            `json:"TransitRouterRouteTableType"`
}

type RouteTableOptions struct {
	MultiRegionECMP string `json:"MultiRegionECMP"`
}

type BasicEntry struct {
	AsPaths               AsPaths               `json:"AsPaths"`
	CenOutRouteMapRecords CenOutRouteMapRecords `json:"CenOutRouteMapRecords"`
	CenRouteMapRecords    CenRouteMapRecords    `json:"CenRouteMapRecords"`
	Communities           Communities           `json:"Communities"`
	DestinationCIDRBlock  string                `json:"DestinationCidrBlock"`
	NextHopInstanceID     string                `json:"NextHopInstanceId"`
	NextHopRegionID       string                `json:"NextHopRegionId"`
	NextHopType           string                `json:"NextHopType"`
	Preference            int64                 `json:"Preference"`
	Status                string                `json:"Status"`
	ToOtherRegionStatus   string                `json:"ToOtherRegionStatus"`
	Type                  string                `json:"Type"`
}

type AsPaths struct {
	AsPath []string `json:"AsPath"`
}

type CenOutRouteMapRecords struct {
	CenOutRouteMapRecord []CenRouteMapRecord `json:"CenOutRouteMapRecord"`
}

type CenRouteMapRecord struct {
	RegionID   string `json:"RegionId"`
	RouteMapID string `json:"RouteMapId"`
}

type CenRouteMapRecords struct {
	CenRouteMapRecord []CenRouteMapRecord `json:"CenRouteMapRecord"`
}

type Communities struct {
}

type RouterClass struct {
	AliUid                   int64  `json:"AliUid"`
	CenID                    string `json:"CenId"`
	CreationTime             string `json:"CreationTime"`
	RegionID                 string `json:"RegionId"`
	Status                   string `json:"Status"`
	SupportMulticast         bool   `json:"SupportMulticast"`
	TransitRouterDescription string `json:"TransitRouterDescription"`
	TransitRouterID          string `json:"TransitRouterId"`
	TransitRouterName        string `json:"TransitRouterName"`
	Type                     string `json:"Type"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.CenGraph, error) {
	original := &Csn{}
	resource := &model.CenGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Cen.CenID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "cen", original.Cen.CenID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "cen"
	resource.Name = original.Cen.Name

	resource.Status = lo.Ternary(strings.EqualFold(original.Cen.Status, "active"), "running", "stopped")
	resource.AttachedInstance = lo.Map(original.AttachedInstances, func(e AttachedInstance, _ int) *model.CenAttachedInstanceGraph {
		return &model.CenAttachedInstanceGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, strings.ToLower(e.ChildInstanceType), e.ChildInstanceID),
				TargetUID: resource.UID,
			},
			InstanceID:   e.ChildInstanceID,
			InstanceType: strings.ToLower(e.ChildInstanceType),
		}
	})
	resource.CenRouteEntry = lo.Flatten(
		lo.FlatMap(original.Routers, func(r Router, _ int) [][]*model.CenRouteEntryGraph {
			return lo.Map(r.RouteTables, func(rt CenRouteTable, _ int) []*model.CenRouteEntryGraph {
				var entries []*model.CenRouteEntryGraph
				entries = append(entries, lo.Map(rt.RouteEntries, func(re RouteEntry, _ int) *model.CenRouteEntryGraph {
					normalizedNextHopType := strings.ToLower(re.TransitRouterRouteEntryNextHopType)
					normalizedSourceType := strings.ToLower(re.TransitRouterRouteEntryOriginResourceType)

					srcInstanceUID := utils.GenerateUID(provider_utils.ProviderID, normalizedSourceType, re.TransitRouterRouteEntryOriginResourceID)
					nextInstanceUID := utils.GenerateUID(provider_utils.ProviderID, normalizedNextHopType, re.TransitRouterRouteEntryNextHopResourceID)
					return &model.CenRouteEntryGraph{
						BaseNode: model.BaseNode{
							UID:       utils.GenerateUID(provider_utils.ProviderID, "cen-route-entry", srcInstanceUID+":"+nextInstanceUID),
							TargetUID: resource.UID,
						},
						SrcInstanceID:   re.TransitRouterRouteEntryOriginResourceID,
						SrcInstanceUID:  srcInstanceUID,
						DstCidrBlock:    re.TransitRouterRouteEntryDestinationCIDRBlock,
						NextInstanceID:  re.TransitRouterRouteEntryNextHopResourceID,
						NextInstanceUID: nextInstanceUID,
						Status:          lo.Ternary(strings.EqualFold(re.TransitRouterRouteEntryStatus, "active"), "active", "inactive"),
						Class:           lo.Ternary(strings.EqualFold(re.TransitRouterRouteEntryType, "static"), "custom", "system"),
					}
				})...)
				entries = append(entries, lo.Map(rt.BasicEntries, func(be BasicEntry, _ int) *model.CenRouteEntryGraph {
					normalizedNextHopType := strings.ToLower(be.NextHopType)
					return &model.CenRouteEntryGraph{
						BaseNode: model.BaseNode{
							UID:       utils.GenerateUID(provider_utils.ProviderID, "cen-route-entry", be.DestinationCIDRBlock+":"+be.NextHopInstanceID),
							TargetUID: resource.UID,
						},
						DstCidrBlock:    be.DestinationCIDRBlock,
						NextInstanceID:  be.NextHopInstanceID,
						NextInstanceUID: utils.GenerateUID(provider_utils.ProviderID, normalizedNextHopType, be.NextHopInstanceID),
						Status:          lo.Ternary(strings.EqualFold(be.Status, "active"), "active", "inactive"),
					}
				})...)

				return entries
			})
		}),
	)

	return resource, nil
}
