package lb_listener

import "AssetStandardizer/graph"

var lbListenerSchema = graph.NodeSchema{
	Label: "LoadBalancerListener",
	Properties: map[string]graph.PropertyRef{
		"uid":                  {Name: "uid"},
		"provider":             {Name: "provider"},
		"original_id":          {Name: "original_id"},
		"transformed_object":   {Name: "transformed_object"},
		"region":               {Name: "region"},
		"last_seen":            {Name: "last_seen"},
		"description":          {Name: "description"},
		"kind":                 {Name: "kind"},
		"name":                 {Name: "name"},
		"status":               {Name: "status"},
		"health_check_enabled": {Name: "health_check_enabled"},
		"cert_exists":          {Name: "cert_exists"},
		"protocol":             {Name: "protocol"},
		"acl_status":           {Name: "acl_status"},
		"acl_type":             {Name: "acl_type"},
		"authorized":           {Name: "authorized"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancer",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LISTENER",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var lbFromListenerSchema = graph.NodeSchema{
	Label: "LoadBalancer",
	Properties: map[string]graph.PropertyRef{
		"uid": {Name: "uid"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancerListener",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LISTENER",
			Direction: graph.OUTWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var portRangeSchema = graph.NodeSchema{
	Label: "PortRange",
	Properties: map[string]graph.PropertyRef{
		"uid":   {Name: "uid"},
		"start": {Name: "start"},
		"end":   {Name: "end"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancerListener",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "LISTENS",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var labelSchema = graph.NodeSchema{
	Label: "Label",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"key":          {Name: "key"},
		"value":        {Name: "value"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LoadBalancerListener",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_LABEL",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
