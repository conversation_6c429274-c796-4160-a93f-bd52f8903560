package lb_listener

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "slb_listener"})

func NewSLBListenerService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "lb_slbListener_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transformSLBListener,
		UpdateResources: updateSLBListeners,
	}
}

func transformSLBListener(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseSLBListener(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["lb_listener_list"] = append(resourceData["lb_listener_list"], utils.GenParamsFromStruct(resource))
		resourceData["lb_list"] = append(resourceData["lb_list"],
			utils.GenParamsFromStructSlice(resource.LB)...,
		)
		resourceData["port_range_list"] = append(resourceData["port_range_list"],
			utils.GenParamsFromStructSlice(resource.PortRange)...,
		)
		resourceData["label_list"] = append(resourceData["label_list"],
			utils.GenParamsFromStructSlice(resource.OriginalLabels)...,
		)
	}
	return resourceData, nil
}

func updateSLBListeners(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, lbListenerSchema, resourceData["lb_listener_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, lbFromListenerSchema, resourceData["lb_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, portRangeSchema, resourceData["port_range_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, labelSchema, resourceData["label_list"], map[string]any{"last_updated": "test"})
}

type LbListener struct {
	Acls     []ListenerACL `json:"acls"`
	Listener Listener      `json:"listener"`
}

type ListenerACL struct {
	ACLEntrys        ACLEntrys        `json:"AclEntrys"`
	ACLID            string           `json:"AclId"`
	ACLName          string           `json:"AclName"`
	AddressIPVersion string           `json:"AddressIPVersion"`
	CreateTime       string           `json:"CreateTime"`
	RelatedListeners RelatedListeners `json:"RelatedListeners"`
	RequestID        string           `json:"RequestId"`
	ResourceGroupID  string           `json:"ResourceGroupId"`
	Tags             Tags             `json:"Tags"`
	TotalACLEntry    int64            `json:"TotalAclEntry"`
}

type ACLEntrys struct {
	ACLEntry []ACLEntry `json:"AclEntry"`
}

type ACLEntry struct {
	ACLEntryComment *string `json:"AclEntryComment"`
	ACLEntryIP      string  `json:"AclEntryIP"`
}

type RelatedListeners struct {
	RelatedListener []RelatedListener `json:"RelatedListener"`
}

type RelatedListener struct {
	ACLType        string `json:"AclType"`
	ListenerPort   int64  `json:"ListenerPort"`
	LoadBalancerID string `json:"LoadBalancerId"`
	Protocol       string `json:"Protocol"`
}

type Listener struct {
	ACLID               string              `json:"AclId"`
	ACLIDS              []string            `json:"AclIds"`
	ACLStatus           string              `json:"AclStatus"`
	ACLType             string              `json:"AclType"`
	BackendServerPort   int64               `json:"BackendServerPort"`
	Bandwidth           int64               `json:"Bandwidth"`
	Description         string              `json:"Description"`
	HTTPListenerConfig  HTTPListenerConfig  `json:"HTTPListenerConfig"`
	HTTPSListenerConfig HTTPSListenerConfig `json:"HTTPSListenerConfig"`
	ListenerPort        int                 `json:"ListenerPort"`
	ListenerProtocol    string              `json:"ListenerProtocol"`
	LoadBalancerID      string              `json:"LoadBalancerId"`
	Scheduler           string              `json:"Scheduler"`
	Status              string              `json:"Status"`
	TCPListenerConfig   TCPListenerConfig   `json:"TCPListenerConfig"`
	UDPListenerConfig   UDPListenerConfig   `json:"UDPListenerConfig"`
	VServerGroupID      string              `json:"VServerGroupId"`
	Tags                Tags                `json:"Tags"`
}

type HTTPListenerConfig struct {
	CookieTimeout          int64  `json:"CookieTimeout"`
	Gzip                   string `json:"Gzip"`
	HealthCheck            string `json:"HealthCheck"`
	HealthCheckConnectPort int64  `json:"HealthCheckConnectPort"`
	HealthCheckHTTPCode    string `json:"HealthCheckHttpCode"`
	HealthCheckHTTPVersion string `json:"HealthCheckHttpVersion"`
	HealthCheckInterval    int64  `json:"HealthCheckInterval"`
	HealthCheckMethod      string `json:"HealthCheckMethod"`
	HealthCheckTimeout     int64  `json:"HealthCheckTimeout"`
	HealthCheckType        string `json:"HealthCheckType"`
	HealthCheckURI         string `json:"HealthCheckURI"`
	HealthyThreshold       int64  `json:"HealthyThreshold"`
	IdleTimeout            int64  `json:"IdleTimeout"`
	ListenerForward        string `json:"ListenerForward"`
	RequestTimeout         int64  `json:"RequestTimeout"`
	StickySession          string `json:"StickySession"`
	StickySessionType      string `json:"StickySessionType"`
	UnhealthyThreshold     int64  `json:"UnhealthyThreshold"`
	XForwardedFor          string `json:"XForwardedFor"`
	XForwardedForSLBID     string `json:"XForwardedFor_SLBID"`
	XForwardedForSLBIP     string `json:"XForwardedFor_SLBIP"`
	XForwardedForProto     string `json:"XForwardedFor_proto"`
}

type HTTPSListenerConfig struct {
	EnableHttp2            string `json:"EnableHttp2"`
	Gzip                   string `json:"Gzip"`
	HealthCheck            string `json:"HealthCheck"`
	HealthCheckConnectPort int64  `json:"HealthCheckConnectPort"`
	HealthCheckDomain      string `json:"HealthCheckDomain"`
	HealthCheckHTTPCode    string `json:"HealthCheckHttpCode"`
	HealthCheckInterval    int64  `json:"HealthCheckInterval"`
	HealthCheckMethod      string `json:"HealthCheckMethod"`
	HealthCheckTimeout     int64  `json:"HealthCheckTimeout"`
	HealthCheckType        string `json:"HealthCheckType"`
	HealthCheckURI         string `json:"HealthCheckURI"`
	HealthyThreshold       int64  `json:"HealthyThreshold"`
	IdleTimeout            int64  `json:"IdleTimeout"`
	RequestTimeout         int64  `json:"RequestTimeout"`
	ServerCertificateID    string `json:"ServerCertificateId"`
	StickySession          string `json:"StickySession"`
	TLSCipherPolicy        string `json:"TLSCipherPolicy"`
	UnhealthyThreshold     int64  `json:"UnhealthyThreshold"`
	XForwardedFor          string `json:"XForwardedFor"`
	XForwardedForSLBID     string `json:"XForwardedFor_SLBID"`
	XForwardedForSLBIP     string `json:"XForwardedFor_SLBIP"`
	XForwardedForProto     string `json:"XForwardedFor_proto"`
}

type TCPListenerConfig struct {
	EstablishedTimeout        int64  `json:"EstablishedTimeout"`
	HealthCheck               string `json:"HealthCheck"`
	HealthCheckConnectTimeout int64  `json:"HealthCheckConnectTimeout"`
	HealthCheckDomain         string `json:"HealthCheckDomain"`
	HealthCheckHTTPCode       string `json:"HealthCheckHttpCode"`
	HealthCheckInterval       int64  `json:"HealthCheckInterval"`
	HealthCheckType           string `json:"HealthCheckType"`
	HealthCheckURI            string `json:"HealthCheckURI"`
	HealthyThreshold          int64  `json:"HealthyThreshold"`
	PersistenceTimeout        int64  `json:"PersistenceTimeout"`
	UnhealthyThreshold        int64  `json:"UnhealthyThreshold"`
}

type UDPListenerConfig struct {
	HealthCheck               string `json:"HealthCheck"`
	HealthCheckConnectTimeout int64  `json:"HealthCheckConnectTimeout"`
	HealthCheckInterval       int64  `json:"HealthCheckInterval"`
	HealthyThreshold          int64  `json:"HealthyThreshold"`
	ProxyProtocolV2Enabled    string `json:"ProxyProtocolV2Enabled"`
	UnhealthyThreshold        int64  `json:"UnhealthyThreshold"`
}

type Tags struct {
	Tag []Tag `json:"Tag"`
}

type Tag struct {
	Key   string `json:"TagKey"`
	Value string `json:"TagValue"`
}

func parseSLBListener(assetMsg *model.AssetMessage) (*model.LbListenerGraph, error) {
	original := &LbListener{}
	resource := &model.LbListenerGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "lb-listener", fmt.Sprintf("%s@%d/%s", original.Listener.LoadBalancerID, original.Listener.ListenerPort, original.Listener.ListenerProtocol))
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "lb-listener"
	resource.Name = fmt.Sprintf("%s@%d/%s", original.Listener.LoadBalancerID, original.Listener.ListenerPort, original.Listener.ListenerProtocol)
	resource.Description = original.Listener.Description
	resource.OriginalLabels = lo.Map(original.Listener.Tags.Tag,
		func(e Tag, _ int) *model.KVGraph { return utils.NewLabel(e.Key, e.Value, resource.UID) },
	)

	resource.TargetUID = utils.GenerateUID(resource.Provider, "lb", original.Listener.LoadBalancerID)
	resource.AclStatus = original.Listener.ACLStatus
	resource.AclType = original.Listener.ACLType
	resource.Protocol = strings.ToLower(original.Listener.ListenerProtocol)
	resource.Status = "active"
	switch resource.Protocol {
	case "tcp":
		resource.HealthCheckEnabled = lo.ToPtr(original.Listener.TCPListenerConfig.HealthCheck == "on")
		resource.CertExists = lo.ToPtr(false)
	case "udp":
		resource.HealthCheckEnabled = lo.ToPtr(original.Listener.UDPListenerConfig.HealthCheck == "on")
		resource.CertExists = lo.ToPtr(false)
	case "http":
		resource.HealthCheckEnabled = lo.ToPtr(original.Listener.HTTPListenerConfig.HealthCheck == "on")
		resource.CertExists = lo.ToPtr(false)
	case "https":
		resource.HealthCheckEnabled = lo.ToPtr(original.Listener.HTTPListenerConfig.HealthCheck == "on")
		resource.CertExists = lo.ToPtr(original.Listener.HTTPSListenerConfig.ServerCertificateID != "")
	}

	resource.LB = append(resource.LB, &model.LBGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "lb", original.Listener.LoadBalancerID),
			TargetUID: resource.UID,
		},
	})
	resource.PortRange = append(resource.PortRange, utils.NewPortRange(original.Listener.ListenerPort, original.Listener.ListenerPort, resource.UID))
	return resource, nil
}
