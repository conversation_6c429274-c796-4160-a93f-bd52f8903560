package bucket

import "AssetStandardizer/graph"

var bucketSchema = graph.NodeSchema{
	Label: "Bucket",
	Properties: map[string]graph.PropertyRef{
		"uid":                         {Name: "uid"},
		"last_updated":                {Name: "last_updated", SetInKwargs: true},
		"provider":                    {Name: "provider"},
		"original_id":                 {Name: "original_id"},
		"transformed_object":          {Name: "transformed_object"},
		"region":                      {Name: "region"},
		"last_seen":                   {Name: "last_seen"},
		"description":                 {Name: "description"},
		"kind":                        {Name: "kind"},
		"name":                        {Name: "name"},
		"private_endpoint":            {Name: "private_endpoint"},
		"public_endpoint":             {Name: "public_endpoint"},
		"acl":                         {Name: "acl"},
		"logging_enabled":             {Name: "logging_enabled"},
		"encryption_enabled":          {Name: "encryption_enabled"},
		"allow_empty_referer":         {Name: "allow_empty_referer"},
		"allow_truncate_query_string": {Name: "allow_truncate_query_string"},
		"truncate_path":               {Name: "truncate_path"},
		"referer_white_list":          {Name: "referer_white_list"},
		"referer_black_list":          {Name: "referer_black_list"},
	},
}

var policySchema = graph.NodeSchema{
	Label: "BucketPolicy",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"effect":       {Name: "effect"},
		"action":       {Name: "action"},
		"resource":     {Name: "resource"},
		"condition":    {Name: "condition"},
		"principal":    {Name: "principal"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Bucket",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}

var corsSchema = graph.NodeSchema{
	Label: "BucketCORS",
	Properties: map[string]graph.PropertyRef{
		"uid":             {Name: "uid"},
		"allowed_origins": {Name: "allowed_origins"},
		"allowed_methods": {Name: "allowed_methods"},
		"allowed_headers": {Name: "allowed_headers"},
		"expose_headers":  {Name: "expose_headers"},
		"max_age_seconds": {Name: "max_age_seconds"},
		"last_updated":    {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "Bucket",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
