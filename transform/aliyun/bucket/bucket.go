package bucket

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"fmt"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "bucket"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "oss_bucket_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["bucket_list"] = append(resourceData["bucket_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_list"] = append(resourceData["policy_list"],
			utils.GenParamsFromStructSlice(resource.Policy)...,
		)

		resourceData["cors_list"] = append(resourceData["cors_list"],
			utils.GenParamsFromStructSlice(resource.CORS)...,
		)
	}
	return resourceData, nil
}

func updateResources(userData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, bucketSchema, userData["bucket_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, policySchema, userData["policy_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, corsSchema, userData["cors_list"], map[string]any{"last_updated": "test"})
}

type Bucket struct {
	ACL          ACL          `json:"acl"`
	Bucket       BucketBasic  `json:"bucket"`
	BucketInfo   BucketInfo   `json:"bucketInfo"`
	Cors         Cors         `json:"cors"`
	Encryption   Encryption   `json:"encryption"`
	Logging      Logging      `json:"logging"`
	ObjACL       []ObjACL     `json:"objAcl"`
	Policy       string       `json:"policy"`
	PolicyStatus PolicyStatus `json:"policyStatus"`
	Referer      Referer      `json:"referer"`
}

type ACL struct {
	Owner             ACLOwner          `json:"Owner"`
	AccessControlList AccessControlList `json:"AccessControlList"`
}

type AccessControlList struct {
	Grant string `json:"grant"`
}

type ACLOwner struct {
	ID          string `json:"ID"`
	DisplayName string `json:"DisplayName"`
}

type BucketBasic struct {
	XMLName      XMLName `json:"XMLName"`
	Name         string  `json:"Name"`
	Location     string  `json:"Location"`
	CreationDate string  `json:"CreationDate"`
	StorageClass string  `json:"StorageClass"`
	Region       string  `json:"Region"`
}

type XMLName struct {
	Space string `json:"Space"`
	Local string `json:"Local"`
}

type BucketInfo struct {
	XMLName                XMLName         `json:"XMLName"`
	Name                   string          `json:"Name"`
	AccessMonitor          string          `json:"AccessMonitor"`
	Location               string          `json:"Location"`
	CreationDate           string          `json:"CreationDate"`
	ExtranetEndpoint       string          `json:"ExtranetEndpoint"`
	IntranetEndpoint       string          `json:"IntranetEndpoint"`
	ACL                    string          `json:"ACL"`
	RedundancyType         string          `json:"RedundancyType"`
	Owner                  BucketInfoOwner `json:"Owner"`
	StorageClass           string          `json:"StorageClass"`
	SSERule                SSERule         `json:"SseRule"`
	Versioning             string          `json:"Versioning"`
	TransferAcceleration   string          `json:"TransferAcceleration"`
	CrossRegionReplication string          `json:"CrossRegionReplication"`
}

type BucketInfoOwner struct {
	XMLName     XMLName `json:"XMLName"`
	ID          string  `json:"ID"`
	DisplayName string  `json:"DisplayName"`
}

type Cors struct {
	CORSRules    []CORSRule `json:"CORSRules"`
	ResponseVary bool       `json:"ResponseVary"`
}

type CORSRule struct {
	XMLName       XMLName  `json:"XMLName"`
	AllowedOrigin []string `json:"AllowedOrigin"`
	AllowedMethod []string `json:"AllowedMethod"`
	AllowedHeader []string `json:"AllowedHeader"`
	ExposeHeader  []string `json:"ExposeHeader"`
	MaxAgeSeconds int64    `json:"MaxAgeSeconds"`
}

type Logging struct {
	LoggingEnabled LoggingEnabled `json:"LoggingEnabled"`
}

type LoggingEnabled struct {
	TargetBucket string `json:"TargetBucket"`
	TargetPrefix string `json:"TargetPrefix"`
}

type ObjACL struct {
	ACL    string `json:"acl"`
	Object string `json:"object"`
}

type PolicyStatus struct {
	PolicyStatus PolicyStatusClass `json:"PolicyStatus"`
}

type PolicyStatusClass struct {
	IsPublic bool `json:"isPublic"`
}

type SSERule struct {
	XMLName           XMLName `json:"XMLName"`
	KMSMasterKeyID    string  `json:"KMSMasterKeyID"`
	SSEAlgorithm      string  `json:"SSEAlgorithm"`
	KMSDataEncryption string  `json:"KMSDataEncryption"`
}

type Encryption struct {
	ApplyServerSideEncryptionByDefault SSERule `json:"ApplyServerSideEncryptionByDefault"`
}

type Referer struct {
	AllowEmptyReferer        bool             `json:"AllowEmptyReferer"`
	AllowTruncateQueryString bool             `json:"AllowTruncateQueryString"`
	TruncatePath             bool             `json:"TruncatePath"`
	RefererList              RefererList      `json:"RefererList"`
	RefererBlacklist         RefererBlacklist `json:"RefererBlacklist"`
}

type RefererBlacklist struct {
	Referer []string `json:"Referer"`
}

type RefererList struct {
	Referer []string `json:"Referer"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.BucketGraph, error) {
	original := &Bucket{}
	resource := &model.BucketGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Bucket.Name
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "bucket", original.Bucket.Name)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "bucket"
	resource.Name = original.Bucket.Name

	resource.PrivateEndpoint = fmt.Sprintf("%s.%s", original.Bucket.Name, original.BucketInfo.IntranetEndpoint)
	resource.PublicEndpoint = fmt.Sprintf("%s.%s", original.Bucket.Name, original.BucketInfo.ExtranetEndpoint)
	resource.Policy = provider_utils.ParsePolicyDocument(original.Policy, resource.UID)

	switch original.ACL.AccessControlList.Grant {
	case "private":
		resource.ACL = []string{"private"}
	case "public-read":
		resource.ACL = []string{"public-read"}
	case "public-read-write":
		resource.ACL = []string{"public-read", "public-write"}
	}
	resource.CORS = lo.Map(original.Cors.CORSRules, func(rule CORSRule, _ int) *model.BucketCORSRuleGraph {
		content, _ := sonic.MarshalString(rule)
		return &model.BucketCORSRuleGraph{
			BaseNode: model.BaseNode{
				UID:       utils.GenerateUID(provider_utils.ProviderID, "bucket_cors", content),
				TargetUID: resource.UID,
			},
			AllowedOrigins: rule.AllowedOrigin,
			AllowedMethods: rule.AllowedMethod,
			AllowedHeaders: rule.AllowedHeader,
			ExposeHeaders:  rule.ExposeHeader,
			MaxAgeSeconds:  int(rule.MaxAgeSeconds),
		}
	})
	resource.EncryptionEnabled = original.Encryption.ApplyServerSideEncryptionByDefault.SSEAlgorithm != ""
	resource.EncryptionAlgorithm = original.Encryption.ApplyServerSideEncryptionByDefault.SSEAlgorithm
	resource.LoggingEnabled = original.Logging.LoggingEnabled.TargetBucket != ""
	resource.AllowEmptyReferer = original.Referer.AllowEmptyReferer
	resource.AllowTruncateQueryString = original.Referer.AllowTruncateQueryString
	resource.TruncatePath = original.Referer.TruncatePath
	resource.RefererWhiteList = original.Referer.RefererList.Referer
	resource.RefererBlackList = original.Referer.RefererBlacklist.Referer

	return resource, nil
}
