package user

import "AssetStandardizer/graph"

var userSchema = graph.NodeSchema{
	Label: "IAMUser",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"last_updated":       {Name: "last_updated", SetInKwargs: true},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"display_name":       {Name: "display_name"},
		"enabled":            {Name: "enabled"},
		"mfa_enabled":        {Name: "mfa_enabled"},
		"login_allowed":      {Name: "login_allowed"},
		"created_at":         {Name: "created_at"},
		"last_login_at":      {Name: "last_login_at"},
	},
}

var policySchema = graph.NodeSchema{
	Label: "IAMPolicy",
	Properties: map[string]graph.PropertyRef{
		"uid":          {Name: "uid"},
		"last_updated": {Name: "last_updated", SetInKwargs: true},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "IAMUser",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			Direction: graph.OUTWARD,
			RelLabel:  "ATTACHED_TO",
			Properties: graph.RelProperties{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
