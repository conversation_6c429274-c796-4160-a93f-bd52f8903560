package peer_connection

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"strings"
	"time"

	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"github.com/samber/lo"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "peer-connection"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "vpc_peerConnection_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["peer_connection_list"] = append(resourceData["peer_connection_list"],
			utils.GenParamsFromStruct(resource),
		)
		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.FromVPC)...,
		)
		resourceData["vpc_list"] = append(resourceData["vpc_list"],
			utils.GenParamsFromStructSlice(resource.ToVPC)...,
		)
	}
	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, peerConnectionSchema, resourceData["peer_connection_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, vpcSchema, resourceData["vpc_list"], map[string]any{"last_updated": "test"})
}

type PeerConnection struct {
	PeerConnection PeerConnectionClass `json:"peerConnection"`
}

type PeerConnectionClass struct {
	InstanceID        string  `json:"InstanceId"`
	GmtCreate         string  `json:"GmtCreate"`
	GmtModified       string  `json:"GmtModified"`
	Name              string  `json:"Name"`
	Description       string  `json:"Description"`
	OwnerID           int64   `json:"OwnerId"`
	AcceptingOwnerUid int64   `json:"AcceptingOwnerUid"`
	RegionID          string  `json:"RegionId"`
	AcceptingRegionID string  `json:"AcceptingRegionId"`
	Bandwidth         int64   `json:"Bandwidth"`
	Status            string  `json:"Status"`
	BizStatus         string  `json:"BizStatus"`
	GmtExpired        string  `json:"GmtExpired"`
	ResourceGroupID   string  `json:"ResourceGroupId"`
	Vpc               PeerVpc `json:"Vpc"`
	AcceptingVpc      PeerVpc `json:"AcceptingVpc"`
	Tags              []Tag   `json:"Tags"`
}

type PeerVpc struct {
	VpcID     string   `json:"VpcId"`
	Ipv4Cidrs []string `json:"Ipv4Cidrs"`
	Ipv6Cidrs []string `json:"Ipv6Cidrs"`
}

type Tag struct {
	TagKey   string `json:"TagKey"`
	TagValue string `json:"TagValue"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.PeerConnectionGraph, error) {
	original := &PeerConnection{}
	resource := &model.PeerConnectionGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.PeerConnection.InstanceID
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "peer-connection", original.PeerConnection.InstanceID)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "peer-connection"
	resource.Name = original.PeerConnection.Name
	resource.Description = original.PeerConnection.Description
	resource.OriginalLabels = lo.Map(original.PeerConnection.Tags, func(tag Tag, _ int) *model.KVGraph {
		return utils.NewLabel(tag.TagKey, tag.TagValue, resource.UID)
	})

	resource.Status = lo.Ternary(strings.EqualFold(original.PeerConnection.Status, "Activated"), "running", "stopped")
	resource.FromVPC = append(resource.FromVPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.PeerConnection.Vpc.VpcID),
			TargetUID: resource.UID,
		},
	})
	resource.ToVPC = append(resource.ToVPC, &model.VPCGraph{
		BaseNode: model.BaseNode{
			UID:       utils.GenerateUID(resource.Provider, "vpc", original.PeerConnection.AcceptingVpc.VpcID),
			TargetUID: resource.UID,
		},
	})
	return resource, nil
}
