package sls

import "AssetStandardizer/graph"

var slsSchema = graph.NodeSchema{
	Label: "LogService",
	Properties: map[string]graph.PropertyRef{
		"uid":                {Name: "uid"},
		"provider":           {Name: "provider"},
		"original_id":        {Name: "original_id"},
		"transformed_object": {Name: "transformed_object"},
		"region":             {Name: "region"},
		"last_seen":          {Name: "last_seen"},
		"description":        {Name: "description"},
		"kind":               {Name: "kind"},
		"name":               {Name: "name"},
		"project":            {Name: "project"},
		"log_store_name":     {Name: "log_store_name"},
		"log_store_configs":  {Name: "log_store_configs"},
	},
}

var slsPolicySchema = graph.NodeSchema{
	Label: "LogServicePolicyStatement",
	Properties: map[string]graph.PropertyRef{
		"uid":       {Name: "uid"},
		"effect":    {Name: "effect"},
		"action":    {Name: "action"},
		"resource":  {Name: "resource"},
		"condition": {Name: "condition"},
		"principal": {Name: "principal"},
	},
	OtherRelationships: graph.OtherRelationships{
		{
			TargetNodeLabel: "LogService",
			TargetNodeMatcher: graph.TargetNodeMatcher{
				"uid": {Name: "target_uid"},
			},
			RelLabel:  "HAS_POLICY_STATEMENT",
			Direction: graph.INWARD,
			Properties: map[string]graph.PropertyRef{
				"last_updated": {Name: "last_updated", SetInKwargs: true},
			},
		},
	},
}
