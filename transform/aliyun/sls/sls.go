package sls

import (
	"AssetStandardizer/graph"
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	provider_utils "AssetStandardizer/transform/aliyun/utils"
	"AssetStandardizer/transform/utils"
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	chjLogger "gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

var logger = chjLogger.DefaultLogger().WithFields(chjLogger.Fields{"provider": provider_utils.ProviderID, "service": "sls"})

func NewResourceService() *model.CommonResourceService {
	return &model.CommonResourceService{
		MessageType:     "sls_logstore_aggregated",
		AssetMsgCh:      make(chan *model.AssetMessage, 100),
		BufferTimeout:   time.Second * 10,
		Transform:       transform,
		UpdateResources: updateResources,
	}
}

func transform(assetMsgs []*model.AssetMessage) (map[string][]map[string]any, error) {
	resourceData := make(map[string][]map[string]any)
	for _, assetMsg := range assetMsgs {
		resource, err := parseOne(assetMsg)
		if err != nil {
			logger.Errorf("parseOne failed, assetMsg: %v, err: %v", assetMsg, err)
			continue
		} else {
			resource.TransformedObject, _ = sonic.MarshalString(resource)
		}

		resourceData["sls_list"] = append(resourceData["sls_list"], utils.GenParamsFromStruct(resource))

		resourceData["policy_statement_list"] = append(resourceData["policy_statement_list"],
			utils.GenParamsFromStructSlice(resource.PolicyDocument)...,
		)
	}

	return resourceData, nil
}

func updateResources(resourceData map[string][]map[string]any) {
	n4jSession := infra.Neo4j.NewSession(context.Background(), neo4j.SessionConfig{AccessMode: neo4j.AccessModeWrite})
	defer n4jSession.Close(context.Background())

	graph.Run(n4jSession, slsSchema, resourceData["sls_list"], map[string]any{"last_updated": "test"})
	graph.Run(n4jSession, slsPolicySchema, resourceData["policy_statement_list"], map[string]any{"last_updated": "test"})
}

type Sls struct {
	LogStoreConfigs []map[string]any `json:"logStoreConfigs"`
	LogStoreDetail  LogStoreDetail   `json:"logStoreDetail"`
	Policy          string           `json:"policy"`
	Project         Project          `json:"project"`
}

type LogStoreDetail struct {
	AppendMeta     bool   `json:"appendMeta"`
	AutoSplit      bool   `json:"autoSplit"`
	CreateTime     int64  `json:"createTime"`
	EnableTracking bool   `json:"enable_tracking"`
	LastModifyTime int64  `json:"lastModifyTime"`
	LogstoreName   string `json:"logstoreName"`
	MaxSplitShard  int64  `json:"maxSplitShard"`
	Mode           string `json:"mode"`
	ProductType    string `json:"productType"`
	ShardCount     int64  `json:"shardCount"`
	TelemetryType  string `json:"telemetryType"`
	TTL            int64  `json:"ttl"`
}

type Project struct {
	CreateTime         string `json:"createTime"`
	DataRedundancyType string `json:"dataRedundancyType"`
	Description        string `json:"description"`
	LastModifyTime     string `json:"lastModifyTime"`
	Owner              string `json:"owner"`
	ProjectName        string `json:"projectName"`
	Region             string `json:"region"`
	ResourceGroupID    string `json:"resourceGroupId"`
	Status             string `json:"status"`
}

func parseOne(assetMsg *model.AssetMessage) (*model.LogServiceGraph, error) {
	original := &Sls{}
	resource := &model.LogServiceGraph{}
	sonic.Unmarshal(assetMsg.RawLog, original)

	resource.Provider = provider_utils.ProviderID
	resource.OriginalID = original.Project.ProjectName
	resource.OriginalObject = string(assetMsg.RawLog)
	resource.UID = utils.GenerateUID(resource.Provider, "log-service", original.Project.ProjectName)
	resource.Region = assetMsg.Region
	resource.LastSeen = time.Now().UnixMilli()
	resource.Kind = "log-service"
	resource.Name = original.Project.ProjectName
	resource.Description = original.Project.Description

	resource.Project = original.Project.ProjectName
	resource.LogStoreName = original.LogStoreDetail.LogstoreName
	resource.LogStoreConfigs, _ = sonic.MarshalString(original.LogStoreConfigs)
	resource.PolicyDocument = provider_utils.ParsePolicyDocument(original.Policy, resource.UID)
	return resource, nil
}
