package lixiang

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type FeishuAppAk struct {
	App FeishuApp `json:"app"`
}

type FeishuApp struct {
	AppID           string  `json:"app_id"`
	CreatorID       string  `json:"creator_id"`
	Status          int64   `json:"status"`
	SceneType       int64   `json:"scene_type"`
	PaymentType     int64   `json:"payment_type"`
	CreateSource    string  `json:"create_source"`
	OnlineVersionID string  `json:"online_version_id"`
	AppName         string  `json:"app_name"`
	AvatarURL       string  `json:"avatar_url"`
	Description     string  `json:"description"`
	Scopes          []Scope `json:"scopes"`
	BackHomeURL     string  `json:"back_home_url"`
	I18N            []I18N  `json:"i18n"`
	PrimaryLanguage string  `json:"primary_language"`
	Owner           Owner   `json:"owner"`
}

type I18N struct {
	I18NKey     string `json:"i18n_key"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type Owner struct {
	Type                   int64  `json:"type"`
	OwnerID                string `json:"owner_id"`
	Name                   string `json:"name"`
	HelpDesk               string `json:"help_desk"`
	Email                  string `json:"email"`
	Phone                  string `json:"phone"`
	CustomerServiceAccount string `json:"customer_service_account"`
}

type Scope struct {
	Scope       string   `json:"scope"`
	Description string   `json:"description"`
	Level       int64    `json:"level"`
	TokenTypes  []string `json:"token_types"`
}

func parseFeishuAppAk(raw map[string]any) model.Asset {
	original := &FeishuAppAk{}
	meta := &model.ObjectMeta{}
	ak := &model.AK{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.App.AppID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "ak", original.App.AppID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "_feishu_app_ak"
	meta.Name = original.App.AppID

	ak.Enabled = utils.DataPointer(original.App.Status == 1)
	ak.UserID = utils.GenerateUID(meta.Provider, "user", original.App.Owner.OwnerID)
	ak.UserName = original.App.Owner.OwnerID

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, ak)
	return asset
}
