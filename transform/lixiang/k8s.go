package lixiang

import (
	"AssetStandardizer/infra"
	"AssetStandardizer/providers/utils"
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type K8S struct {
	PrivateEndpoint    string `json:"private_endpoint"`
	PublicEndpoint     string `json:"public_endpoint"`
	UUID               string `json:"uuid"`
	ClusterName        string `json:"cluster_name"`
	ProviderName       string `json:"provider_name"`
	ProviderID         string `json:"provider_id"`
	Provider           string `json:"provider"`
	VDC                string `json:"vdc"`
	Business           string `json:"business"`
	IsProd             bool   `json:"is_prod"`
	IsFactory          bool   `json:"is_factory"`
	Region             string `json:"region"`
	K8SVersion         string `json:"k8s_version"`
	Status             string `json:"status"`
	AuditLogEnabled    bool   `json:"audit_log_enabled"`
	KubelinkerDeployed bool   `json:"kubelinker_deployed"`
	KubelinkerUUID     string `json:"kubelinker_uuid"`
	CreateTime         int64  `json:"create_time"`
	Desc               string `json:"desc"`
	Raw                any    `json:"raw"`
	SyncedAt           int64  `json:"synced_at"`
}

type ClusterData struct {
	CMDBCluster       CMDBCluster       `json:"cmdb_cluster"`
	K8SCluster        K8SCluster        `json:"k8s_cluster"`
	CspmCluster       CspmCluster       `json:"cspm_cluster"`
	KubelinkerCluster KubelinkerCluster `json:"kubelinker_cluster"`
	AuditLogEnabled   bool              `json:"audit_log_enabled"`
}

type CMDBCluster struct {
	AccountCode           string `json:"account_code"`
	APIEndpoint           string `json:"api_endpoint"`
	AssetsID              string `json:"assets_id"`
	Cell                  string `json:"cell"`
	CellName              string `json:"cell_name"`
	CloudID               string `json:"cloud_id"`
	CloudName             string `json:"cloud_name"`
	Cluster               string `json:"cluster"`
	ClusterType           string `json:"cluster_type"`
	CreateBy              string `json:"create_by"`
	CreateDateInHistory   string `json:"create_date_in_history"`
	CreateTime            string `json:"create_time"`
	Description           string `json:"description"`
	Env                   string `json:"env"`
	GoVersion             string `json:"go_version"`
	Group                 string `json:"group"`
	K8SVersion            string `json:"k8s_version"`
	Name                  string `json:"name"`
	NodeCount             int64  `json:"node_count"`
	Notes                 string `json:"notes"`
	Owner                 string `json:"owner"`
	RegionFlag            string `json:"region_flag"`
	Status                string `json:"status"`
	TenantID              string `json:"tenant_id"`
	TenantIDName          string `json:"tenant_id_name"`
	TotalCPU              int64  `json:"total_cpu"`
	TotalEphemeralStorage int64  `json:"total_ephemeral_storage"`
	TotalMemory           int64  `json:"total_memory"`
	UpdateBy              string `json:"update_by"`
	UpdateTime            string `json:"update_time"`
	Used                  string `json:"used"`
	VpcID                 string `json:"vpc_id"`
}

type CspmCluster struct {
	Provider        string   `json:"provider"`
	OriginalLabels  []KV     `json:"original_labels"`
	OriginalID      string   `json:"original_id"`
	OriginalObject  any      `json:"original_object"`
	Region          string   `json:"region"`
	Uid             string   `json:"uid"`
	UpdateTime      int64    `json:"update_time"`
	Description     string   `json:"description"`
	Name            string   `json:"name"`
	Status          string   `json:"status"`
	EngineVersion   string   `json:"engine_version"`
	PrivateEndpoint string   `json:"private_endpoint"`
	PublicEndpoint  string   `json:"public_endpoint"`
	PodCidrs        []string `json:"pod_cidrs"`
	ServiceCidrs    []string `json:"service_cidrs"`
}

type K8SCluster struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	Intro        string `json:"intro"`
	Status       string `json:"status"`
	Labels       []KV   `json:"labels"`
	Annotations  []KV   `json:"annotations"`
	Endpoint     string `json:"endpoint"`
	CreatedBy    string `json:"createdBy"`
	CreatedAt    string `json:"createdAt"`
	UpdatedAt    string `json:"updatedAt"`
	KsLink       string `json:"ks_link"`
	CERTExpireAt string `json:"certExpireAt"`
}

type KV struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type KubelinkerCluster struct {
	ClusterName  string `json:"cluster_name"`
	KubeSystemID string `json:"kube_system_id"`
}

var providerNameMap = map[string]string{
	"aws-cn": "aws",
	"bidu":   "baidu",
	"bdu":    "baidu",
	"chj":    "lixiang",
	"vol":    "volcengine",
}

var regionMap = map[string]string{
	"北京":        "cn-bj",
	"华北2（北京）":   "cn-bj",
	"华北6（乌兰察布）": "cn-wlcb",
	"保定":        "cn-bd",
	"成都":        "cn-cd",
	"广州":        "cn-gz",
	"苏州":        "cn-su",
	"南京":        "cn-nj",
	"阳泉":        "cn-yq",
	"宁夏":        "cn-nx",
	"上海":        "cn-sh",
	"武汉":        "cn-wh",
	"香港":        "cn-hk",
}

var outdatedClusterUUIDs = []string{
	"08ac8c7a-651c-50d2-9ae7-3e27be07aa07",
}

func parseK8s(raw map[string]any) error {
	original := ClusterData{}
	utils.Decode(raw["raw_log"], &original)
	k8s := K8S{}

	k8s.PrivateEndpoint = lo.CoalesceOrEmpty(original.K8SCluster.Endpoint, original.CMDBCluster.APIEndpoint, original.CspmCluster.PrivateEndpoint)
	k8s.PublicEndpoint = lo.CoalesceOrEmpty(original.CspmCluster.PublicEndpoint)

	k8s.UUID = utils.GenerateUID(ProviderID, "kubernetes", lo.CoalesceOrEmpty(k8s.PrivateEndpoint, k8s.PublicEndpoint))

	if k8s.UUID == "" {
		return fmt.Errorf("failed to generate k8s uuid")
	} else if slices.Contains(outdatedClusterUUIDs, k8s.UUID) {
		// skip outdated clusters
		return nil
	}

	k8s.ClusterName = strings.ToLower(lo.CoalesceOrEmpty(original.K8SCluster.Name, original.CMDBCluster.Name, original.KubelinkerCluster.ClusterName))
	vdc, bussiness, provider, env := parseName(k8s.ClusterName)

	k8sPlatformProviderId := extractK8SLabel(original.K8SCluster.Annotations, "vendor-resource-id")
	k8s.ProviderID = lo.CoalesceOrEmpty(original.CMDBCluster.CloudID, original.CspmCluster.OriginalID, k8sPlatformProviderId, original.CMDBCluster.Name)

	k8s.ProviderName = lo.CoalesceOrEmpty(original.CspmCluster.Name, original.CMDBCluster.Name, original.K8SCluster.Name)

	k8sPlatformProvider := extractK8SLabel(original.K8SCluster.Labels, "provider")
	k8s.Provider = lo.CoalesceOrEmpty(original.CspmCluster.Provider, k8sPlatformProvider, original.CMDBCluster.ClusterType, provider)
	k8s.Provider = lo.CoalesceOrEmpty(providerNameMap[k8s.Provider], k8s.Provider)

	k8sPlatformVDC := extractK8SLabel(original.K8SCluster.Labels, "vdc")
	k8s.VDC = lo.CoalesceOrEmpty(vdc, k8sPlatformVDC)

	k8sPlatformBusiness := extractK8SLabel(original.K8SCluster.Labels, "business")
	k8s.Business = lo.CoalesceOrEmpty(bussiness, k8sPlatformBusiness)

	k8sPlatformEnv := extractK8SLabel(original.K8SCluster.Labels, "env")
	k8s.IsProd = lo.Ternary(k8sPlatformEnv == "prod", true, false) || env == "p" || !strings.Contains(k8s.ClusterName, "np")

	k8s.IsFactory = strings.Contains(k8s.ClusterName, "fa")

	k8sPlatformRegion := extractK8SLabel(original.K8SCluster.Labels, "region")
	k8s.Region = lo.CoalesceOrEmpty(k8sPlatformRegion, original.CspmCluster.Region)
	k8s.Region = lo.CoalesceOrEmpty(regionMap[k8s.Region], k8s.Region)

	k8s.K8SVersion = lo.CoalesceOrEmpty(original.CMDBCluster.K8SVersion, original.CspmCluster.EngineVersion)

	var k8sPlatformStatus string
	switch original.K8SCluster.Status {
	case "Ready":
		k8sPlatformStatus = "running"
	case "NotReady":
		k8sPlatformStatus = "stopped"
	default:
		k8sPlatformStatus = ""
	}
	k8s.Status = lo.CoalesceOrEmpty(k8sPlatformStatus, original.CspmCluster.Status, original.CMDBCluster.Status)

	createTime, err := time.Parse(time.RFC3339, lo.CoalesceOrEmpty(original.K8SCluster.CreatedAt, original.CMDBCluster.CreateTime))
	if err == nil {
		k8s.CreateTime = createTime.UnixMilli()
	}

	k8s.AuditLogEnabled = original.AuditLogEnabled
	k8s.KubelinkerDeployed = original.KubelinkerCluster.KubeSystemID != ""
	k8s.KubelinkerUUID = original.KubelinkerCluster.KubeSystemID

	k8s.Desc = strings.Join(
		lo.Filter([]string{original.CspmCluster.Description, original.K8SCluster.Intro, original.CMDBCluster.Description},
			func(s string, _ int) bool { return s != "" }),
		"\n",
	)
	k8s.Raw = raw["raw_log"]
	k8s.SyncedAt = time.Now().UnixMilli()

	opts := options.Replace().SetUpsert(true)
	_, err = infra.Mongo.Collection("kubernetes_clusters").ReplaceOne(context.Background(), bson.M{"uuid": k8s.UUID}, k8s, opts)
	return err
}

func extractK8SLabel(labels []KV, key string) string {
	kv := lo.FindOrElse(labels, KV{}, func(kv KV) bool {
		return kv.Key == key
	})
	return kv.Value
}

// vdc-business-provider-env-xx
func parseName(name string) (string, string, string, string) {
	// special case
	name = strings.TrimSuffix(name, "-5k")

	parts := strings.Split(name, "-")
	if len(parts) != 5 {
		return "", "", "", ""
	}
	return parts[0], parts[1], parts[2], parts[3]
}
