package lixiang

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"

	"github.com/samber/lo"
)

type ApolloApp struct {
	App App   `json:"app"`
	Env []Env `json:"env"`
}

type App struct {
	Name                       string `json:"name"`
	AppID                      string `json:"appId"`
	OrgID                      string `json:"orgId"`
	OrgName                    string `json:"orgName"`
	OwnerName                  string `json:"ownerName"`
	OwnerEmail                 string `json:"ownerEmail"`
	DataChangeCreatedBy        string `json:"dataChangeCreatedBy"`
	DataChangeLastModifiedBy   string `json:"dataChangeLastModifiedBy"`
	DataChangeCreatedTime      string `json:"dataChangeCreatedTime"`
	DataChangeLastModifiedTime string `json:"dataChangeLastModifiedTime"`
}

type Env struct {
	Env       string    `json:"env"`
	Configs   []Config  `json:"configs"`
	Aks       []AppAk   `json:"aks"`
	SECStatus SECStatus `json:"sec_status"`
}

type AppAk struct {
	ID                         int64  `json:"id"`
	AppID                      string `json:"appId"`
	Enabled                    bool   `json:"enabled"`
	DataChangeCreatedBy        string `json:"dataChangeCreatedBy"`
	DataChangeLastModifiedBy   string `json:"dataChangeLastModifiedBy"`
	DataChangeCreatedTime      string `json:"dataChangeCreatedTime"`
	DataChangeLastModifiedTime string `json:"dataChangeLastModifiedTime"`
}

type SECStatus struct {
	HasKeyring bool   `json:"hasKeyring"`
	Status     Status `json:"status"`
}

type Status struct {
	CommonEnc  ConfigStatus `json:"COMMON_ENC"`
	ApolloSign ConfigStatus `json:"APOLLO_SIGN"`
}

type ConfigStatus struct {
	Enabled             bool            `json:"enabled"`
	ClusterEnableStatus map[string]bool `json:"clusterEnableStatus"`
}

type Config struct {
	Env           string        `json:"env"`
	Cluster       string        `json:"cluster"`
	Namespace     string        `json:"namespace"`
	ConfigExists  bool          `json:"config_exists"`
	SensitiveConf SensitiveConf `json:"sensitive_conf"`
}

type SensitiveConf struct {
	AppID          string         `json:"appId"`
	Cluster        string         `json:"cluster"`
	Configurations map[string]any `json:"configurations"`
	Env            string         `json:"env"`
	Namespace      string         `json:"namespace"`
	OwnerName      string         `json:"ownerName"`
	ExistAk        bool           `json:"exist_ak"`
}

func parseApolloApp(raw map[string]any) model.Asset {
	original := &ApolloApp{}
	meta := &model.ObjectMeta{}
	app := &model.ApolloApp{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.App.AppID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "apollo-app", original.App.AppID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "apollo-app"
	meta.Name = original.App.Name

	app.OrgID = original.App.OrgID
	app.OrgName = original.App.OrgName
	app.OwnerName = original.App.OwnerName
	app.OwnerEmail = original.App.OwnerEmail

	for _, env := range original.Env {
		signedClusters := lo.Keys(lo.PickByValues(env.SECStatus.Status.ApolloSign.ClusterEnableStatus, []bool{true}))
		encryptedClusters := lo.Keys(lo.PickByValues(env.SECStatus.Status.CommonEnc.ClusterEnableStatus, []bool{true}))

		for _, config := range env.Configs {
			app.Envs = append(app.Envs, model.ApolloAppEnv{
				EnvName:       env.Env,
				Cluster:       config.Cluster,
				Namespace:     config.Namespace,
				ConfigExists:  config.ConfigExists,
				Signed:        lo.Contains(signedClusters, config.Cluster) || len(env.Aks) > 0,
				Encrypted:     lo.Contains(signedClusters, config.Cluster) && lo.Contains(encryptedClusters, config.Cluster),
				SensitiveConf: lo.Ternary(config.SensitiveConf.Configurations != nil, config.SensitiveConf.Configurations, map[string]any{}),
				ExistAk:       config.SensitiveConf.ExistAk,
			})
		}
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, app)
	return asset
}
