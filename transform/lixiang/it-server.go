package lixiang

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
)

type ItServerAgg struct {
	Server Server `json:"server"`
}

type Server struct {
	Account                 string            `json:"account"`
	AccountID               string            `json:"account_id"`
	AutoRenew               bool              `json:"auto_renew"`
	BackupGuestStatus       string            `json:"backup_guest_status"`
	BillingType             string            `json:"billing_type"`
	BIOS                    string            `json:"bios"`
	BootOrder               string            `json:"boot_order"`
	Brand                   string            `json:"brand"`
	CanDelete               bool              `json:"can_delete"`
	CanRecycle              bool              `json:"can_recycle"`
	CanUpdate               bool              `json:"can_update"`
	Cdrom                   string            `json:"cdrom"`
	CdromSupport            bool              `json:"cdrom_support"`
	CloudDesc               string            `json:"cloud_desc"`
	CloudEnv                string            `json:"cloud_env"`
	Cloudregion             string            `json:"cloudregion"`
	CloudregionID           string            `json:"cloudregion_id"`
	CreatedAt               string            `json:"created_at"`
	Deleted                 bool              `json:"deleted"`
	DisableDelete           bool              `json:"disable_delete"`
	Disk                    int64             `json:"disk"`
	DiskCount               int64             `json:"disk_count"`
	Disks                   string            `json:"disks"`
	DisksInfo               []DisksInfo       `json:"disks_info"`
	DomainID                string            `json:"domain_id"`
	EIP                     string            `json:"eip"`
	Environment             string            `json:"environment"`
	ExternalID              string            `json:"external_id"`
	Freezed                 bool              `json:"freezed"`
	Host                    string            `json:"host"`
	HostID                  string            `json:"host_id"`
	HostServiceStatus       string            `json:"host_service_status"`
	HostStatus              string            `json:"host_status"`
	HostType                string            `json:"host_type"`
	Hostname                string            `json:"hostname"`
	Hypervisor              string            `json:"hypervisor"`
	ID                      string            `json:"id"`
	ImportedAt              string            `json:"imported_at"`
	InternetMaxBandwidthOut int64             `json:"internet_max_bandwidth_out"`
	IPS                     string            `json:"ips"`
	IsDaemon                bool              `json:"is_daemon"`
	IsEmulated              bool              `json:"is_emulated"`
	IsGPU                   bool              `json:"is_gpu"`
	IsPrepaidRecycle        bool              `json:"is_prepaid_recycle"`
	IsSystem                bool              `json:"is_system"`
	Machine                 string            `json:"machine"`
	Macs                    string            `json:"macs"`
	Manager                 string            `json:"manager"`
	ManagerDomain           string            `json:"manager_domain"`
	ManagerDomainID         string            `json:"manager_domain_id"`
	ManagerID               string            `json:"manager_id"`
	ManagerProject          string            `json:"manager_project"`
	ManagerProjectID        string            `json:"manager_project_id"`
	Metadata                map[string]string `json:"metadata"`
	MonitorURL              string            `json:"monitor_url"`
	Name                    string            `json:"name"`
	Nics                    []NIC             `json:"nics"`
	OSArch                  string            `json:"os_arch"`
	OSType                  string            `json:"os_type"`
	PendingDeleted          bool              `json:"pending_deleted"`
	Progress                int64             `json:"progress"`
	ProgressMbps            int64             `json:"progress_mbps"`
	Project                 string            `json:"project"`
	ProjectDomain           string            `json:"project_domain"`
	ProjectSrc              string            `json:"project_src"`
	Provider                string            `json:"provider"`
	QgaStatus               string            `json:"qga_status"`
	Region                  string            `json:"region"`
	RegionID                string            `json:"region_id"`
	ShutdownBehavior        string            `json:"shutdown_behavior"`
	Secgroup                []Secgroup        `json:"secgroup"`
	Source                  string            `json:"source"`
	SrcIPCheck              bool              `json:"src_ip_check"`
	SrcMACCheck             bool              `json:"src_mac_check"`
	SshableLastState        bool              `json:"sshable_last_state"`
	Status                  string            `json:"status"`
	Tenant                  string            `json:"tenant"`
	TenantID                string            `json:"tenant_id"`
	Throughput              int64             `json:"throughput"`
	UpdateVersion           int64             `json:"update_version"`
	UpdatedAt               string            `json:"updated_at"`
	VcpuCount               int64             `json:"vcpu_count"`
	Vdi                     string            `json:"vdi"`
	VGA                     string            `json:"vga"`
	VmemSize                int64             `json:"vmem_size"`
	Vpc                     string            `json:"vpc"`
	VpcExternalAccessMode   string            `json:"vpc_external_access_mode"`
	VpcID                   string            `json:"vpc_id"`
	Zone                    string            `json:"zone"`
	ZoneID                  string            `json:"zone_id"`
}

type DisksInfo struct {
	AioMode     string `json:"aio_mode"`
	Bps         int64  `json:"bps"`
	CacheMode   string `json:"cache_mode"`
	DiskFormat  string `json:"disk_format"`
	DiskType    string `json:"disk_type"`
	Driver      string `json:"driver"`
	ID          string `json:"id"`
	Index       int64  `json:"index"`
	Iops        int64  `json:"iops"`
	MediumType  string `json:"medium_type"`
	Name        string `json:"name"`
	Size        int64  `json:"size"`
	StorageID   string `json:"storage_id"`
	StorageType string `json:"storage_type"`
}

type NIC struct {
	IPAddr    string `json:"ip_addr"`
	IsExit    bool   `json:"is_exit"`
	MAC       string `json:"mac"`
	NetworkID string `json:"network_id"`
	VpcID     string `json:"vpc_id"`
}

type Secgroup struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

func parseItServer(raw map[string]any) model.Asset {
	original := &ItServerAgg{}
	meta := &model.ObjectMeta{}
	server := &model.ECS{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Server.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "ecs", original.Server.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "ecs"
	meta.Name = original.Server.Name
	meta.Description = original.Server.CloudDesc

	server.Hostname = original.Server.Hostname
	server.Class = "cvm"
	server.DeleteProtection = &original.Server.DisableDelete
	server.Spec = fmt.Sprintf("%dC%dG", original.Server.VcpuCount, original.Server.VmemSize/1024)
	server.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Server.VpcID)
	server.Status = lo.Ternary(strings.EqualFold(original.Server.Status, "running"), "running", "stopped")

	server.OSName = lo.CoalesceOrEmpty(original.Server.Metadata["os_full_name"], original.Server.Metadata["os_distribution"])
	server.OSType = strings.ToLower(original.Server.OSType)

	server.SGIDList = lo.Map(original.Server.Secgroup, func(secgroup Secgroup, _ int) string {
		return utils.GenerateUID(ProviderID, "security-group", secgroup.ID)
	})

	server.ENIIDList = lo.Map(original.Server.Nics, func(nic NIC, _ int) string {
		return utils.GenerateUID(ProviderID, "eni", nic.MAC)
	})

	privateIPs := lo.CoalesceOrEmpty(original.Server.IPS, original.Server.Metadata["sync_ips"])
	server.PrivateIPList = lo.Ternary(privateIPs != "", strings.Split(privateIPs, ","), []string{})
	if len(server.PrivateIPList) > 0 {
		server.PrimaryPrivateIP = server.PrivateIPList[0]
	}

	server.PublicIPList = lo.Ternary(original.Server.EIP != "", strings.Split(original.Server.EIP, ","), []string{})
	if len(server.PublicIPList) > 0 {
		server.PrimaryPublicIP = server.PublicIPList[0]
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, server)
	return asset
}
