package lixiang

import (
	"AssetStandardizer/infra"
	"AssetStandardizer/providers/utils"
	"context"
	"strings"
	"time"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
)

type LbListener struct {
	LBID        string `json:"lb_id"`
	LBPublicIP  string `json:"lb_public_ip"`
	LBPrivateIP string `json:"lb_private_ip"`
	Protocol    string `json:"protocol"`
	OwnerID     string `json:"owner_id"`
	ListenPort  int64  `json:"listenPort"`
}

const LbTimeout = 1 * time.Minute

var LbTimer *time.Timer

func parseLb(raw map[string]any) error {
	original := &LbListener{}
	utils.Decode(raw["raw_log"], original)

	if LbTimer == nil {
		LbTimer = time.AfterFunc(1*time.Minute, SetLbListenerAuthorized)
	} else {
		LbTimer.Reset(1 * time.Minute)
	}

	filter := bson.M{
		"lb_original_id": original.LBID,
		"protocol":       strings.ToLower(original.Protocol),
		"$or": []bson.M{
			{"port": original.ListenPort},
			{"$and": []bson.M{
				{"port_start": bson.M{"$lte": original.ListenPort}},
				{"port_end": bson.M{"$gte": original.ListenPort}}},
			},
		},
	}

	update := bson.M{
		"$set": map[string]any{
			"authorized": true,
		},
	}

	_, err := infra.Mongo.Collection("cspm_resources").UpdateOne(context.TODO(), filter, update)
	return err
}

func SetLbListenerAuthorized() {
	validBefore := time.Now().Add(-LbTimeout).UnixMilli()
	filter := bson.M{"kind": "lb-listener", "authorized": bson.M{"$exists": false}, "create_time": bson.M{"$lt": validBefore}}
	update := bson.M{"$set": bson.M{"authorized": false}}
	_, err := infra.Mongo.Collection("cspm_resources").UpdateMany(context.TODO(), filter, update)
	if err != nil {
		logger.DefaultLogger().Error(err)
	}
}
