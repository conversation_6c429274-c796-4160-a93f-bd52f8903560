package lixiang

import (
	"AssetStandardizer/infra"
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"net"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/PaloAltoNetworks/pango/objects/address"
	address_group "github.com/PaloAltoNetworks/pango/objects/address/group"
	"github.com/PaloAltoNetworks/pango/objects/service"
	service_group "github.com/PaloAltoNetworks/pango/objects/service/group"
	"github.com/PaloAltoNetworks/pango/policies/rules/security"
	"github.com/PaloAltoNetworks/pango/version"
	"github.com/samber/lo"
	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/sync/errgroup"
)

type Paloalto struct {
	ControlPlane       string `json:"control_plane"`
	Version            string `json:"version"`
	ConfigXML          string `json:"config_xml"`
	PredefinedServices string `json:"predefined_services"`
}

type PaloaltoConf struct {
	Devices []DeviceEntry `xml:"devices>entry"`
}

type DeviceEntry struct {
	Name   string      `xml:"name,attr"`
	Vsyses []VsysEntry `xml:"vsys>entry"`
}

type VsysEntry struct {
	Name string `xml:"name,attr"`
	// ApplicationXML      ApplicationXML      `xml:"application"`
	// ApplicationGroupXML ApplicationGroupXML `xml:"application-group"`
	ZoneXML         ZoneXML         `xml:"zone"`
	ServiceXML      ServiceXML      `xml:"service"`
	ServiceGroupXML ServiceGroupXML `xml:"service-group"`
	AddressXML      AddressXML      `xml:"address"`
	AddressGroupXML AddressGroupXML `xml:"address-group"`
	RuleXML         RuleXML         `xml:"rulebase>security>rules"`
}

type ApplicationXML struct {
	Content string `xml:",innerxml"`
}

type ApplicationGroupXML struct {
	Content string `xml:",innerxml"`
}

type AddressXML struct {
	Content string `xml:",innerxml"`
}

type AddressGroupXML struct {
	Content string `xml:",innerxml"`
}

type ServiceXML struct {
	Content string `xml:",innerxml"`
}

type ServiceGroupXML struct {
	Content string `xml:",innerxml"`
}

type RuleXML struct {
	Content string `xml:",innerxml"`
}

type ZoneXML struct {
	Content string `xml:",innerxml"`
}

func parsePaloalto(raw map[string]any) (model.Asset, error) {
	original := &Paloalto{}
	meta := &model.ObjectMeta{}
	pa := &model.PaloaltoFirewall{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.ControlPlane
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "paloalto-ngfw", original.ControlPlane)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "paloalto-ngfw"
	meta.Name = original.ControlPlane

	paloaltoConf := &PaloaltoConf{}
	if err := xml.Unmarshal([]byte(original.ConfigXML), paloaltoConf); err != nil {
		return model.Asset{}, err
	}

	pa.Version = original.Version
	version, err := version.New(original.Version)
	if err != nil {
		return model.Asset{}, err
	}

	_, serviceNormalizer, _ := service.Versioning(version)
	predefinedServices, err := retriveEntriesFromXML(original.PredefinedServices, serviceNormalizer)
	if err != nil {
		return model.Asset{}, err
	}

	for _, device := range paloaltoConf.Devices {
		d := &model.PaloaltoDevice{Name: device.Name}
		d.Vsyses = make([]*model.PaloaltoVsys, 0, len(device.Vsyses))

		for _, vsys := range device.Vsyses {
			v := &model.PaloaltoVsys{Name: vsys.Name}

			if rules, err := parseVsysRules(vsys, &version, predefinedServices); err != nil {
				return model.Asset{}, err
			} else {
				v.Rules = rules
			}

			d.Vsyses = append(d.Vsyses, v)
		}
		pa.Devices = append(pa.Devices, d)
	}

	if lastFirewallConf, err := fetchLastRulesFromDB(original.ControlPlane); err != nil && err != mongo.ErrNoDocuments {
		return model.Asset{}, err
	} else if lastFirewallConf != nil {
		isSame, err := compareFirewallRules(lastFirewallConf, pa)
		if err != nil {
			return model.Asset{}, err
		}

		pa.InternetRulesUpdated = !isSame
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, pa)
	return asset, nil
}

func parseVsysRules(vsys VsysEntry, version *version.Number, predefinedServices []*service.Entry) ([]*model.PaloaltoRule, error) {
	errGroup := &errgroup.Group{}

	var serviceEntries []*service.Entry
	errGroup.Go(func() (err error) {
		_, serviceNormalizer, _ := service.Versioning(*version)
		serviceEntries, err = retriveEntriesFromXML(vsys.ServiceXML.Content, serviceNormalizer)
		return err
	})

	var serviceGroupEntries []*service_group.Entry
	errGroup.Go(func() (err error) {
		_, serviceGroupNormalizer, _ := service_group.Versioning(*version)
		serviceGroupEntries, err = retriveEntriesFromXML(vsys.ServiceGroupXML.Content, serviceGroupNormalizer)
		return err
	})

	var ruleEntries []*security.Entry
	errGroup.Go(func() (err error) {
		_, ruleNormalizer, _ := security.Versioning(*version)
		ruleEntries, err = retriveEntriesFromXML(vsys.RuleXML.Content, ruleNormalizer)
		return err
	})

	var addressEntries []*address.Entry
	errGroup.Go(func() (err error) {
		_, addressNormalizer, _ := address.Versioning(*version)
		addressEntries, err = retriveEntriesFromXML(vsys.AddressXML.Content, addressNormalizer)
		return err
	})

	var addressGroupEntries []*address_group.Entry
	errGroup.Go(func() (err error) {
		_, addressGroupNormalizer, _ := address_group.Versioning(*version)
		addressGroupEntries, err = retriveEntriesFromXML(vsys.AddressGroupXML.Content, addressGroupNormalizer)
		return err
	})

	if err := errGroup.Wait(); err != nil {
		return nil, err
	}

	serviceEntries = append(serviceEntries, predefinedServices...)
	s1, err := serviceNameToPortRange(serviceEntries)
	if err != nil {
		return nil, err
	}

	s2, err := serviceGroupNameToPortRange(serviceGroupEntries, s1)
	if err != nil {
		return nil, err
	}
	serviceMap := lo.Assign(s1, s2)

	a1, err := addressNameToIPRange(addressEntries)
	if err != nil {
		return nil, err
	}

	a2, err := addressGroupNameToIPRange(addressGroupEntries, a1)
	if err != nil {
		return nil, err
	}
	addressMap := lo.Assign(a1, a2)

	return formatRules(ruleEntries, addressMap, serviceMap), nil
}

type normalizer[T any] interface {
	Normalize() ([]*T, error)
}

func retriveEntriesFromXML[T any](content string, normalizer normalizer[T]) ([]*T, error) {
	if err := xml.Unmarshal(fmt.Appendf(nil, "<a>%s</a>", content), &normalizer); err != nil {
		return nil, err
	}

	entries, err := normalizer.Normalize()
	if err != nil {
		return nil, err
	}
	return entries, nil
}

func addressNameToIPRange(addresses []*address.Entry) (map[string][]model.PaloaltoIPRange, error) {
	addrMap := make(map[string][]model.PaloaltoIPRange, len(addresses))
	for _, addr := range addresses {
		if addr.IpNetmask != nil {
			start, end, err := toIPRange(*addr.IpNetmask)
			if err != nil {
				return nil, err
			}
			addrMap[addr.Name] = []model.PaloaltoIPRange{{Start: start, End: end}}
		} else if addr.IpRange != nil {
			ipRange := strings.Split(*addr.IpRange, "-")
			if len(ipRange) != 2 {
				return nil, fmt.Errorf("invalid ip range: %s", *addr.IpRange)
			}
			start, end := ipRange[0], ipRange[1]
			addrMap[addr.Name] = []model.PaloaltoIPRange{{Start: start, End: end}}
		} else {
			addrMap[addr.Name] = []model.PaloaltoIPRange{{Start: addr.Name, End: addr.Name}}
		}
	}
	return addrMap, nil
}

func addressGroupNameToIPRange(addressGroups []*address_group.Entry, addressMap map[string][]model.PaloaltoIPRange) (map[string][]model.PaloaltoIPRange, error) {
	addrGroupMap := make(map[string][]model.PaloaltoIPRange, len(addressGroups))
	for _, addrGroup := range addressGroups {
		ipRanges := make([]model.PaloaltoIPRange, 0)
		for _, addr := range addrGroup.Static {
			if ipRange, ok := addressMap[addr]; ok {
				ipRanges = append(ipRanges, ipRange...)
			} else {
				ipRanges = append(ipRanges, model.PaloaltoIPRange{Start: addr, End: addr})
			}
		}
		addrGroupMap[addrGroup.Name] = ipRanges
	}
	return addrGroupMap, nil
}

func serviceNameToPortRange(services []*service.Entry) (map[string][]model.PaloaltoPortRangeWithProtocol, error) {
	serviceMap := make(map[string][]model.PaloaltoPortRangeWithProtocol, len(services)+3)

	// predefined service
	serviceMap["any"] = []model.PaloaltoPortRangeWithProtocol{
		{PaloaltoPortRange: model.PaloaltoPortRange{Start: 0, End: 65535}, Protocol: "tcp"},
		{PaloaltoPortRange: model.PaloaltoPortRange{Start: 0, End: 65535}, Protocol: "udp"},
	}

	for _, service := range services {
		if service.Protocol == nil {
			continue
		}

		if service.Protocol.Tcp != nil {
			portRanges, err := toPortRanges(*service.Protocol.Tcp.Port)
			if err != nil {
				return nil, err
			}
			serviceMap[service.Name] = append(serviceMap[service.Name],
				lo.Map(portRanges, func(portRange model.PaloaltoPortRange, _ int) model.PaloaltoPortRangeWithProtocol {
					return model.PaloaltoPortRangeWithProtocol{
						PaloaltoPortRange: portRange,
						Protocol:          "tcp",
					}
				})...,
			)
		} else if service.Protocol.Udp != nil {
			portRanges, err := toPortRanges(*service.Protocol.Udp.Port)
			if err != nil {
				return nil, err
			}
			serviceMap[service.Name] = append(serviceMap[service.Name],
				lo.Map(portRanges, func(portRange model.PaloaltoPortRange, _ int) model.PaloaltoPortRangeWithProtocol {
					return model.PaloaltoPortRangeWithProtocol{
						PaloaltoPortRange: portRange,
						Protocol:          "udp",
					}
				})...,
			)
		}
	}

	return serviceMap, nil
}

func serviceGroupNameToPortRange(serviceGroups []*service_group.Entry, serviceMap map[string][]model.PaloaltoPortRangeWithProtocol) (map[string][]model.PaloaltoPortRangeWithProtocol, error) {
	serviceGroupMap := make(map[string][]model.PaloaltoPortRangeWithProtocol, len(serviceGroups))

	for _, serviceGroup := range serviceGroups {
		if serviceGroup.Members == nil {
			continue
		}

		serviceGroupMap[serviceGroup.Name] = lo.FlatMap(serviceGroup.Members, func(member string, _ int) []model.PaloaltoPortRangeWithProtocol {
			if portRanges, ok := serviceMap[member]; ok {
				return portRanges
			}
			return nil
		})
	}

	return serviceGroupMap, nil
}

func toIPRange(cidr string) (string, string, error) {
	if len(cidr) == 0 {
		return "", "", fmt.Errorf("invalid cidr: %s", cidr)
	} else if !strings.Contains(cidr, "/") {
		return cidr, cidr, nil
	}

	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", "", err
	}

	startIP := ipnet.IP

	mask := ipnet.Mask
	endIP := make(net.IP, len(startIP))
	for i := range startIP {
		endIP[i] = startIP[i] | ^mask[i]
	}

	return startIP.String(), endIP.String(), nil
}

func toPortRanges(portMember string) ([]model.PaloaltoPortRange, error) {
	portRanges := strings.Split(portMember, ",")

	result := make([]model.PaloaltoPortRange, 0, len(portRanges))
	for _, portRange := range portRanges {
		portRange := strings.Split(strings.TrimSpace(portRange), "-")

		switch len(portRange) {
		case 0:
			continue
		case 1:
			port, err := strconv.Atoi(portRange[0])
			if err != nil {
				return nil, err
			}
			result = append(result, model.PaloaltoPortRange{Start: port, End: port})
		case 2:
			start, err := strconv.Atoi(portRange[0])
			if err != nil {
				return nil, err
			}
			end, err := strconv.Atoi(portRange[1])
			if err != nil {
				return nil, err
			}
			result = append(result, model.PaloaltoPortRange{Start: start, End: end})
		default:
			return nil, fmt.Errorf("invalid port range: %s", portRange)
		}
	}
	return result, nil
}

func formatRules(ruleEntries []*security.Entry, addressMap map[string][]model.PaloaltoIPRange, serviceMap map[string][]model.PaloaltoPortRangeWithProtocol) []*model.PaloaltoRule {
	rules := make([]*model.PaloaltoRule, 0, len(ruleEntries))

	for _, ruleEntry := range ruleEntries {
		rule := &model.PaloaltoRule{Name: ruleEntry.Name}

		if ruleEntry.Uuid != nil {
			rule.Uuid = *ruleEntry.Uuid
		}
		if ruleEntry.RuleType != nil {
			rule.RuleType = *ruleEntry.RuleType
		} else {
			rule.RuleType = "universal"
		}

		if ruleEntry.From != nil {
			rule.SrcZones = ruleEntry.From
		}

		if ruleEntry.To != nil {
			rule.DstZones = ruleEntry.To
		}

		if ruleEntry.Source != nil {
			rule.SrcAddresses = lo.FlatMap(ruleEntry.Source, func(src string, _ int) []model.PaloaltoIPRange {
				if ipRange, ok := addressMap[src]; ok {
					return ipRange
				}

				start, end, err := toIPRange(src)
				if err != nil {
					logger.DefaultLogger().Errorf("invalid ip range: %s", src)
					return []model.PaloaltoIPRange{{Start: src, End: src}}
				}
				return []model.PaloaltoIPRange{{Start: start, End: end}}
			})
		}

		if ruleEntry.Destination != nil {
			rule.DstAddresses = lo.FlatMap(ruleEntry.Destination, func(dst string, _ int) []model.PaloaltoIPRange {
				if ipRange, ok := addressMap[dst]; ok {
					return ipRange
				}

				start, end, err := toIPRange(dst)
				if err != nil {
					logger.DefaultLogger().Errorf("invalid ip range: %s", dst)
					return nil
				}
				return []model.PaloaltoIPRange{{Start: start, End: end}}
			})
		}

		if ruleEntry.Service != nil {
			rule.DstPorts = lo.FlatMap(ruleEntry.Service, func(service string, _ int) []model.PaloaltoPortRangeWithProtocol {
				if portRanges, ok := serviceMap[service]; ok {
					return portRanges
				}

				logger.DefaultLogger().Errorf("invalid service: %s", service)
				return nil
			})
		}

		if ruleEntry.Application != nil {
			rule.Applications = ruleEntry.Application
		}

		if ruleEntry.Action != nil {
			rule.Action = strings.ToLower(*ruleEntry.Action)
		}

		if ruleEntry.GroupTag != nil {
			rule.Tag = *ruleEntry.GroupTag
		}

		rules = append(rules, rule)
	}

	return rules
}

func fetchLastRulesFromDB(controlPlane string) (*model.PaloaltoFirewall, error) {
	filter := bson.M{"uid": utils.GenerateUID(ProviderID, "paloalto-ngfw", controlPlane)}

	result := infra.Mongo.Collection("cspm_resources").FindOne(context.Background(), filter)
	if result.Err() != nil {
		return nil, result.Err()
	}

	lastFirewallConf := &model.PaloaltoFirewall{}
	if err := result.Decode(lastFirewallConf); err != nil {
		return nil, err
	}

	return lastFirewallConf, nil
}

type internetRule struct {
	DeviceName string
	VsysName   string
	*model.PaloaltoRule
}

func compareFirewallRules(lastFirewallConf *model.PaloaltoFirewall, newFirewallConf *model.PaloaltoFirewall) (bool, error) {
	if len(lastFirewallConf.Devices) != len(newFirewallConf.Devices) {
		return false, nil
	}

	lastInternetRulesJson, err := getInternetRulesJson(lastFirewallConf.Devices)
	if err != nil {
		return false, err
	}

	newInternetRulesJson, err := getInternetRulesJson(newFirewallConf.Devices)
	if err != nil {
		return false, err
	}

	return lastInternetRulesJson == newInternetRulesJson, nil
}

func getInternetRulesJson(devices []*model.PaloaltoDevice) (string, error) {
	internetRules := make([]*internetRule, 0)

	for deviceIdx := range devices {
		for vsysIdx := range devices[deviceIdx].Vsyses {
			lastVsysInternetRules := lo.FilterMap(devices[deviceIdx].Vsyses[vsysIdx].Rules, func(rule *model.PaloaltoRule, _ int) (*internetRule, bool) {
				if !strings.EqualFold(rule.Tag, "internet") {
					return nil, false
				}
				return &internetRule{
					DeviceName:   devices[deviceIdx].Name,
					VsysName:     devices[deviceIdx].Vsyses[vsysIdx].Name,
					PaloaltoRule: rule,
				}, true
			})
			internetRules = append(internetRules, lastVsysInternetRules...)
		}
	}

	slices.SortFunc(internetRules, func(a, b *internetRule) int {
		if a.DeviceName != b.DeviceName {
			return strings.Compare(a.DeviceName, b.DeviceName)
		} else if a.VsysName != b.VsysName {
			return strings.Compare(a.VsysName, b.VsysName)
		}
		return strings.Compare(a.Name, b.Name)
	})

	internetRulesJson, err := json.Marshal(internetRules)
	if err != nil {
		return "", err
	}

	return string(internetRulesJson), nil
}
