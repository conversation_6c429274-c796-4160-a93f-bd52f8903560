package lixiang

import (
	"AssetStandardizer/infra"
	"AssetStandardizer/providers/utils"
	"context"
	"sync"

	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type TrafficMirrorList struct {
	TrafficMirrorList []TrafficMirror `json:"TrafficMirrorList"`
}

type TrafficMirror struct {
	CreatedTime   string `json:"created_time"`
	Description   string `json:"description"`
	DestID        string `json:"dest_id"`
	DestType      string `json:"dest_type"`
	InsertTime    string `json:"insert_time"`
	Name          string `json:"name"`
	Region        string `json:"region"`
	RuleGroupID   string `json:"rule_group_id"`
	RuleGroupName string `json:"rule_group_name"`
	SessionID     string `json:"session_id"`
	SourceID      string `json:"source_id"`
	SourceType    string `json:"source_type"`
	Status        string `json:"status"`
	UpdatedTime   string `json:"updated_time"`
	Vni           int64  `json:"vni"`
}

func parseTrafficMirror(raw map[string]any) error {
	tmList := TrafficMirrorList{}
	utils.Decode(raw["raw_log"], &tmList)

	wg := &sync.WaitGroup{}
	wg.Add(2)

	go func() {
		defer wg.Done()
		advancedNatIDs := utils.FilterMap(tmList.TrafficMirrorList, func(tm TrafficMirror) (string, bool) {
			return tm.SourceID, tm.SourceType == "nat"
		})

		parseByNat(advancedNatIDs)
	}()

	go func() {
		defer wg.Done()
		eips := utils.FilterMap(tmList.TrafficMirrorList, func(tm TrafficMirror) (string, bool) {
			return tm.SourceID, tm.SourceType == "eip"
		})

		praseByEip(eips)
	}()

	wg.Wait()
	return nil
}

func parseByNat(natIDs []string) {
	filter := bson.M{"original_id": bson.M{"$in": natIDs}}
	update := bson.M{"$set": bson.M{"traffic_mirror_enabled": true}}
	r, err := infra.Mongo.Collection("cspm_resources").UpdateMany(context.TODO(), filter, update)
	_ = r
	if err != nil {
		logger.DefaultLogger().Error(err)
		return
	}
}

func praseByEip(eips []string) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: bson.M{"kind": "nat"}}},
		bson.D{{Key: "$lookup", Value: bson.M{"from": "cspm_resources", "localField": "eip_id_list", "foreignField": "uid", "as": "eip_list"}}},
		bson.D{{Key: "$match", Value: bson.M{"eip_list.ip": bson.M{"$in": eips}}}},
		bson.D{{Key: "$project", Value: bson.M{"uid": 1}}},
	}

	cursor, err := infra.Mongo.Collection("cspm_resources").Aggregate(context.TODO(), pipeline)
	if err != nil {
		logger.DefaultLogger().Error(err)
		return
	}

	var uidList []map[string]string
	err = cursor.All(context.TODO(), &uidList)
	if err != nil {
		logger.DefaultLogger().Error(err)
		return
	}

	uids := utils.Map(uidList, func(uid map[string]string) string {
		return uid["uid"]
	})

	filter := bson.M{"uid": bson.M{"$in": uids}}
	update := bson.M{"$set": bson.M{"traffic_mirror_enabled": true}}
	r, err := infra.Mongo.Collection("cspm_resources").UpdateMany(context.TODO(), filter, update)
	_ = r
	if err != nil {
		logger.DefaultLogger().Error(err)
		return
	}
}
