package lixiang

import (
	"AssetStandardizer/infra"
	"context"
	"errors"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func parseHidsInfo(raw map[string]any) error {
	if host, ok := raw["raw_log"].(map[string]any); ok {
		if ip, ok := host["intranet_ipv4"]; ok {
			host["sync_at"] = time.Now().UnixMilli()
			filter := bson.M{"kind": "ecs", "primary_private_ip": ip}
			if statusAny, ok := host["stream_status"]; !ok {
				status, ok := statusAny.(string)
				if !ok {
					return errors.New("invalid stream_status")
				}
				if strings.EqualFold(status, "stopped") {
					filter["hids_info"] = bson.M{"$exists": true}
				}
			}
			_, err := infra.Mongo.Collection("cspm_resources").UpdateMany(context.Background(),
				filter,
				bson.M{
					"$set": bson.M{
						"hids_info": host,
					},
				})
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return err
			}
		}
		return nil
	} else {
		return errors.New("invalid hids info event")
	}
}
