package lixiang

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"time"
)

type Waf struct {
	Domain    string `json:"domain"`
	Cname     string `json:"cname"`
	IP        string `json:"ip"`
	NodeGroup string `json:"nodegroup"`
	Internal  *bool  `json:"internal"`
}

func parseWaf(raw map[string]any, version int) model.Asset {
	original := &Waf{}
	meta := &model.ObjectMeta{}
	utils.Decode(raw["raw_log"], original)
	waf := &model.Waf{}

	meta.Provider = ProviderID
	meta.OriginalID = fmt.Sprintf("WAF%v:%v-%v-%v", version, original.Domain, utils.UnwrapOr(original.IP, original.IP != "", original.Cname), original.NodeGroup)
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "waf", meta.OriginalID)
	meta.Region = "全局"
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "waf"
	meta.Name = original.Domain

	waf.Version = version
	waf.Cname = utils.DataPointer(original.Cname)
	waf.Domain = utils.DataPointer(original.Domain)
	waf.IP = utils.DataPointer(original.IP)
	waf.NodeGroup = utils.DataPointer(original.NodeGroup)
	if original.Internal != nil {
		waf.Internal = original.Internal
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, waf, utils.WithOmitempty())
	return asset
}
