package lixiang

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
)

const ProviderID = "lixiang"

func Parse(raw map[string]any) (model.Asset, error) {
	switch utils.ReadFieldString(raw, "type") {
	case "srv-lb", "lb":
		return nil, parseLb(raw)
	case "waf_1":
		return parseWaf(raw, 1), nil
	case "waf_2":
		return parseWaf(raw, 2), nil
	case "traffic_mirror_list":
		return nil, parseTrafficMirror(raw)
	case "k8s_cluster_aggregated":
		return nil, parseK8s(raw)
	case "hids_host_info":
		return nil, parseHidsInfo(raw)
	case "apollo_appEnv_aggregated":
		return parseApolloApp(raw), nil
	case "apollo":
		return parseApolloSensitiveConf(raw), nil
	case "it_server_aggregated":
		return parseItServer(raw), nil
	case "feishu_app_ak":
		return parseFeishuAppAk(raw), nil
	case "paloalto-ngfw":
		return parsePaloalto(raw)
	default:
		return nil, fmt.Errorf("unknown type")
	}
}
