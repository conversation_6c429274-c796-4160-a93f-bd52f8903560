package lixiang

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"time"
)

type ApolloSensitiveConf struct {
	AppID          string         `json:"appId"`
	Env            string         `json:"env"`
	Cluster        string         `json:"cluster"`
	Namespace      string         `json:"namespace"`
	Configurations map[string]any `json:"configurations"`
	OwnerName      string         `json:"ownerName"`
	ExistAk        bool           `json:"exist_ak"`
}

func parseApolloSensitiveConf(raw map[string]any) model.Asset {
	original := &ApolloSensitiveConf{}
	meta := &model.ObjectMeta{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.AppID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	fakeId := fmt.Sprintf("%v:%v:%v:%v", original.AppID, original.Env, original.Cluster, original.Namespace)
	meta.UID = utils.GenerateUID(meta.Provider, "__apollo_sensitive_conf", fakeId)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "__apollo_sensitive_conf"
	meta.Name = original.AppID

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, original)
	return asset
}
