package volcengine

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"errors"
	"strconv"
	"strings"
	"time"
)

type EdgeSG struct {
	SG EdgeSGClass `json:"security_group"`
}

type EdgeSGClass struct {
	GroupIdentity    string      `json:"group_identity"`
	AccountIdentity  int64       `json:"account_identity"`
	UserIdentity     int64       `json:"user_identity"`
	Name             string      `json:"name"`
	Region           string      `json:"region"`
	City             string      `json:"city"`
	ClusterName      string      `json:"cluster_name"`
	VpcIdentity      string      `json:"vpc_identity"`
	VpcName          string      `json:"vpc_name"`
	VpcCIDR          []string    `json:"vpc_cidr"`
	GroupType        string      `json:"group_type"`
	Remark           string      `json:"remark"`
	IngressRule      EdgeSGRules `json:"ingress_rule"`
	EgressRule       EdgeSGRules `json:"egress_rule"`
	Binds            EdgeSGBinds `json:"binds"`
	ClusterNameAlias string      `json:"cluster_name_alias"`
	CreateTime       int64       `json:"create_time"`
	UpdateTime       int64       `json:"update_time"`
}

type EdgeSGBinds struct {
	Total int64        `json:"total"`
	Binds []EdgeSGBind `json:"binds"`
}

type EdgeSGBind struct {
	GroupIdentity string `json:"group_identity"`
	BindType      string `json:"bind_type"`
	BindIdentity  string `json:"bind_identity"`
	Status        int64  `json:"status"`
}

type EdgeSGRules struct {
	Total int64        `json:"total"`
	Rules []EdgeSGRule `json:"rules"`
}

type EdgeSGRule struct {
	RuleIdentity  string `json:"rule_identity"`
	Direction     string `json:"direction"`
	Rule          string `json:"rule"`
	Protocol      string `json:"protocol"`
	Ports         string `json:"ports"`
	Priority      int    `json:"priority"`
	IPType        string `json:"ip_type"`
	IPCIDR        string `json:"ip_cidr"`
	Remark        string `json:"remark"`
	GroupIdentity string `json:"group_identity"`
	Status        int64  `json:"status"`
	CreateTime    int64  `json:"create_time"`
	UpdateTime    int64  `json:"update_time"`
}

func parseEdgeSG(raw map[string]any) model.Asset {
	original := &EdgeSG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.SG.GroupIdentity
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.SG.GroupIdentity)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.SG.Name
	meta.Description = original.SG.Remark

	sg.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.SG.VpcIdentity)
	sg.Rules = utils.Flatten(utils.Map(original.SG.IngressRule.Rules, func(perm EdgeSGRule) []model.SGRule {
		portRanges := strings.Split(perm.Ports, ",")
		rules := []model.SGRule{}
		for _, portRange := range portRanges {
			portStart, portEnd, err := formatPorts(portRange)
			if err != nil {
				continue
			}
			rule := model.SGRule{
				Description: perm.Remark,
				Protocol:    perm.Protocol,
				Policy:      perm.Rule,
				Priority:    perm.Priority,
				Direction:   perm.Direction,
				PeerCIDR:    perm.IPCIDR,
				PortStart:   utils.UnwrapOr(portStart, portStart >= 0, 0),
				PortEnd:     utils.UnwrapOr(portEnd, portEnd >= 0, 65535),
			}
			rules = append(rules, rule)
		}
		return rules
	}))

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}

func formatPorts(ports string) (int, int, error) {
	ports = strings.TrimSpace(ports)
	portRanges := strings.Split(ports, "-")

	switch len(portRanges) {
	case 1:
		port, err := strconv.Atoi(portRanges[0])
		if err != nil {
			return 0, 0, err
		}
		return port, port, nil
	case 2:
		portStart, err := strconv.Atoi(portRanges[0])
		if err != nil {
			return 0, 0, err
		}
		portEnd, err := strconv.Atoi(portRanges[1])
		if err != nil {
			return 0, 0, err
		}
		return portStart, portEnd, nil
	}
	return 0, 0, errors.New("invalid port range")
}
