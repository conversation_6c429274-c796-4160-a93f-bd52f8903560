package azure

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"net/netip"
	"strconv"
	"strings"
	"time"
)

type SG struct {
	Etag       string       `json:"etag"`
	ID         string       `json:"id"`
	Location   string       `json:"location"`
	Name       string       `json:"name"`
	Properties SGProperties `json:"properties"`
	Tags       Tags         `json:"tags"`
	Type       string       `json:"type"`
}

type SGProperties struct {
	DefaultSecurityRules []SecurityRule       `json:"defaultSecurityRules"`
	NetworkInterfaces    []SGNetworkInterface `json:"networkInterfaces"`
	ProvisioningState    string               `json:"provisioningState"`
	ResourceGUID         string               `json:"resourceGuid"`
	SecurityRules        []SecurityRule       `json:"securityRules"`
	Subnets              []SGNetworkInterface `json:"subnets"`
}

type SecurityRule struct {
	Etag       string                 `json:"etag"`
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Properties SecurityRuleProperties `json:"properties"`
	Type       string                 `json:"type"`
}

type SecurityRuleProperties struct {
	Access                     string        `json:"access"`
	Description                string        `json:"description"`
	DestinationAddressPrefix   string        `json:"destinationAddressPrefix"`
	DestinationAddressPrefixes []interface{} `json:"destinationAddressPrefixes"`
	DestinationPortRange       string        `json:"destinationPortRange"`
	DestinationPortRanges      []interface{} `json:"destinationPortRanges"`
	Direction                  string        `json:"direction"`
	Priority                   int           `json:"priority"`
	Protocol                   string        `json:"protocol"`
	ProvisioningState          string        `json:"provisioningState"`
	SourceAddressPrefix        string        `json:"sourceAddressPrefix"`
	SourceAddressPrefixes      []interface{} `json:"sourceAddressPrefixes"`
	SourcePortRange            string        `json:"sourcePortRange"`
	SourcePortRanges           []interface{} `json:"sourcePortRanges"`
}

type SGNetworkInterface struct {
	ID string `json:"id"`
}

func parseSG(raw map[string]any) model.Asset {
	original := &SG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.Name

	sg.IsDefault = false
	subnetId, _ := utils.FindMap(original.Properties.Subnets, func(e SGNetworkInterface) (string, bool) {
		return e.ID, e.ID != ""
	})
	if i := strings.Index(subnetId, "/subnets/"); i > 0 {
		sg.VPCID = utils.GenerateUID(ProviderID, "vpc", subnetId[0:i])
	}

	original.Properties.SecurityRules = append(original.Properties.SecurityRules, original.Properties.DefaultSecurityRules...)
	sg.Rules = utils.Map(original.Properties.SecurityRules, func(perm SecurityRule) model.SGRule {
		rule := model.SGRule{}
		rule.Description = perm.Properties.Description
		rule.Protocol = utils.UnwrapOr("all", perm.Properties.Protocol == "*", strings.ToLower(perm.Properties.Protocol))
		rule.Policy = utils.UnwrapOr("accept", perm.Properties.Access == "Allow", "drop")
		rule.Priority = perm.Properties.Priority
		rule.Direction = utils.UnwrapOr("ingress", perm.Properties.Direction == "Inbound", "egress")

		var cidr, portRange string
		if perm.Properties.Direction == "Inbound" {
			cidr = perm.Properties.SourceAddressPrefix
			portRange = perm.Properties.SourcePortRange
		} else {
			cidr = perm.Properties.DestinationAddressPrefix
			portRange = perm.Properties.DestinationPortRange
		}

		if _, err := netip.ParseAddr(cidr); err == nil && !strings.Contains(cidr, "/") {
			rule.PeerCIDR = cidr + "/32"
		} else if cidr == "*" {
			rule.PeerCIDR = "0.0.0.0/0"
		} else {
			rule.PeerCIDR = cidr
		}

		if portRange == "*" {
			rule.PortStart = 0
			rule.PortEnd = 65535
		} else {
			ports := strings.Split(portRange, "-")
			if len(ports) == 2 {
				rule.PortStart, _ = strconv.Atoi(ports[0])
				if rule.PortStart < 0 {
					rule.PortStart = 0
				}
				rule.PortEnd, _ = strconv.Atoi(ports[1])
				if rule.PortEnd < 0 {
					rule.PortEnd = 65535
				}
			}
		}
		return rule
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}
