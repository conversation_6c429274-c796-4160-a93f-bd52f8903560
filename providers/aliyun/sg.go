package aliyun

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"
)

type SG struct {
	Permissions   []Permission  `json:"permissions"`
	SecurityGroup SecurityGroup `json:"security_group"`
}

type Permission struct {
	CreateTime              string `json:"CreateTime"`
	Description             string `json:"Description"`
	DestCIDRIP              string `json:"DestCidrIp"`
	DestGroupID             string `json:"DestGroupId"`
	DestGroupName           string `json:"DestGroupName"`
	DestGroupOwnerAccount   string `json:"DestGroupOwnerAccount"`
	DestPrefixListID        string `json:"DestPrefixListId"`
	DestPrefixListName      string `json:"DestPrefixListName"`
	Direction               string `json:"Direction"`
	IPProtocol              string `json:"IpProtocol"`
	Ipv6DestCIDRIP          string `json:"Ipv6DestCidrIp"`
	Ipv6SourceCIDRIP        string `json:"Ipv6SourceCidrIp"`
	NICType                 string `json:"NicType"`
	Policy                  string `json:"Policy"`
	PortRange               string `json:"PortRange"`
	Priority                string `json:"Priority"`
	SecurityGroupRuleID     string `json:"SecurityGroupRuleId"`
	SourceCIDRIP            string `json:"SourceCidrIp"`
	SourceGroupID           string `json:"SourceGroupId"`
	SourceGroupName         string `json:"SourceGroupName"`
	SourceGroupOwnerAccount string `json:"SourceGroupOwnerAccount"`
	SourcePortRange         string `json:"SourcePortRange"`
	SourcePrefixListID      string `json:"SourcePrefixListId"`
	SourcePrefixListName    string `json:"SourcePrefixListName"`
}

type SecurityGroup struct {
	CreationTime      string `json:"CreationTime"`
	Description       string `json:"Description"`
	ResourceGroupID   string `json:"ResourceGroupId"`
	RuleCount         int64  `json:"RuleCount"`
	SecurityGroupID   string `json:"SecurityGroupId"`
	SecurityGroupName string `json:"SecurityGroupName"`
	SecurityGroupType string `json:"SecurityGroupType"`
	ServiceID         int64  `json:"ServiceID"`
	ServiceManaged    bool   `json:"ServiceManaged"`
	Tags              Tags   `json:"Tags"`
	VpcID             string `json:"VpcId"`
}

func parseSG(raw map[string]any) model.Asset {
	original := &SG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.SecurityGroup.SecurityGroupID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.SecurityGroup.Tags.Tag, func(tag Tag) model.KV {
		return model.KV{
			Key:   tag.TagKey,
			Value: tag.TagValue,
		}
	})
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.SecurityGroup.SecurityGroupID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.SecurityGroup.SecurityGroupName
	meta.Description = original.SecurityGroup.Description

	sg.IsDefault = strings.EqualFold(original.SecurityGroup.SecurityGroupName, "default")
	sg.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.SecurityGroup.VpcID)
	sg.Rules = utils.Map(original.Permissions, func(perm Permission) model.SGRule {
		rule := model.SGRule{}
		rule.Description = perm.Description
		rule.Protocol = strings.ToLower(perm.IPProtocol)
		rule.Policy = strings.ToLower(perm.Policy)
		rule.Priority, _ = strconv.Atoi(perm.Priority)
		rule.Direction = perm.Direction

		var cidr, sgid, portRange string
		if perm.Direction == "ingress" {
			cidr = perm.SourceCIDRIP
			sgid = perm.SourceGroupID
			portRange = perm.PortRange
		} else {
			cidr = perm.DestCIDRIP
			sgid = perm.DestGroupID
			portRange = perm.PortRange
		}

		if cidr != "" && !strings.Contains(cidr, "/") {
			rule.PeerCIDR = cidr + "/32"
		} else {
			rule.PeerCIDR = cidr
		}

		rule.PeerSGID = utils.UnwrapOr(sgid, sgid == "", utils.GenerateUID(meta.Provider, "security-group", sgid))

		ports := strings.Split(portRange, "/")
		if len(ports) == 2 {
			rule.PortStart, _ = strconv.Atoi(ports[0])
			if rule.PortStart < 0 {
				rule.PortStart = 0
			}
			rule.PortEnd, _ = strconv.Atoi(ports[1])
			if rule.PortEnd < 0 {
				rule.PortEnd = 65535
			}
		}
		return rule
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}
