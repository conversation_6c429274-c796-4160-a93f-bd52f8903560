package aliyun

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.chehejia.com/go-commons/op-golang-common/pkg/logger"
)

type Slb struct {
	LB        SlbClass      `json:"lb"`
	MSServers []interface{} `json:"msServers"`
	Servers   []SlbServer   `json:"servers"`
	VServers  []SlbVServer  `json:"vServers"`
}

type SlbClass struct {
	Address                      string                   `json:"address"`
	AddressIPVersion             string                   `json:"addressIPVersion"`
	AddressType                  string                   `json:"addressType"`
	AutoReleaseTime              any                      `json:"autoReleaseTime"`
	BackendServers               BackendServers           `json:"backendServers"`
	Bandwidth                    int                      `json:"bandwidth"`
	CreateTime                   time.Time                `json:"createTime"`
	CreateTimeStamp              int64                    `json:"createTimeStamp"`
	DeleteProtection             string                   `json:"deleteProtection"`
	EndTime                      time.Time                `json:"endTime"`
	EndTimeStamp                 int64                    `json:"endTimeStamp"`
	InstanceChargeType           string                   `json:"instanceChargeType"`
	InternetChargeType           string                   `json:"internetChargeType"`
	ListenerPorts                ListenerPorts            `json:"listenerPorts"`
	ListenerPortsAndProtocal     ListenerPortsAndProtocal `json:"listenerPortsAndProtocal"`
	ListenerPortsAndProtocol     ListenerPortsAndProtocol `json:"listenerPortsAndProtocol"`
	LoadBalancerID               string                   `json:"loadBalancerId"`
	LoadBalancerName             string                   `json:"loadBalancerName"`
	LoadBalancerSpec             string                   `json:"loadBalancerSpec"`
	LoadBalancerStatus           string                   `json:"loadBalancerStatus"`
	MasterZoneID                 string                   `json:"masterZoneId"`
	ModificationProtectionReason string                   `json:"modificationProtectionReason"`
	ModificationProtectionStatus string                   `json:"modificationProtectionStatus"`
	NetworkType                  string                   `json:"networkType"`
	PayType                      string                   `json:"payType"`
	RegionID                     string                   `json:"regionId"`
	RegionIDAlias                string                   `json:"regionIdAlias"`
	RenewalCycUnit               any                      `json:"renewalCycUnit"`
	RenewalDuration              any                      `json:"renewalDuration"`
	RenewalStatus                any                      `json:"renewalStatus"`
	RequestID                    string                   `json:"requestId"`
	ResourceGroupID              string                   `json:"resourceGroupId"`
	SlaveZoneID                  string                   `json:"slaveZoneId"`
	Tags                         Tags                     `json:"tags"`
	VSwitchID                    string                   `json:"vSwitchId"`
	VpcID                        string                   `json:"vpcId"`
	VswitchID                    string                   `json:"vswitchId"`
}
type BackendServers struct {
	BackendServer []any `json:"backendServer"`
}
type ListenerPorts struct {
	ListenerPort []int `json:"listenerPort"`
}
type ListenerPortAndProtocal struct {
	ListenerPort     int    `json:"listenerPort"`
	ListenerProtocal string `json:"listenerProtocal"`
}
type ListenerPortsAndProtocal struct {
	ListenerPortAndProtocal []ListenerPortAndProtocal `json:"listenerPortAndProtocal"`
}
type ListenerPortAndProtocol struct {
	Description      string `json:"description"`
	ForwardPort      any    `json:"forwardPort"`
	ListenerForward  any    `json:"listenerForward"`
	ListenerPort     int    `json:"listenerPort"`
	ListenerProtocol string `json:"listenerProtocol"`
}
type ListenerPortsAndProtocol struct {
	ListenerPortAndProtocol []ListenerPortAndProtocol `json:"listenerPortAndProtocol"`
}
type SlbServer struct {
	ListenerPort       int    `json:"ListenerPort"`
	Port               int    `json:"Port"`
	Protocol           string `json:"Protocol"`
	ServerHealthStatus string `json:"ServerHealthStatus"`
	ServerID           string `json:"ServerId"`
	ServerIP           string `json:"ServerIp"`
}

type SlbVServer struct {
	Port     int    `json:"Port"`
	ServerID string `json:"ServerId"`
	ServerIP string `json:"ServerIp"`
	Type     string `json:"Type"`
	Weight   int    `json:"Weight"`
}

func parseAlb(raw map[string]any) model.Asset {
	meta := &model.ObjectMeta{}
	lb := &model.LB{}

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.ReadTags(ProviderID, raw, "raw_log", "tags", "tag")
	meta.OriginalID = utils.ReadFieldString(raw, "raw_log", "LoadBalancerId")
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = uuid.NewSHA1(uuid.Nil, []byte(fmt.Sprintf("%s+%s", meta.Provider, meta.OriginalID))).String()
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Description = utils.ReadFieldString(raw, "raw_log", "description")
	meta.Kind = "lb"
	meta.Name = utils.ReadFieldString(raw, "raw_log", "LoadBalancerName")

	lb.Class = "application"
	lb.Status = utils.GetMappedValue(raw, []string{"raw_log", "status"}, valueMap, []string{"lb", "status"})
	lb.VPCID = utils.ReadFieldString(raw, "raw_log", "vpcId")
	ipType := utils.ReadFieldString(raw, "raw_log", "addressType")
	ipv6Type := utils.ReadFieldString(raw, "ipv6AddressType")
	zones, _ := utils.ReadFieldSlice(raw, "raw_log", "zoneMappings")
	for i := range zones {
		zone, ok := zones[i].(map[string]any)
		if !ok {
			logger.DefaultLogger().Error("failed to parse zoneMappings")
			continue
		}
		addresses, _ := utils.ReadFieldSlice(zone, "loadBalancerAddresses")
		for i := range addresses {
			address, ok := addresses[i].(map[string]any)
			if !ok {
				logger.DefaultLogger().Error("failed to parse loadBalancerAddresses")
				continue
			}
			if ip := utils.ReadFieldString(address, "address"); len(ip) > 0 {
				if ipType == "intranet" {
					lb.PrivateIPList = append(lb.PrivateIPList, ip)
				} else {
					lb.PublicIPList = append(lb.PublicIPList, ip)
				}
			}
			if ip := utils.ReadFieldString(address, "ipv6Address"); len(ip) > 0 {
				if ipv6Type == "intranet" {
					lb.PrivateIPList = append(lb.PrivateIPList, ip)
				} else {
					lb.PublicIPList = append(lb.PublicIPList, ip)
				}
			}
		}
	}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}

type Nlb struct {
	LB        NlbClass      `json:"lb"`
	Listeners []NlbListener `json:"listeners"`
	Servers   []NlbServer   `json:"servers"`
}

type NlbClass struct {
	AddressIPVersion             string                       `json:"AddressIpVersion"`
	AddressType                  string                       `json:"AddressType"`
	CPS                          int64                        `json:"Cps"`
	CreateTime                   string                       `json:"CreateTime"`
	CrossZoneEnabled             bool                         `json:"CrossZoneEnabled"`
	DNSName                      string                       `json:"DNSName"`
	DeletionProtectionConfig     DeletionProtectionConfig     `json:"DeletionProtectionConfig"`
	Ipv6AddressType              string                       `json:"Ipv6AddressType"`
	LoadBalancerBillingConfig    LoadBalancerBillingConfig    `json:"LoadBalancerBillingConfig"`
	LoadBalancerBusinessStatus   string                       `json:"LoadBalancerBusinessStatus"`
	LoadBalancerID               string                       `json:"LoadBalancerId"`
	LoadBalancerName             string                       `json:"LoadBalancerName"`
	LoadBalancerStatus           string                       `json:"LoadBalancerStatus"`
	LoadBalancerType             string                       `json:"LoadBalancerType"`
	ModificationProtectionConfig ModificationProtectionConfig `json:"ModificationProtectionConfig"`
	RegionID                     string                       `json:"RegionId"`
	RequestID                    string                       `json:"RequestId"`
	ResourceGroupID              string                       `json:"ResourceGroupId"`
	Tags                         []Tag                        `json:"Tags"`
	VpcID                        string                       `json:"VpcId"`
	ZoneMappings                 []ZoneMapping                `json:"ZoneMappings"`
	SecurityGroupIDS             []string                     `json:"SecurityGroupIds"`
}

type DeletionProtectionConfig struct {
	Enabled bool `json:"Enabled"`
}

type LoadBalancerBillingConfig struct {
	PayType string `json:"PayType"`
}

type ModificationProtectionConfig struct {
	Status string `json:"Status"`
}

type ZoneMapping struct {
	LoadBalancerAddresses []LoadBalancerAddress `json:"LoadBalancerAddresses"`
	Status                string                `json:"Status"`
	VSwitchID             string                `json:"VSwitchId"`
	ZoneID                string                `json:"ZoneId"`
}

type LoadBalancerAddress struct {
	EniID               string   `json:"EniId"`
	Ipv4LocalAddresses  []string `json:"Ipv4LocalAddresses"`
	PrivateIPv4Address  string   `json:"PrivateIPv4Address"`
	PrivateIPv4HcStatus string   `json:"PrivateIPv4HcStatus"`
	AllocationID        string   `json:"AllocationId"`
	PublicIPv4Address   string   `json:"PublicIPv4Address"`
	Ipv6Address         string   `json:"Ipv6Address"`
	PrivateIPv6HcStatus string   `json:"PrivateIPv6HcStatus,"`
	Ipv6LocalAddresses  []string `json:"Ipv6LocalAddresses"`
}

type NlbListener struct {
	AlpnEnabled           bool     `json:"AlpnEnabled"`
	CAEnabled             bool     `json:"CaEnabled"`
	CPS                   int64    `json:"Cps"`
	IdleTimeout           int64    `json:"IdleTimeout"`
	ListenerID            string   `json:"ListenerId"`
	ListenerPort          int      `json:"ListenerPort"`
	ListenerProtocol      string   `json:"ListenerProtocol"`
	ListenerStatus        string   `json:"ListenerStatus"`
	LoadBalancerID        string   `json:"LoadBalancerId"`
	Mss                   int64    `json:"Mss"`
	ProxyProtocolEnabled  bool     `json:"ProxyProtocolEnabled"`
	ProxyProtocolV2Config any      `json:"ProxyProtocolV2Config"`
	RegionID              string   `json:"RegionId"`
	SECSensorEnabled      bool     `json:"SecSensorEnabled"`
	ServerGroupID         string   `json:"ServerGroupId"`
	Tags                  []Tag    `json:"Tags"`
	StartPort             string   `json:"StartPort"`
	EndPort               string   `json:"EndPort"`
	ListenerDescription   string   `json:"ListenerDescription"`
	SecurityPolicyID      string   `json:"SecurityPolicyId"`
	CertificateIDS        []string `json:"CertificateIds"`
	CACertificateIDS      []string `json:"CaCertificateIds"`
	AlpnPolicy            string   `json:"AlpnPolicy"`
}

type NlbServer struct {
	Port          int64  `json:"Port"`
	ServerGroupID string `json:"ServerGroupId"`
	ServerID      string `json:"ServerId"`
	ServerIP      string `json:"ServerIp"`
	ServerType    string `json:"ServerType"`
	Status        string `json:"Status"`
	Weight        int64  `json:"Weight"`
	ZoneID        string `json:"ZoneId"`
}

func parseNlb(raw map[string]any) model.Asset {
	original := &Nlb{}
	meta := &model.ObjectMeta{}
	lb := &model.LB{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.LB.Tags, func(e Tag) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.OriginalID = original.LB.LoadBalancerID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "lb", meta.OriginalID)
	meta.Region = original.LB.RegionID
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "lb"
	meta.Name = original.LB.LoadBalancerName

	lb.Class = "network"
	lb.DeleteProtection = lo.ToPtr(original.LB.DeletionProtectionConfig.Enabled)
	lb.Status = lo.Ternary(strings.EqualFold(original.LB.LoadBalancerStatus, "active"), "active", "inactive")
	lb.VPCID = utils.GenerateUID(ProviderID, "vpc", original.LB.VpcID)

	ipv6Type := original.LB.Ipv6AddressType
	for _, zoneMapping := range original.LB.ZoneMappings {
		for _, address := range zoneMapping.LoadBalancerAddresses {
			if len(address.PublicIPv4Address) > 0 {
				lb.PublicIPList = append(lb.PublicIPList, address.PublicIPv4Address)
			}
			if len(address.PrivateIPv4Address) > 0 {
				lb.PrivateIPList = append(lb.PrivateIPList, address.PrivateIPv4Address)
			}
			if len(address.Ipv6Address) > 0 {
				if ipv6Type == "intranet" {
					lb.PrivateIPList = append(lb.PrivateIPList, address.Ipv6Address)
				} else {
					lb.PublicIPList = append(lb.PublicIPList, address.Ipv6Address)
				}
			}
		}
	}

	lb.SGIDList = utils.Map(original.LB.SecurityGroupIDS, func(e string) string { return utils.GenerateUID(ProviderID, "security-group", e) })
	lb.ListenerIDList = utils.Map(original.Listeners, func(e NlbListener) string {
		return utils.GenerateUID(ProviderID, "lb-listener", e.ListenerID)
	})
	lb.Servers = utils.Map(original.Servers, func(e NlbServer) model.LbServer {
		var class = strings.ToLower(e.ServerType)

		return model.LbServer{
			OriginalID: e.ServerID,
			Uid:        lo.Ternary(class != "ip", utils.GenerateUID(ProviderID, class, e.ServerID), ""),
			Class:      class,
			IP:         e.ServerIP,
			Port:       int(e.Port),
			Weight:     int(e.Weight),
		}
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}

func parseSlb(raw map[string]any) model.Asset {
	original := &Slb{}
	meta := &model.ObjectMeta{}
	lb := &model.LB{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalLabels = utils.Map(original.LB.Tags.Tag, func(e Tag) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.OriginalID = original.LB.LoadBalancerID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(ProviderID, "lb", meta.OriginalID)
	meta.Region = original.LB.RegionID
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "lb"
	meta.Name = original.LB.LoadBalancerName

	lb.Class = "mixed"
	lb.DeleteProtection = lo.ToPtr(strings.EqualFold(original.LB.DeleteProtection, "on"))
	if original.LB.AddressType == "intranet" {
		lb.PrivateIPList = []string{original.LB.Address}
	} else {
		lb.PublicIPList = []string{original.LB.Address}
	}
	lb.Status = utils.UnwrapOr("active", original.LB.LoadBalancerStatus == "active", "inactive")
	lb.VPCID = utils.GenerateUID(ProviderID, "vpc", original.LB.VpcID)

	for _, listener := range original.LB.ListenerPortsAndProtocol.ListenerPortAndProtocol {
		port := listener.ListenerPort
		protocol := strings.ToLower(listener.ListenerProtocol)
		id := fmt.Sprintf("%s+%d+%s", meta.UID, port, protocol)
		listenerID := utils.GenerateUID(ProviderID, "lb-listener", id)
		lb.ListenerIDList = append(lb.ListenerIDList, listenerID)
	}
	lb.Servers = append(lb.Servers, utils.Map(original.Servers, func(e SlbServer) model.LbServer {
		var class string
		if strings.HasPrefix(e.ServerID, "i-") {
			class = "ecs"
		} else if strings.HasPrefix(e.ServerID, "eni-") {
			class = "eni"
		}
		return model.LbServer{
			OriginalID: e.ServerID,
			Uid:        lo.Ternary(class != "", utils.GenerateUID(ProviderID, class, e.ServerID), ""),
			Class:      class,
			IP:         e.ServerIP,
			Port:       e.Port,
		}
	})...)
	lb.Servers = append(lb.Servers, utils.Map(original.VServers, func(e SlbVServer) model.LbServer {
		class := strings.ToLower(e.Type)
		return model.LbServer{
			OriginalID: e.ServerID,
			Uid:        utils.GenerateUID(ProviderID, class, e.ServerID),
			Class:      class,
			IP:         e.ServerIP,
			Port:       e.Port,
			Weight:     e.Weight,
		}
	})...)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, lb)
	return asset
}
