package aliyun

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"
)

type Subnet struct {
	NetworkACL SubnetACL   `json:"nacl"`
	Subnet     SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	Status            string            `json:"Status"`
	VpcID             string            `json:"VpcId"`
	CreationTime      string            `json:"CreationTime"`
	Description       string            `json:"Description"`
	NetworkACLName    string            `json:"NetworkAclName"`
	NetworkACLID      string            `json:"NetworkAclId"`
	OwnerID           float64           `json:"OwnerId"`
	RegionID          string            `json:"RegionId"`
	IngressACLEntries IngressACLEntries `json:"IngressAclEntries"`
	EgressACLEntries  EgressACLEntries  `json:"EgressAclEntries"`
	Resources         Resources         `json:"Resources"`
	Tags              Tags              `json:"Tags"`
}

type EgressACLEntries struct {
	EgressACLEntry []EgressACLEntry `json:"EgressAclEntry"`
}

type EgressACLEntry struct {
	NetworkACLEntryID   string `json:"NetworkAclEntryId"`
	EntryType           string `json:"EntryType"`
	NetworkACLEntryName string `json:"NetworkAclEntryName"`
	Policy              string `json:"Policy"`
	Description         string `json:"Description"`
	Protocol            string `json:"Protocol"`
	DestinationCIDRIP   string `json:"DestinationCidrIp"`
	IPVersion           string `json:"IpVersion"`
	Port                string `json:"Port"`
}

type IngressACLEntries struct {
	IngressACLEntry []IngressACLEntry `json:"IngressAclEntry"`
}

type IngressACLEntry struct {
	NetworkACLEntryID   string `json:"NetworkAclEntryId"`
	EntryType           string `json:"EntryType"`
	NetworkACLEntryName string `json:"NetworkAclEntryName"`
	Policy              string `json:"Policy"`
	Description         string `json:"Description"`
	SourceCIDRIP        string `json:"SourceCidrIp"`
	IPVersion           string `json:"IpVersion"`
	Protocol            string `json:"Protocol"`
	Port                string `json:"Port"`
}

type Resources struct {
	Resource []Resource `json:"Resource"`
}

type Resource struct {
	Status       string `json:"Status"`
	ResourceType string `json:"ResourceType"`
	ResourceID   string `json:"ResourceId"`
}

type SubnetClass struct {
	AvailableIPAddressCount int        `json:"AvailableIpAddressCount"`
	CIDRBlock               string     `json:"CidrBlock"`
	CreationTime            string     `json:"CreationTime"`
	Description             string     `json:"Description"`
	Ipv6CIDRBlock           string     `json:"Ipv6CidrBlock"`
	IsDefault               bool       `json:"IsDefault"`
	NetworkACLID            string     `json:"NetworkAclId"`
	OwnerID                 int64      `json:"OwnerId"`
	ResourceGroupID         string     `json:"ResourceGroupId"`
	RouteTable              RouteTable `json:"RouteTable"`
	ShareType               string     `json:"ShareType"`
	Status                  string     `json:"Status"`
	VSwitchID               string     `json:"VSwitchId"`
	VSwitchName             string     `json:"VSwitchName"`
	VpcID                   string     `json:"VpcId"`
	ZoneID                  string     `json:"ZoneId"`
}

type RouteTable struct {
	RouteTableID   string `json:"RouteTableId"`
	RouteTableType string `json:"RouteTableType"`
}

func parseSubnet(raw map[string]any) model.Asset {
	original := &Subnet{}
	meta := &model.ObjectMeta{}
	subnet := &model.Subnet{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Subnet.VSwitchID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "subnet", original.Subnet.VSwitchID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "subnet"
	meta.Name = original.Subnet.VSwitchName
	meta.Description = original.Subnet.Description

	subnet.IsDefault = original.Subnet.IsDefault
	subnet.CIDR = original.Subnet.CIDRBlock
	subnet.CIDRv6 = original.Subnet.Ipv6CIDRBlock
	subnet.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Subnet.VpcID)
	subnet.ACL = parseACL([]SubnetACL{original.NetworkACL})
	subnet.AvailableIPCount = original.Subnet.AvailableIPAddressCount

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, subnet)
	return asset
}

func parseACL(acls []SubnetACL) []model.ACLRule {
	results := []model.ACLRule{}

	for _, acl := range acls {
		for _, entry := range acl.EgressACLEntries.EgressACLEntry {
			rule := model.ACLRule{}
			rule.RuleID = entry.NetworkACLEntryID
			rule.Name = entry.NetworkACLEntryName
			rule.Description = entry.Description
			rule.Protocol = entry.Protocol
			rule.IPVersion = entry.IPVersion
			rule.Policy = entry.Policy
			rule.Direction = "egress"
			rule.PeerCIDR = strings.TrimSpace(entry.DestinationCIDRIP)

			if entry.Port == "" {
				rule.PortStart, rule.PortEnd = 1, 65535
			} else {
				ports := strings.Split(entry.Port, "/")
				switch len(ports) {
				case 1:
					port, _ := strconv.Atoi(ports[0])
					rule.PortStart, rule.PortEnd = port, port
				case 2:
					portStart, _ := strconv.Atoi(ports[0])
					portEnd, _ := strconv.Atoi(ports[1])
					rule.PortStart = utils.UnwrapOr(portStart, portStart > 0, 1)
					rule.PortEnd = utils.UnwrapOr(portEnd, portEnd > 0, 65535)
				}
			}

			results = append(results, rule)
		}
		for _, entry := range acl.IngressACLEntries.IngressACLEntry {
			rule := model.ACLRule{}
			rule.RuleID = entry.NetworkACLEntryID
			rule.Name = entry.NetworkACLEntryName
			rule.Description = entry.Description
			rule.Protocol = entry.Protocol
			rule.IPVersion = entry.IPVersion
			rule.Policy = entry.Policy
			rule.Direction = "egress"
			rule.PeerCIDR = strings.TrimSpace(entry.SourceCIDRIP)

			if entry.Port == "" {
				rule.PortStart, rule.PortEnd = 1, 65535
			} else {
				ports := strings.Split(entry.Port, "/")
				switch len(ports) {
				case 1:
					port, _ := strconv.Atoi(ports[0])
					rule.PortStart, rule.PortEnd = port, port
				case 2:
					portStart, _ := strconv.Atoi(ports[0])
					portEnd, _ := strconv.Atoi(ports[1])
					rule.PortStart = utils.UnwrapOr(portStart, portStart > 0, 1)
					rule.PortEnd = utils.UnwrapOr(portEnd, portEnd > 0, 65535)
				}
			}

			results = append(results, rule)
		}
	}
	return results
}
