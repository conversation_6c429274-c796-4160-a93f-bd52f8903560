package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"strconv"
	"strings"
	"time"
)

type Subnet struct {
	ACL    SubnetACL   `json:"acl"`
	Subnet SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	SubnetID   string    `json:"subnetId"`
	SubnetName string    `json:"subnetName"`
	SubnetCIDR string    `json:"subnetCidr"`
	ACLRules   []ACLRule `json:"aclRules"`
}

type ACLRule struct {
	ID                   string `json:"id"`
	SubnetID             string `json:"subnetId"`
	Description          string `json:"description"`
	Protocol             string `json:"protocol"`
	SourceIPAddress      string `json:"sourceIpAddress"`
	DestinationIPAddress string `json:"destinationIpAddress"`
	SourcePort           string `json:"sourcePort"`
	DestinationPort      string `json:"destinationPort"`
	Position             int    `json:"position"`
	Direction            string `json:"direction"`
	Action               string `json:"action"`
}

type SubnetClass struct {
	SubnetID    string `json:"subnetId"`
	Name        string `json:"name"`
	ZoneName    string `json:"zoneName"`
	CIDR        string `json:"cidr"`
	Ipv6CIDR    string `json:"ipv6Cidr"`
	VpcID       string `json:"vpcId"`
	SubnetType  string `json:"subnetType"`
	Description string `json:"description"`
	CreatedTime string `json:"createdTime"`
	AvailableIP int    `json:"availableIp"`
	Tags        []Tags `json:"tags"`
}

func parseSubnet(raw map[string]any) model.Asset {
	original := &Subnet{}
	meta := &model.ObjectMeta{}
	subnet := &model.Subnet{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Subnet.SubnetID
	meta.OriginalLabels = utils.Map(original.Subnet.Tags, func(e Tags) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "subnet", original.Subnet.SubnetID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "subnet"
	meta.Name = original.Subnet.Name
	meta.Description = original.Subnet.Description

	subnet.IsDefault, _ = utils.FindMap(original.Subnet.Tags, func(e Tags) (bool, bool) { return e.TagKey == "默认项目", e.TagKey == "默认项目" })
	subnet.CIDR = original.Subnet.CIDR
	subnet.CIDRv6 = original.Subnet.Ipv6CIDR
	subnet.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Subnet.VpcID)
	subnet.AvailableIPCount = original.Subnet.AvailableIP
	subnet.ACL = utils.Map(original.ACL.ACLRules, func(e ACLRule) model.ACLRule { return parseACL(e) })

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, subnet)
	return asset
}

func parseACL(rule ACLRule) model.ACLRule {
	result := model.ACLRule{
		RuleID:      rule.ID,
		Name:        rule.SubnetID,
		Description: rule.Description,
		Protocol:    rule.Protocol,
		IPVersion:   "IPv4",
		Policy:      utils.UnwrapOr("accept", rule.Action == "allow", "drop"),
		Priority:    rule.Position,
		Direction:   rule.Direction,
	}
	if rule.Direction == "ingress" {
		result.PeerCIDR = parseCIDR(rule.SourceIPAddress)
		result.PortStart, result.PortEnd = parsePort(rule.SourcePort)
	} else {
		result.PeerCIDR = parseCIDR(rule.DestinationIPAddress)
		result.PortStart, result.PortEnd = parsePort(rule.DestinationPort)
	}
	return result
}

func parseCIDR(cidr string) string {
	if cidr == "all" {
		return "0.0.0.0/0"
	}
	if !strings.Contains(cidr, "/") {
		return fmt.Sprintf("%s/32", cidr)
	}
	return cidr
}

func parsePort(port string) (int, int) {
	var portStart, portEnd int
	if port == "all" || port == "null" {
		portStart, portEnd = 1, 65535
	} else {
		ports := strings.Split(port, "-")
		switch len(ports) {
		case 1:
			port, _ := strconv.Atoi(ports[0])
			portStart, portEnd = port, port
		case 2:
			portStart, _ = strconv.Atoi(ports[0])
			portEnd, _ = strconv.Atoi(ports[1])
		}
	}
	return portStart, portEnd
}
