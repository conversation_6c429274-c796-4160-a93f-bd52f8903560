package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"github.com/samber/lo"
)

type Kafka struct {
	ClusterID   string      `json:"clusterId"`
	ClusterSid  string      `json:"clusterSid"`
	Name        string      `json:"name"`
	Region      string      `json:"region"`
	Type        string      `json:"type"`
	Mode        string      `json:"mode"`
	State       string      `json:"state"`
	Provisioned Provisioned `json:"provisioned"`
	Tags        []Tag       `json:"tags"`
	CreateTime  string      `json:"createTime"`
}

type Provisioned struct {
	KafkaVersion               string               `json:"kafkaVersion"`
	Billing                    KafkaBilling         `json:"billing"`
	Vpc                        KafkaVpc             `json:"vpc"`
	Subnets                    []KafkaSubnet        `json:"subnets"`
	LogicalZones               []string             `json:"logicalZones"`
	SecurityGroup              []KafkaSecurityGroup `json:"securityGroup"`
	SecurityGroups             []KafkaSecurityGroup `json:"securityGroups"`
	VpcID                      interface{}          `json:"vpcId"`
	SubnetIDS                  interface{}          `json:"subnetIds"`
	SecurityGroupIDS           interface{}          `json:"securityGroupIds"`
	PublicIPEnabled            bool                 `json:"publicIpEnabled"`
	PublicIPBandwidth          int64                `json:"publicIpBandwidth"`
	PublicIPMode               interface{}          `json:"publicIpMode"`
	Eips                       interface{}          `json:"eips"`
	IntranetIPEnabled          bool                 `json:"intranetIpEnabled"`
	Authentications            []Authentication     `json:"authentications"`
	ACLEnabled                 bool                 `json:"aclEnabled"`
	NumberOfBrokerNodes        int64                `json:"numberOfBrokerNodes"`
	NumberOfBrokerNodesPerZone int64                `json:"numberOfBrokerNodesPerZone"`
	NodeType                   string               `json:"nodeType"`
	StorageMeta                StorageMeta          `json:"storageMeta"`
	StoragePolicyEnabled       bool                 `json:"storagePolicyEnabled"`
	StoragePolicy              StoragePolicy        `json:"storagePolicy"`
	RemoteStorageEnabled       bool                 `json:"remoteStorageEnabled"`
	ConfigMeta                 ConfigMeta           `json:"configMeta"`
	DeploySetEnabled           bool                 `json:"deploySetEnabled"`
}

type Authentication struct {
	Mode    string      `json:"mode"`
	Context interface{} `json:"context"`
}

type KafkaBilling struct {
	Payment             string      `json:"payment"`
	TimeLength          int64       `json:"timeLength"`
	TimeUnit            string      `json:"timeUnit"`
	ExpireTime          string      `json:"expireTime"`
	AutoRenewEnabled    bool        `json:"autoRenewEnabled"`
	AutoRenewTimeLength int64       `json:"autoRenewTimeLength"`
	AutoRenewTimeUnit   string      `json:"autoRenewTimeUnit"`
	CouponIDS           interface{} `json:"couponIds"`
	IsAutoPay           interface{} `json:"isAutoPay"`
}

type ConfigMeta struct {
	ConfigID   string  `json:"configId"`
	RevisionID int64   `json:"revisionId"`
	Context    Context `json:"context"`
}

type Context struct {
	NumNetworkThreads  string `json:"num.network.threads"`
	JVMHeapSize        string `json:"jvm.heap.size"`
	NumIoThreads       string `json:"num.io.threads"`
	NumReplicaFetchers string `json:"num.replica.fetchers"`
}

type KafkaSecurityGroup struct {
	SecurityGroupID   string `json:"securityGroupId"`
	SecurityGroupUUID string `json:"securityGroupUuid"`
	Name              string `json:"name"`
	VpcID             string `json:"vpcId"`
}

type StorageMeta struct {
	StorageType  string `json:"storageType"`
	StorageSize  int64  `json:"storageSize"`
	NumberOfDisk int64  `json:"numberOfDisk"`
}

type StoragePolicy struct {
	Type             string      `json:"type"`
	AutoDelete       interface{} `json:"autoDelete"`
	AutoExpand       AutoExpand  `json:"autoExpand"`
	DynamicRetention interface{} `json:"dynamicRetention"`
}

type AutoExpand struct {
	DiskUsedThresholdPercent int64 `json:"diskUsedThresholdPercent"`
	StepForwardPercent       int64 `json:"stepForwardPercent"`
	StepForwardSize          int64 `json:"stepForwardSize"`
	MaxStorageSize           int64 `json:"maxStorageSize"`
}

type KafkaSubnet struct {
	SubnetID   string `json:"subnetId"`
	Name       string `json:"name"`
	SubnetType string `json:"subnetType"`
	Zone       string `json:"zone"`
	VpcID      string `json:"vpcId"`
	CIDR       string `json:"cidr"`
}

type KafkaVpc struct {
	VpcID   string `json:"vpcId"`
	VpcUUID string `json:"vpcUuid"`
	Name    string `json:"name"`
	CIDR    string `json:"cidr"`
}

func parseKafka(raw map[string]any) model.Asset {
	original := &Kafka{}
	meta := &model.ObjectMeta{}
	kafka := &model.Kafka{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.ClusterID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "mq", original.ClusterID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "mq"
	meta.Name = original.Name

	kafka.Version = original.Provisioned.KafkaVersion
	kafka.Status = lo.Ternary(strings.EqualFold(original.State, "active"), "running", "stopped")
	kafka.VPCID = utils.GenerateUID(ProviderID, "vpc", original.Provisioned.Vpc.VpcID)
	kafka.SubnetIDList = lo.Map(original.Provisioned.Subnets, func(item KafkaSubnet, _ int) string {
		return utils.GenerateUID(ProviderID, "subnet", item.SubnetID)
	})
	// TODO: 无EIP数据样例
	// kafka.EipIDList = lo.Map(original.Provisioned.Eips, func(item interface{}, _ int) string {
	// 	return utils.GenerateUID(ProviderID, "eip", item.(string))
	// })
	kafka.NodeNum = int(original.Provisioned.NumberOfBrokerNodes)
	kafka.NodeSpec = original.Provisioned.NodeType
	kafka.PublicIPEnabled = original.Provisioned.PublicIPEnabled
	kafka.Authentications = lo.Map(original.Provisioned.Authentications, func(item Authentication, _ int) string {
		return strings.ToLower(item.Mode)
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, kafka)
	return asset
}
