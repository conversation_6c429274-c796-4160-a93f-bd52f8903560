package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"
)

type EdgeSG struct {
	SecurityGroup EdgeSGSecurityGroup `json:"securityGroup"`
}

type EdgeSGSecurityGroup struct {
	CreatedTime string `json:"createdTime"`
	ID          string `json:"id"`
	Name        string `json:"name"`
	Rules       []EdgeSGRule `json:"rules"`
}

type EdgeSGRule struct {
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	Remark              string `json:"remark"`
	SecurityGroupRuleID string `json:"securityGroupRuleId"`
	SourceIP            string `json:"sourceIp"`
	DestIP              string `json:"destIp"`
}

func parseEdgeSG(raw map[string]any) model.Asset {
	original := &EdgeSG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.SecurityGroup.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.SecurityGroup.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.SecurityGroup.Name

	sg.Rules = utils.Map(original.SecurityGroup.Rules, func(perm EdgeSGRule) model.SGRule {
		rule := model.SGRule{}
		rule.Description = perm.Remark
		rule.Protocol = perm.Protocol
		rule.Direction = perm.Direction
		rule.Policy = "accept"

		if perm.Direction == "ingress" {
			rule.PeerCIDR = utils.UnwrapOr(perm.SourceIP, perm.SourceIP != "all", "0.0.0.0/0")
		} else {
			rule.PeerCIDR = utils.UnwrapOr(perm.DestIP, perm.DestIP != "all", "0.0.0.0/0")
		}

		if perm.PortRange == "" {
			rule.PortStart, rule.PortEnd = 1, 65535
		} else {
			ports := strings.Split(perm.PortRange, "-")
			switch len(ports) {
			case 1:
				port, _ := strconv.Atoi(ports[0])
				rule.PortStart, rule.PortEnd = port, port
			case 2:
				rule.PortStart, _ = strconv.Atoi(ports[0])
				rule.PortEnd, _ = strconv.Atoi(ports[1])
			}
		}

		return rule
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}
