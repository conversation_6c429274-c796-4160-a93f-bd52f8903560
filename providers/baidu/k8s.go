package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"fmt"
	"time"
)

type K8S struct {
	Cluster Cluster `json:"cluster"`
}

type Cluster struct {
	Spec      Spec   `json:"spec"`
	Status    Status `json:"status"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

type Spec struct {
	ClusterID              string                 `json:"clusterID"`
	ClusterName            string                 `json:"clusterName"`
	ClusterType            string                 `json:"clusterType"`
	Description            string                 `json:"description"`
	K8SVersion             string                 `json:"k8sVersion"`
	VpcID                  string                 `json:"vpcID"`
	VpcCIDR                string                 `json:"vpcCIDR"`
	Plugins                []string               `json:"plugins"`
	MasterConfig           MasterConfig           `json:"masterConfig"`
	ContainerNetworkConfig ContainerNetworkConfig `json:"containerNetworkConfig"`
	Tags                   []Tag                  `json:"tags"`
}

type ContainerNetworkConfig struct {
	Mode                 string          `json:"mode"`
	EniVPCSubnetIDs      EniVPCSubnetIDs `json:"eniVPCSubnetIDs"`
	EniSecurityGroupID   string          `json:"eniSecurityGroupID"`
	IPVersion            string          `json:"ipVersion"`
	LBServiceVPCSubnetID string          `json:"lbServiceVPCSubnetID"`
	NodePortRangeMin     int64           `json:"nodePortRangeMin"`
	NodePortRangeMax     int64           `json:"nodePortRangeMax"`
	ClusterPodCIDR       string          `json:"clusterPodCIDR"`
	ClusterIPServiceCIDR string          `json:"clusterIPServiceCIDR"`
	MaxPodsPerNode       int64           `json:"maxPodsPerNode"`
	KubeProxyMode        string          `json:"kubeProxyMode"`
}

type EniVPCSubnetIDs map[string][]string

type MasterConfig struct {
	MasterType                 string                     `json:"masterType"`
	ClusterBLBVPCSubnetID      string                     `json:"clusterBLBVPCSubnetID"`
	ManagedClusterMasterOption ManagedClusterMasterOption `json:"managedClusterMasterOption"`
}

type ManagedClusterMasterOption struct {
}

type Status struct {
	ClusterBLB   ClusterBLB `json:"clusterBLB"`
	ClusterPhase string     `json:"clusterPhase"`
	NodeNum      int64      `json:"nodeNum"`
}

type ClusterBLB struct {
	ID    string `json:"id"`
	VpcIP string `json:"vpcIP"`
	Eip   string `json:"eip"`
}

func parseK8S(raw map[string]any) (model.Asset, error) {
	original := &K8S{}
	meta := &model.ObjectMeta{}
	k8s := &model.K8S{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Cluster.Spec.ClusterID
	meta.OriginalLabels = utils.Map(original.Cluster.Spec.Tags, func(e Tag) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "kubernetes", meta.OriginalID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "kubernetes"
	meta.Name = original.Cluster.Spec.ClusterName

	k8s.Status = utils.UnwrapOr("running", original.Cluster.Status.ClusterPhase == "running", "stopped")
	k8s.EngineVersion = original.Cluster.Spec.K8SVersion
	k8s.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Cluster.Spec.VpcID)
	if original.Cluster.Spec.ContainerNetworkConfig.EniSecurityGroupID != "" {
		k8s.SGIDList = append(k8s.SGIDList, utils.GenerateUID(meta.Provider, "security-group", original.Cluster.Spec.ContainerNetworkConfig.EniSecurityGroupID))
	}
	k8s.LBIDList = []string{utils.GenerateUID(meta.Provider, "lb", original.Cluster.Status.ClusterBLB.ID)}
	for _, subnets := range original.Cluster.Spec.ContainerNetworkConfig.EniVPCSubnetIDs {
		k8s.SubnetIDList = append(k8s.SubnetIDList, utils.Map(subnets, func(e string) string { return utils.GenerateUID(meta.Provider, "subnet", e) })...)
	}

	if len(original.Cluster.Status.ClusterBLB.VpcIP) > 0 {
		k8s.PrivateEndpoint = fmt.Sprintf("https://%s:6443", original.Cluster.Status.ClusterBLB.VpcIP)
	}
	if len(original.Cluster.Status.ClusterBLB.Eip) > 0 {
		k8s.PublicEndpoint = fmt.Sprintf("https://%s:6443", original.Cluster.Status.ClusterBLB.Eip)
	}

	k8s.PodCIDRs = append(k8s.PodCIDRs, original.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR)
	k8s.ServiceCIDRs = []string{original.Cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR}

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, k8s)
	return asset, nil
}
