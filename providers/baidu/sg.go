package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
)

type SG struct {
	SecurityGroup SGSecurityGroup `json:"securityGroup"`
}

type SGSecurityGroup struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	VpcID       string `json:"vpcId"`
	Rules       []Rule `json:"rules"`
	Tags        []Tag  `json:"tags"`
	CreatedTime string `json:"createdTime"`
}

type Rule struct {
	Remark              string `json:"remark"`
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	SourceGroupID       string `json:"sourceGroupId"`
	SourceIP            string `json:"sourceIp"`
	DestGroupID         string `json:"destGroupId"`
	DestIP              string `json:"destIp"`
	SecurityGroupID     string `json:"securityGroupId"`
	SecurityGroupRuleID string `json:"securityGroupRuleId"`
}

func parseSG(raw map[string]any) model.Asset {
	original := &SG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.SecurityGroup.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.SecurityGroup.Tags, func(e Tag) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.SecurityGroup.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.SecurityGroup.Name
	meta.Description = original.SecurityGroup.Desc

	sg.IsDefault = strings.Contains(original.SecurityGroup.Name, "默认安全组") || strings.Contains(original.SecurityGroup.Name, "default")
	sg.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.SecurityGroup.VpcID)
	sg.Rules = utils.Map(original.SecurityGroup.Rules, func(perm Rule) model.SGRule {
		rule := model.SGRule{}
		rule.Description = perm.Remark
		rule.Protocol = perm.Protocol
		rule.Direction = perm.Direction
		rule.Policy = "accept"

		if perm.Direction == "ingress" {
			rule.PeerCIDR = utils.UnwrapOr(perm.SourceIP, perm.SourceIP != "all", "0.0.0.0/0")
			rule.PeerSGID = utils.UnwrapOr(perm.SourceGroupID, perm.SourceGroupID == "", utils.GenerateUID(meta.Provider, "security-group", perm.SourceGroupID))
		} else {
			rule.PeerCIDR = utils.UnwrapOr(perm.DestIP, perm.DestIP != "all", "0.0.0.0/0")
			rule.PeerSGID = utils.UnwrapOr(perm.DestGroupID, perm.DestGroupID == "", utils.GenerateUID(meta.Provider, "security-group", perm.DestGroupID))
		}

		if perm.PortRange == "" {
			rule.PortStart, rule.PortEnd = 1, 65535
		} else {
			ports := strings.Split(perm.PortRange, "-")
			switch len(ports) {
			case 1:
				port, _ := strconv.Atoi(ports[0])
				rule.PortStart, rule.PortEnd = port, port
			case 2:
				rule.PortStart, _ = strconv.Atoi(ports[0])
				rule.PortEnd, _ = strconv.Atoi(ports[1])
			}
		}

		return rule
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}

type Esg struct {
	Esg EsgClass `json:"esg"`
}

type EsgClass struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Desc        string    `json:"desc"`
	Rules       []EsgRule `json:"rules"`
	Tags        []Tag     `json:"tags"`
	CreatedTime string    `json:"createdTime"`
}

type EsgRule struct {
	EnterpriseSecurityGroupRuleID string `json:"enterpriseSecurityGroupRuleId"`
	Remark                        string `json:"remark"`
	Direction                     string `json:"direction"`
	Ethertype                     string `json:"ethertype"`
	PortRange                     string `json:"portRange"`
	Protocol                      string `json:"protocol"`
	SourceIP                      string `json:"sourceIp"`
	DestIP                        string `json:"destIp"`
	Action                        string `json:"action"`
	Priority                      int64  `json:"priority"`
	CreatedTime                   string `json:"createdTime"`
	UpdatedTime                   string `json:"updatedTime"`
}

func parseESG(raw map[string]any) model.Asset {
	original := &Esg{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Esg.ID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.Esg.Tags, func(e Tag) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.Esg.ID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.Esg.Name
	meta.Description = original.Esg.Desc

	sg.IsDefault = strings.Contains(original.Esg.Name, "默认安全组") || strings.Contains(original.Esg.Name, "default")
	sg.Rules = utils.Map(original.Esg.Rules, func(perm EsgRule) model.SGRule {
		rule := model.SGRule{}
		rule.Description = perm.Remark
		rule.Protocol = perm.Protocol
		rule.Direction = perm.Direction
		rule.Policy = "accept"

		if perm.Direction == "ingress" {
			rule.PeerCIDR = lo.Ternary(
				perm.SourceIP != "all",
				perm.SourceIP,
				lo.Ternary(strings.EqualFold(perm.Ethertype, "ipv4"), "0.0.0.0/0", "::/0"),
			)
		} else {
			rule.PeerCIDR = lo.Ternary(
				perm.DestIP != "all",
				perm.DestIP,
				lo.Ternary(strings.EqualFold(perm.Ethertype, "ipv4"), "0.0.0.0/0", "::/0"),
			)
		}

		if perm.PortRange == "" {
			rule.PortStart, rule.PortEnd = 1, 65535
		} else {
			ports := strings.Split(perm.PortRange, "-")
			switch len(ports) {
			case 1:
				port, _ := strconv.Atoi(ports[0])
				rule.PortStart, rule.PortEnd = port, port
			case 2:
				rule.PortStart, _ = strconv.Atoi(ports[0])
				rule.PortEnd, _ = strconv.Atoi(ports[1])
			}
		}

		return rule
	})

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}
