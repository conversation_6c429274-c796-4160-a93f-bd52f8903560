package baidu

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type EdgeSubnet struct {
	ACL    []EdgeSubnetACL `json:"acls"`
	Subnet EdgeSubnetClass `json:"subnet"`
}

type EdgeSubnetACL struct {
	SubnetID   string        `json:"subnetId"`
	SubnetName string        `json:"subnetName"`
	SubnetCIDR string        `json:"subnetCidr"`
	ACLRules   []EdgeACLRule `json:"aclRules"`
}

type EdgeACLRule struct {
	SubnetID             string `json:"subnetId"`
	Description          string `json:"description"`
	Protocol             string `json:"protocol"`
	SourceIPAddress      string `json:"sourceIpAddress"`
	DestinationIPAddress string `json:"destinationIpAddress"`
	SourcePort           string `json:"sourcePort"`
	DestinationPort      string `json:"destinationPort"`
	Position             int    `json:"position"`
	Direction            string `json:"direction"`
	Action               string `json:"action"`
}

type EdgeSubnetClass struct {
	SubnetID    string `json:"subnetId"`
	Name        string `json:"name"`
	CIDR        string `json:"cidr"`
	VpcID       string `json:"vpcId"`
	Description string `json:"description"`
	CreatedTime string `json:"createdTime"`
	RegionID    string `json:"regionId"`
	SubnetType  string `json:"subnetType"`
	Tags        []Tags `json:"tags"`
}

func parseEdgeSubnet(raw map[string]any) model.Asset {
	original := &EdgeSubnet{}
	meta := &model.ObjectMeta{}
	subnet := &model.Subnet{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Subnet.SubnetID
	meta.OriginalLabels = utils.Map(original.Subnet.Tags, func(e Tags) model.KV { return model.KV{Key: e.TagKey, Value: e.TagValue} })
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "subnet", original.Subnet.SubnetID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "subnet"
	meta.Name = original.Subnet.Name
	meta.Description = original.Subnet.Description

	subnet.IsDefault, _ = utils.FindMap(original.Subnet.Tags, func(e Tags) (bool, bool) { return e.TagKey == "默认项目", e.TagKey == "默认项目" })
	subnet.CIDR = original.Subnet.CIDR
	subnet.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Subnet.VpcID)
	subnet.AvailableIPCount = -1
	subnet.ACL = utils.Flatten(utils.Map(original.ACL, func(acl EdgeSubnetACL) []model.ACLRule {
		return utils.Map(acl.ACLRules, func(e EdgeACLRule) model.ACLRule { return parseEdgeACL(e) })
	}))

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, subnet)
	return asset
}

func parseEdgeACL(rule EdgeACLRule) model.ACLRule {
	result := model.ACLRule{
		Name:        rule.SubnetID,
		Description: rule.Description,
		Protocol:    rule.Protocol,
		IPVersion:   "IPv4",
		Policy:      utils.UnwrapOr("accept", rule.Action == "allow", "drop"),
		Priority:    rule.Position,
		Direction:   rule.Direction,
	}
	if rule.Direction == "ingress" {
		result.PeerCIDR = parseCIDR(rule.SourceIPAddress)
		result.PortStart, result.PortEnd = parsePort(rule.SourcePort)
	} else {
		result.PeerCIDR = parseCIDR(rule.DestinationIPAddress)
		result.PortStart, result.PortEnd = parsePort(rule.DestinationPort)
	}
	return result
}
