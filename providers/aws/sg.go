package aws

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"strings"
	"time"

	"golang.org/x/exp/slices"
)

type SG struct {
	SecurityGroup SecurityGroup `json:"securityGroup"`
}

type SecurityGroup struct {
	Description         string         `json:"Description"`
	GroupID             string         `json:"GroupId"`
	GroupName           string         `json:"GroupName"`
	IPPermissions       []IPPermission `json:"IpPermissions"`
	IPPermissionsEgress []IPPermission `json:"IpPermissionsEgress"`
	OwnerID             string         `json:"OwnerId"`
	Tags                []Tag          `json:"Tags"`
	VpcID               string         `json:"VpcId"`
}

type IPPermission struct {
	FromPort         int           `json:"FromPort"`
	IPProtocol       string        `json:"IpProtocol"`
	IPRanges         []IPRange     `json:"IpRanges"`
	Ipv6Ranges       []interface{} `json:"Ipv6Ranges"`
	PrefixListIDS    []interface{} `json:"PrefixListIds"`
	ToPort           int           `json:"ToPort"`
	UserIDGroupPairs []interface{} `json:"UserIdGroupPairs"`
}

type IPRange struct {
	CIDRIP      string `json:"CidrIp"`
	Description string `json:"Description"`
}

type Tag struct {
	Key   string `json:"Key"`
	Value string `json:"Value"`
}

func parseSG(raw map[string]any) model.Asset {
	original := &SG{}
	meta := &model.ObjectMeta{}
	sg := &model.SG{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.SecurityGroup.GroupID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.OriginalLabels = utils.Map(original.SecurityGroup.Tags, func(e Tag) model.KV { return model.KV{Key: e.Key, Value: e.Value} })
	meta.UID = utils.GenerateUID(meta.Provider, "security-group", original.SecurityGroup.GroupID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "security-group"
	meta.Name = original.SecurityGroup.GroupName
	meta.Description = original.SecurityGroup.Description

	sg.IsDefault = strings.EqualFold(original.SecurityGroup.GroupName, "default")
	sg.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.SecurityGroup.VpcID)
	sg.Rules = append(sg.Rules, mapRule(original.SecurityGroup.IPPermissions, "ingress")...)
	sg.Rules = append(sg.Rules, mapRule(original.SecurityGroup.IPPermissionsEgress, "egress")...)

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, sg)
	return asset
}

func mapRule(perms []IPPermission, direction string) []model.SGRule {
	results := []model.SGRule{}
	for _, perm := range perms {
		rules := make([]model.SGRule, len(perm.IPRanges))
		for i, ipRange := range perm.IPRanges {
			rules[i].Description = ipRange.Description
			rules[i].Protocol = utils.UnwrapOr(perm.IPProtocol, perm.IPProtocol != "-1", "all")
			rules[i].Policy = "accept"
			rules[i].Direction = direction
			rules[i].PeerCIDR = ipRange.CIDRIP

			if slices.Contains([]string{"icmp", "icmpv6", "-1"}, perm.IPProtocol) {
				rules[i].PortStart = 0
			} else {
				rules[i].PortStart = perm.FromPort
			}
			if slices.Contains([]string{"icmp", "icmpv6", "-1"}, perm.IPProtocol) {
				rules[i].PortEnd = 65535
			} else {
				rules[i].PortEnd = perm.ToPort
			}
		}
		results = append(results, rules...)
	}
	return results
}
