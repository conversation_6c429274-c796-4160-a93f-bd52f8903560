package aws

import (
	"AssetStandardizer/model"
	"AssetStandardizer/providers/utils"
	"time"
)

type AwsSubnet struct {
	ACL    []SubnetACL `json:"acl"`
	Subnet SubnetClass `json:"subnet"`
}

type SubnetACL struct {
	Associations []SubnetAssociation `json:"Associations"`
	Entries      []Entry             `json:"Entries"`
	IsDefault    bool                `json:"IsDefault"`
	NetworkACLID string              `json:"NetworkAclId"`
	OwnerID      string              `json:"OwnerId"`
	Tags         []interface{}       `json:"Tags"`
	VpcID        string              `json:"VpcId"`
}

type SubnetAssociation struct {
	NetworkACLAssociationID string `json:"NetworkAclAssociationId"`
	NetworkACLID            string `json:"NetworkAclId"`
	SubnetID                string `json:"SubnetId"`
}

type Entry struct {
	CIDRBlock     string      `json:"CidrBlock"`
	Egress        bool        `json:"Egress"`
	ICMPTypeCode  interface{} `json:"IcmpTypeCode"`
	Ipv6CIDRBlock string      `json:"Ipv6CidrBlock"`
	PortRange     PortRange   `json:"PortRange"`
	Protocol      string      `json:"Protocol"`
	RuleAction    string      `json:"RuleAction"`
	RuleNumber    int         `json:"RuleNumber"`
}

type PortRange struct {
	From int `json:"from"`
	To   int `json:"to"`
}

type SubnetClass struct {
	AssignIpv6AddressOnCreation   bool                          `json:"AssignIpv6AddressOnCreation"`
	AvailabilityZone              string                        `json:"AvailabilityZone"`
	AvailabilityZoneID            string                        `json:"AvailabilityZoneId"`
	AvailableIPAddressCount       int                           `json:"AvailableIpAddressCount"`
	CIDRBlock                     string                        `json:"CidrBlock"`
	CustomerOwnedIpv4Pool         interface{}                   `json:"CustomerOwnedIpv4Pool"`
	DefaultForAz                  bool                          `json:"DefaultForAz"`
	EnableDns64                   bool                          `json:"EnableDns64"`
	EnableLniAtDeviceIndex        interface{}                   `json:"EnableLniAtDeviceIndex"`
	Ipv6CIDRBlockAssociationSet   []Ipv6CIDRBlockAssociationSet `json:"Ipv6CidrBlockAssociationSet"`
	Ipv6Native                    bool                          `json:"Ipv6Native"`
	MapCustomerOwnedIPOnLaunch    interface{}                   `json:"MapCustomerOwnedIpOnLaunch"`
	MapPublicIPOnLaunch           bool                          `json:"MapPublicIpOnLaunch"`
	OutpostArn                    interface{}                   `json:"OutpostArn"`
	OwnerID                       string                        `json:"OwnerId"`
	PrivateDNSNameOptionsOnLaunch PrivateDNSNameOptionsOnLaunch `json:"PrivateDnsNameOptionsOnLaunch"`
	State                         string                        `json:"State"`
	SubnetArn                     string                        `json:"SubnetArn"`
	SubnetID                      string                        `json:"SubnetId"`
	Tags                          []Tags                        `json:"Tags"`
	VpcID                         string                        `json:"VpcId"`
}

func parseSubnet(raw map[string]any) model.Asset {
	original := &AwsSubnet{}
	meta := &model.ObjectMeta{}
	subnet := &model.Subnet{}
	utils.Decode(raw["raw_log"], original)

	meta.Provider = ProviderID
	meta.OriginalID = original.Subnet.SubnetID
	meta.OriginalObject = utils.ReadFieldAny(raw, "raw_log")
	meta.UID = utils.GenerateUID(meta.Provider, "subnet", original.Subnet.SubnetID)
	meta.Region = utils.ReadFieldString(raw, "region")
	meta.UpdateTime = time.Now().UnixMilli()
	meta.Kind = "subnet"
	meta.Name, _ = utils.FindMap(original.Subnet.Tags, func(e Tags) (string, bool) { return e.Value, e.Key == "Name" })

	subnet.IsDefault = original.Subnet.DefaultForAz
	subnet.CIDR = original.Subnet.CIDRBlock
	subnet.VPCID = utils.GenerateUID(meta.Provider, "vpc", original.Subnet.VpcID)
	subnet.AvailableIPCount = original.Subnet.AvailableIPAddressCount
	subnet.ACL = utils.Flatten(utils.Map(original.ACL, func(e SubnetACL) []model.ACLRule { return parseACL(e.Entries) }))

	asset := model.Asset{}
	asset = utils.MergeAsset(asset, meta)
	asset = utils.MergeAsset(asset, subnet)
	return asset
}

func parseACL(entries []Entry) []model.ACLRule {
	rules := make([]model.ACLRule, 0, len(entries))

	for _, e := range entries {
		rules = append(rules, model.ACLRule{
			Protocol:  utils.UnwrapOr(e.Protocol, e.Protocol != "-1", "all"),
			IPVersion: utils.UnwrapOr("IPv4", e.Ipv6CIDRBlock == "", "IPv6"),
			Policy:    utils.UnwrapOr("accept", e.RuleAction == "allow", "drop"),
			Priority:  e.RuleNumber,
			Direction: utils.UnwrapOr("egress", e.Egress, "ingress"),
			PortStart: utils.UnwrapOr(e.PortRange.From, e.PortRange.From > 0, 1),
			PortEnd:   utils.UnwrapOr(e.PortRange.To, e.PortRange.To > 0, 65535),
			PeerCIDR:  utils.UnwrapOr(e.CIDRBlock, e.Ipv6CIDRBlock == "", e.Ipv6CIDRBlock),
		})
	}
	return rules
}
